import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:mistoer/models/order.dart';
import 'package:mistoer/models/order_detail.dart';
import 'dart:convert';
import 'package:crypto/crypto.dart';

class DatabaseService {
  static Database? _database;

  // 初始化数据库
  static Future<void> init() async {
    if (_database != null) return;

    try {
      _database = await openDatabase(
        join(await getDatabasesPath(), 'wktech.db'),
        version: 13, // 数据库版本从12增加到13（新增 label_58x40_mode 列）
        onCreate: (db, version) async {
          // 创建 orders 表
          await db.execute('''
            CREATE TABLE orders (
              id INTEGER PRIMARY KEY,
              pedidoKey TEXT,
              riqi TEXT,
              name TEXT,
              amount REAL,
              note TEXT
            )
          ''');

          // 创建 orderDetails 表
          await db.execute('''
            CREATE TABLE orderDetails (
              id INTEGER PRIMARY KEY,
              pedidoKey TEXT,
              codigo TEXT,
              bian<PERSON> TEXT,
              zongshuliang INTEGER,
              baozhuangshu INTEGER,
              scan INTEGER DEFAULT 0,
              finish INTEGER DEFAULT 0,
              weizhi TEXT,
              name_ce TEXT,
              artId TEXT,
              hash TEXT
            )
          ''');

          // 创建 settings 表
          await db.execute('''
            CREATE TABLE IF NOT EXISTS settings (
              key TEXT PRIMARY KEY,
              value TEXT
            )
          ''');

          // 创建 empleados 表
          await db.execute('''
            CREATE TABLE IF NOT EXISTS empleados (
              id INTEGER PRIMARY KEY,
              EmpleadoID INTEGER,
              Nombre TEXT UNIQUE COLLATE NOCASE,
              Contrasena TEXT,
              PoderPDA INTEGER,
              PoderMantener INTEGER,
              in_use INTEGER DEFAULT 0,
              revision TINYINT DEFAULT 0,
              Revisor TINYINT DEFAULT 0,
              PoderCambioStock INTEGER DEFAULT 0,
              PoderMantenerArticulo INTEGER DEFAULT 0
            )
          ''');

          // 创建 prints 表
          await db.execute('''
            CREATE TABLE IF NOT EXISTS prints (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              store_name TEXT,
              beizhu TEXT,
              product_number TEXT,
              product_name TEXT,
              retail_price TEXT,
              member_price TEXT,
              currency_symbol TEXT,
              currency_symbol_position INTEGER,
              maxpage INTEGER DEFAULT 1,
              labe1 INTEGER DEFAULT 2,
              labe2 INTEGER DEFAULT 1,
              print_mode INTEGER DEFAULT 1,
              small_labe INTEGER DEFAULT 1,
              font INTEGER,
              decimal_places INTEGER DEFAULT 2,
              label_40x30_mode INTEGER DEFAULT 0,
              label_58x40_mode INTEGER DEFAULT 0
            )
          ''');

          // 插入默认的 prints 记录
          await db.insert(
            'prints',
            {
              'store_name': '',
              'beizhu': 'P.V.P IVA Inc',
              'product_number': '',
              'product_name': '',
              'retail_price': '',
              'member_price': '',
              'currency_symbol': '€',
              'currency_symbol_position': '-1',
              'maxpage': '1',
              'labe1': '2',
              'labe2': '1',
              'print_mode': '2',
              'small_labe': '0',
              'font': '60',
              'decimal_places': '2',
              'label_40x30_mode': '0',
              'label_58x40_mode': '0',

            },
            conflictAlgorithm: ConflictAlgorithm.replace,
          );

          // 创建 albaranproveedor 表
          await db.execute('''
            CREATE TABLE IF NOT EXISTS albaranproveedor (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              AlbaranProveedorNo INTEGER,
              ArticuloID VARCHAR(20),
              CodigoBarra VARCHAR(20),
              CodigoBarraBalanza TINYINT,
              NombreES VARCHAR(255),
              NombreCN VARCHAR(80),
              Precio DECIMAL(12,4),
              Cantidad DECIMAL(12,2),
              IVA FLOAT,
              REQ FLOAT,
              IVAID SMALLINT,
              Descuento FLOAT,
              DescuentoCambioProhibido TINYINT,
              Comentario VARCHAR(255),
              Temporal TINYINT,
              FechaCaducada DATE,
              OrdenNo INTEGER,
              Checked INTEGER DEFAULT 0,
              ImagePath TEXT
            )
          ''');

          // 创建 yginfo 表
          await db.execute('''
            CREATE TABLE IF NOT EXISTS yginfo (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              YGID TEXT
            )
          ''');

          // 创建本地盘点 stocktaking 表（与服务器字段对齐，额外 isSynced 本地字段）
          await db.execute('''
            CREATE TABLE IF NOT EXISTS stocktaking (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              stdata TEXT,
              ArticuloID TEXT,
              CodigoBarra TEXT NOT NULL,
              Weizhi TEXT,
              Stock TEXT,
              user TEXT,
              oldstock TEXT,
              isUnknown INTEGER DEFAULT 0,
              isSynced INTEGER DEFAULT 0
            )
          ''');

          // 创建本地商品 articulos 表
          await db.execute('''
            CREATE TABLE IF NOT EXISTS articulos (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              ArticuloID TEXT UNIQUE,
              CodigoBarra TEXT,
              NombreES TEXT,
              PrecioDetalle REAL,
              PrecioSocio REAL,
              PrecioMayor REAL,
              Descuento REAL
            )
          ''');
          await db.execute('CREATE INDEX IF NOT EXISTS idx_articulos_barcode ON articulos(CodigoBarra)');
          await db.execute('CREATE UNIQUE INDEX IF NOT EXISTS uq_articulos_articuloid ON articulos(ArticuloID)');

          // 预置临时商品
          await db.insert('articulos', {
            'ArticuloID': '0',
            'CodigoBarra': '',
            'NombreES': 'ARTICULO TEMPORAL',
            'PrecioDetalle': 0,
            'PrecioSocio': 0,
            'PrecioMayor': 0,
            'Descuento': 0,
          }, conflictAlgorithm: ConflictAlgorithm.ignore);
        },
        onUpgrade: (db, oldVersion, newVersion) async {
          if (oldVersion < 2) {
            var tables = await db.rawQuery("SELECT name FROM sqlite_master WHERE type='table' AND name='orderDetails'");
            if (tables.isEmpty) {
              await db.execute('''
                CREATE TABLE orderDetails (
                  id INTEGER PRIMARY KEY,
                  pedidoKey TEXT,
                  codigo TEXT,
                  bianhao TEXT,
                  zongshuliang INTEGER,
                  baozhuangshu INTEGER,
                  scan INTEGER DEFAULT 0,
                  finish INTEGER DEFAULT 0,
                  weizhi TEXT,
                  name_ce TEXT,
                  artId TEXT,
                  hash TEXT
                )
              ''');
            } else {
              await db.execute('ALTER TABLE orderDetails ADD COLUMN weizhi TEXT');
            }
          }
          if (oldVersion < 3) {
            await db.execute('''
              CREATE TABLE IF NOT EXISTS empleados (
                id INTEGER PRIMARY KEY,
                EmpleadoID INTEGER,
                Nombre TEXT UNIQUE COLLATE NOCASE,
                Contrasena TEXT,
                PoderPDA INTEGER,
                PoderMantener INTEGER,
                in_use INTEGER DEFAULT 0
              )
            ''');
          }
          if (oldVersion < 4) {
            await db.execute('ALTER TABLE orderDetails ADD COLUMN name_ce TEXT');
          }
          if (oldVersion < 5) {
            await db.execute('ALTER TABLE empleados ADD COLUMN revision TINYINT DEFAULT 0');
            await db.execute('ALTER TABLE empleados ADD COLUMN Revisor TINYINT DEFAULT 0');
          }
          if (oldVersion < 6) {
            await db.execute('ALTER TABLE empleados ADD COLUMN PoderCambioStock INTEGER DEFAULT 0');
            await db.execute('ALTER TABLE empleados ADD COLUMN PoderMantenerArticulo INTEGER DEFAULT 0');

            await db.execute('''
              CREATE TABLE IF NOT EXISTS prints (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                store_name TEXT,
                beizhu TEXT,
                product_number TEXT,
                product_name TEXT,
                retail_price TEXT,
                member_price TEXT,
                currency_symbol TEXT,
                currency_symbol_position INTEGER,
                maxpage INTEGER DEFAULT 1,
                labe1 INTEGER DEFAULT 2,
                labe2 INTEGER DEFAULT 1,
                print_mode INTEGER DEFAULT 1,
                small_labe INTEGER DEFAULT 1,
                font INTEGER,
                decimal_places INTEGER DEFAULT 2,
                label_40x30_mode INTEGER DEFAULT 0,
                label_58x40_mode INTEGER DEFAULT 0
              )
            ''');

            await db.insert(
              'prints',
              {
                'store_name': '',
                'beizhu': 'P.V.P IVA Inc',
                'product_number': '',
                'product_name': '',
                'retail_price': '',
                'member_price': '',
                'currency_symbol': '€',
                'currency_symbol_position': '-1',
                'maxpage': '1',
                'labe1': '2',
                'labe2': '1',
                'print_mode': '2',
                'small_labe': '0',
                'font': '60',
                'decimal_places': '2',
                'label_40x30_mode': '0',
                'label_58x40_mode': '0',

              },
              conflictAlgorithm: ConflictAlgorithm.replace,
            );
          }
          if (oldVersion < 7) {
            await db.execute('''
              CREATE TABLE IF NOT EXISTS albaranproveedor (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                AlbaranProveedorNo INTEGER,
                ArticuloID VARCHAR(20),
                CodigoBarra VARCHAR(20),
                CodigoBarraBalanza TINYINT,
                NombreES VARCHAR(255),
                NombreCN VARCHAR(80),
                Precio DECIMAL(12,4),
                Cantidad DECIMAL(12,2),
                IVA FLOAT,
                REQ FLOAT,
                IVAID SMALLINT,
                Descuento FLOAT,
                DescuentoCambioProhibido TINYINT,
                Comentario VARCHAR(255),
                Temporal TINYINT,
                FechaCaducada DATE,
                OrdenNo INTEGER,
                Checked INTEGER DEFAULT 0,
                ImagePath TEXT
              )
            ''');
          }
          if (oldVersion < 8) {
            await db.execute('''
              CREATE TABLE IF NOT EXISTS yginfo (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                YGID TEXT
              )
            ''');
          }
          if (oldVersion < 9) {
            var printsTableInfo = await db.rawQuery('PRAGMA table_info(prints)');
            bool hasLabel40x30ModeColumn = printsTableInfo.any((column) => column['name'] == 'label_40x30_mode');
            if (!hasLabel40x30ModeColumn) {
              await db.execute('ALTER TABLE prints ADD COLUMN label_40x30_mode INTEGER DEFAULT 0');
            }
          }
          if (oldVersion < 10) {
            await db.execute('''
              CREATE TABLE IF NOT EXISTS stocktaking (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT,
                warehouse TEXT,
                barcode TEXT,
                articleId TEXT,
                stock INTEGER,
                isUnknown INTEGER DEFAULT 0,
                oldStock TEXT,
                duplicateWarehouses TEXT,
                inUse INTEGER,
                isSynced INTEGER DEFAULT 0,
                createdAt INTEGER,
                updatedAt INTEGER
              )
            ''');
          }
          if (oldVersion < 11) {
            final cols = await db.rawQuery('PRAGMA table_info(stocktaking)');
            final has = (String name) => cols.any((c) => (c['name'] as String) == name);
            if (!has('stdata')) await db.execute('ALTER TABLE stocktaking ADD COLUMN stdata TEXT');
            if (!has('ArticuloID')) await db.execute('ALTER TABLE stocktaking ADD COLUMN ArticuloID TEXT');
            if (!has('CodigoBarra')) await db.execute('ALTER TABLE stocktaking ADD COLUMN CodigoBarra TEXT');
            if (!has('Weizhi')) await db.execute('ALTER TABLE stocktaking ADD COLUMN Weizhi TEXT');
            if (!has('Stock')) await db.execute('ALTER TABLE stocktaking ADD COLUMN Stock TEXT');
            if (!has('user')) await db.execute('ALTER TABLE stocktaking ADD COLUMN user TEXT');
            if (!has('oldstock')) await db.execute('ALTER TABLE stocktaking ADD COLUMN oldstock TEXT');
            if (!has('isUnknown')) await db.execute('ALTER TABLE stocktaking ADD COLUMN isUnknown INTEGER DEFAULT 0');
            if (!has('isSynced')) await db.execute('ALTER TABLE stocktaking ADD COLUMN isSynced INTEGER DEFAULT 0');
          }
          if (oldVersion < 12) {
            // 创建 articulos 表及索引
            await db.execute('''
              CREATE TABLE IF NOT EXISTS articulos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ArticuloID TEXT UNIQUE,
                CodigoBarra TEXT,
                NombreES TEXT,
                PrecioDetalle REAL,
                PrecioSocio REAL,
                PrecioMayor REAL,
                Descuento REAL
              )
            ''');
            await db.execute('CREATE INDEX IF NOT EXISTS idx_articulos_barcode ON articulos(CodigoBarra)');
            await db.execute('CREATE UNIQUE INDEX IF NOT EXISTS uq_articulos_articuloid ON articulos(ArticuloID)');

            // 预置临时商品
            await db.insert('articulos', {
              'ArticuloID': '0',
              'CodigoBarra': '',
              'NombreES': 'ARTICULO TEMPORAL',
              'PrecioDetalle': 0,
              'PrecioSocio': 0,
              'PrecioMayor': 0,
              'Descuento': 0,
            }, conflictAlgorithm: ConflictAlgorithm.ignore);
          }
          if (oldVersion < 13) {
            // 添加 label_58x40_mode 列
            var printsTableInfo = await db.rawQuery('PRAGMA table_info(prints)');
            bool hasLabel58x40ModeColumn = printsTableInfo.any((column) => column['name'] == 'label_58x40_mode');
            if (!hasLabel58x40ModeColumn) {
              await db.execute('ALTER TABLE prints ADD COLUMN label_58x40_mode INTEGER DEFAULT 0');
            }
          }
        },
        readOnly: false, // 确保数据库以读写模式打开
      );

      await _checkAndAddColumns(); // 确保所有必要的列都存在

      // 强制检查并添加 label_58x40_mode 列（临时修复）
      try {
        var printsTableInfo = await _database!.rawQuery('PRAGMA table_info(prints)');
        bool hasLabel58x40ModeColumn = printsTableInfo.any((column) => column['name'] == 'label_58x40_mode');
        if (!hasLabel58x40ModeColumn) {
          print('Adding missing label_58x40_mode column...');
          await _database!.execute('ALTER TABLE prints ADD COLUMN label_58x40_mode INTEGER DEFAULT 0');
          print('Successfully added label_58x40_mode column');
        }
      } catch (e) {
        print('Error checking/adding label_58x40_mode column: $e');
      }

    } catch (e) {
      print('Error initializing database: $e');
    }
  }

  // 获取数据库实例
  static Future<Database> get database async {
    if (_database != null) return _database!;
    await init();
    return _database!;
  }

  // 检查并添加缺失的列
  static Future<void> _checkAndAddColumns() async {
    final db = await database;

    try {
      // 检查 orderDetails 表是否存在
      var tables = await db.rawQuery("SELECT name FROM sqlite_master WHERE type='table' AND name='orderDetails'");
      if (tables.isEmpty) {
        await db.execute('''
          CREATE TABLE orderDetails (
            id INTEGER PRIMARY KEY,
            pedidoKey TEXT,
            codigo TEXT,
            bianhao TEXT,
            zongshuliang INTEGER,
            baozhuangshu INTEGER,
            scan INTEGER DEFAULT 0,
            finish INTEGER DEFAULT 0,
            weizhi TEXT,
            name_ce TEXT,
            artId TEXT,
            hash TEXT
          )
        ''');
      } else {
        var tableInfo = await db.rawQuery('PRAGMA table_info(orderDetails)');
        bool hasWeizhiColumn = tableInfo.any((column) => column['name'] == 'weizhi');
        bool hasNameCeColumn = tableInfo.any((column) => column['name'] == 'name_ce');

        if (!hasWeizhiColumn) {
          await db.execute('ALTER TABLE orderDetails ADD COLUMN weizhi TEXT');
        }

        if (!hasNameCeColumn) {
          await db.execute('ALTER TABLE orderDetails ADD COLUMN name_ce TEXT');
        }
      }

      // 检查 empleados 表是否存在
      tables = await db.rawQuery("SELECT name FROM sqlite_master WHERE type='table' AND name='empleados'");
      if (tables.isEmpty) {
        await db.execute('''
          CREATE TABLE empleados (
            id INTEGER PRIMARY KEY,
            EmpleadoID INTEGER,
            Nombre TEXT UNIQUE COLLATE NOCASE,
            Contrasena TEXT,
            PoderPDA INTEGER,
            PoderMantener INTEGER,
            in_use INTEGER DEFAULT 0,
            revision TINYINT DEFAULT 0,
            Revisor TINYINT DEFAULT 0,
            PoderCambioStock INTEGER DEFAULT 0,
            PoderMantenerArticulo INTEGER DEFAULT 0
          )
        ''');
      } else {
        var empleadosTableInfo = await db.rawQuery('PRAGMA table_info(empleados)');
        bool hasRevisionColumn = empleadosTableInfo.any((column) => column['name'] == 'revision');
        bool hasRevisorColumn = empleadosTableInfo.any((column) => column['name'] == 'Revisor');

        if (!hasRevisionColumn) {
          await db.execute('ALTER TABLE empleados ADD COLUMN revision TINYINT DEFAULT 0');
        }

        if (!hasRevisorColumn) {
          await db.execute('ALTER TABLE empleados ADD COLUMN Revisor TINYINT DEFAULT 0');
        }
      }

      // 检查 prints 表是否存在
      tables = await db.rawQuery("SELECT name FROM sqlite_master WHERE type='table' AND name='prints'");
      if (tables.isEmpty) {
        await db.execute('''
          CREATE TABLE prints (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            store_name TEXT,
            beizhu TEXT,
            product_number TEXT,
            product_name TEXT,
            retail_price TEXT,
            member_price TEXT,
            currency_symbol TEXT,
            currency_symbol_position INTEGER,
            maxpage INTEGER DEFAULT 1,
            labe1 INTEGER DEFAULT 2,
            labe2 INTEGER DEFAULT 1,
            print_mode INTEGER DEFAULT 1,
            small_labe INTEGER DEFAULT 1,
            font INTEGER,
            decimal_places INTEGER DEFAULT 2,
            label_40x30_mode INTEGER DEFAULT 0,
            label_58x40_mode INTEGER DEFAULT 0
          )
        ''');

        await db.insert(
          'prints',
          {
            'store_name': '',
            'beizhu': 'P.V.P IVA Inc',
            'product_number': '',
            'product_name': '',
            'retail_price': '',
            'member_price': '',
            'currency_symbol': '€',
            'currency_symbol_position': '-1',
            'maxpage': '1',
            'labe1': '2',
            'labe2': '1',
            'print_mode': '2',
            'small_labe': '0',
            'font': '60',
            'decimal_places': '2',
            'label_40x30_mode': '0',
            'label_58x40_mode': '0',

          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      } else {
        var printsTableInfo = await db.rawQuery('PRAGMA table_info(prints)');
        bool hasLabel40x30ModeColumn = printsTableInfo.any((column) => column['name'] == 'label_40x30_mode');
        bool hasLabel58x40ModeColumn = printsTableInfo.any((column) => column['name'] == 'label_58x40_mode');

        if (!hasLabel40x30ModeColumn) {
          await db.execute('ALTER TABLE prints ADD COLUMN label_40x30_mode INTEGER DEFAULT 0');
        }
        if (!hasLabel58x40ModeColumn) {
          await db.execute('ALTER TABLE prints ADD COLUMN label_58x40_mode INTEGER DEFAULT 0');
        }

      }

      // 确保 stocktaking 表存在并含有服务器字段
      var stockCols = await db.rawQuery('PRAGMA table_info(stocktaking)');
      if (stockCols.isEmpty) {
        await db.execute('''
          CREATE TABLE IF NOT EXISTS stocktaking (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            stdata TEXT,
            ArticuloID TEXT,
            CodigoBarra TEXT NOT NULL,
            Weizhi TEXT,
            Stock TEXT,
            user TEXT,
            oldstock TEXT,
            isUnknown INTEGER DEFAULT 0,
            isSynced INTEGER DEFAULT 0
          )
        ''');
      } else {
        final has = (String name) => stockCols.any((c) => (c['name'] as String) == name);
        if (!has('stdata')) await db.execute('ALTER TABLE stocktaking ADD COLUMN stdata TEXT');
        if (!has('ArticuloID')) await db.execute('ALTER TABLE stocktaking ADD COLUMN ArticuloID TEXT');
        if (!has('CodigoBarra')) await db.execute('ALTER TABLE stocktaking ADD COLUMN CodigoBarra TEXT');
        if (!has('Weizhi')) await db.execute('ALTER TABLE stocktaking ADD COLUMN Weizhi TEXT');
        if (!has('Stock')) await db.execute('ALTER TABLE stocktaking ADD COLUMN Stock TEXT');
        if (!has('user')) await db.execute('ALTER TABLE stocktaking ADD COLUMN user TEXT');
        if (!has('oldstock')) await db.execute('ALTER TABLE stocktaking ADD COLUMN oldstock TEXT');
        if (!has('isUnknown')) await db.execute('ALTER TABLE stocktaking ADD COLUMN isUnknown INTEGER DEFAULT 0');
        if (!has('isSynced')) await db.execute('ALTER TABLE stocktaking ADD COLUMN isSynced INTEGER DEFAULT 0');
      }

      // 确保 articulos 表存在
      var artCols = await db.rawQuery('PRAGMA table_info(articulos)');
      if (artCols.isEmpty) {
        await db.execute('''
          CREATE TABLE IF NOT EXISTS articulos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ArticuloID TEXT UNIQUE,
            CodigoBarra TEXT,
            NombreES TEXT,
            PrecioDetalle REAL,
            PrecioSocio REAL,
            PrecioMayor REAL,
            Descuento REAL
          )
        ''');
        await db.execute('CREATE INDEX IF NOT EXISTS idx_articulos_barcode ON articulos(CodigoBarra)');
        await db.execute('CREATE UNIQUE INDEX IF NOT EXISTS uq_articulos_articuloid ON articulos(ArticuloID)');
      }

    } catch (e) {
      print('Error checking and adding columns: $e');
    }
  }

  // 以下是您原有的所有方法，功能保持不变

  // 获取 prints 表中的字段值
  static Future<Map<String, String>> getPrintTitles() async {
    final db = await database;

    final List<Map<String, dynamic>> results = await db.query('prints');

    if (results.isNotEmpty) {
      final Map<String, String> titles = {
        'store_name': results[0]['store_name'] ?? '',
        'beizhu': results[0]['beizhu'] ?? 'P.V.P IVA Inc',
        'product_number': results[0]['product_number'] ?? '',
        'product_name': results[0]['product_name'] ?? '',
        'retail_price': results[0]['retail_price'] ?? '',
        'member_price': results[0]['member_price'] ?? '',
        'currency_symbol': results[0]['currency_symbol'] ?? '',
        'currency_symbol_position': results[0]['currency_symbol_position'].toString(),
        'maxpage': results[0]['maxpage'].toString(),
        'labe1': results[0]['labe1'].toString(),
        'labe2': results[0]['labe2'].toString(),
        'print_mode': results[0]['print_mode'].toString(),
        'small_labe': results[0]['small_labe'].toString(),
        'font': results[0]['font'].toString(),
        'decimal_places': results[0]['decimal_places'].toString(),
        'label_40x30_mode': results[0]['label_40x30_mode'].toString(),
        'label_58x40_mode': results[0]['label_58x40_mode']?.toString() ?? '0',

      };
      return titles;
    } else {
      return {
        'store_name': '',
        'beizhu': 'P.V.P IVA Inc',
        'product_number': '',
        'product_name': '',
        'retail_price': '',
        'member_price': '',
        'currency_symbol': '€',
        'currency_symbol_position': '-1',
        'maxpage': '1',
        'labe1': '2',
        'labe2': '1',
        'print_mode': '2',
        'small_labe': '0',
        'font': '60',
        'decimal_places': '2',
        'label_40x30_mode': '0',
        'label_58x40_mode': '0',

      };
    }
  }

  // 更新 prints 记录
  static Future<void> upsertPrintsRecord(Map<String, dynamic> newRecord) async {
    final db = await database;

    try {
      // 临时修复：检查并添加 label_58x40_mode 列
      try {
        var printsTableInfo = await db.rawQuery('PRAGMA table_info(prints)');
        bool hasLabel58x40ModeColumn = printsTableInfo.any((column) => column['name'] == 'label_58x40_mode');
        if (!hasLabel58x40ModeColumn) {
          print('Adding missing label_58x40_mode column...');
          await db.execute('ALTER TABLE prints ADD COLUMN label_58x40_mode INTEGER DEFAULT 0');
          print('Successfully added label_58x40_mode column');
        }
      } catch (columnError) {
        print('Error checking/adding label_58x40_mode column: $columnError');
      }

      await db.delete(
        'prints',
        where: 'id = ?',
        whereArgs: [1],
      );

      await db.insert(
        'prints',
        {
          'id': 1,
          ...newRecord,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      print('Error upserting prints record: $e');
    }
  }

  // 获取当前用户的 PoderCambioStock 值
  static Future<int?> getPoderCambioStock() async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> result = await db.query(
        'empleados',
        columns: ['PoderCambioStock'],
        where: 'in_use = ?',
        whereArgs: [-1],
      );

      if (result.isNotEmpty) {
        return result.first['PoderCambioStock'] as int?;
      }
      return null;
    } catch (e) {
      print('Error getting PoderCambioStock: $e');
      return null;
    }
  }

  // 获取当前用户的 PoderMantenerArticulo 值
  static Future<int?> getPoderMantenerArticulo() async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> result = await db.query(
        'empleados',
        columns: ['PoderMantenerArticulo'],
        where: 'in_use = ?',
        whereArgs: [-1],
      );

      if (result.isNotEmpty) {
        return result.first['PoderMantenerArticulo'] as int?;
      }
      return null;
    } catch (e) {
      print('Error getting PoderMantenerArticulo: $e');
      return null;
    }
  }

  // 删除数据库
  static Future<void> deleteDatabase() async {
    final path = join(await getDatabasesPath(), 'wktech.db');
    await databaseFactory.deleteDatabase(path);
  }

  // 存储服务器 IP
  static Future<void> setIp(String ip) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('server_ip', ip);
  }

  // 获取服务器 IP
  static Future<String?> getIp() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('server_ip');
  }

  // 插入订单
  static Future<void> insertOrder(Map<String, dynamic> order) async {
    final db = await database;
    try {
      await db.insert('orders', order, conflictAlgorithm: ConflictAlgorithm.replace);
    } catch (e) {
      print('Error inserting order: $e');
    }
  }

  // 获取零包装订单
  static Future<List<OrderDetail>> getZeroPackageOrders() async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> maps = await db.query(
        'orderDetails',
        where: 'baozhuangshu = ?',
        whereArgs: [0],
      );

      return List.generate(maps.length, (i) {
        return OrderDetail.fromJson(maps[i]);
      });
    } catch (e) {
      print('Error getting zero package orders: $e');
      return [];
    }
  }

  // 插入订单详情
  static Future<void> insertOrderDetail(OrderDetail orderDetail) async {
    final db = await database;
    try {
      var exists = await db.query(
        'orderDetails',
        where: 'pedidoKey = ? AND codigo = ? AND bianhao = ?',
        whereArgs: [
          orderDetail.pedidoKey,
          orderDetail.codigo,
          orderDetail.bianhao,
        ],
      );

      if (exists.isEmpty) {
        await db.insert('orderDetails', orderDetail.toMap(), conflictAlgorithm: ConflictAlgorithm.replace);
      }
    } catch (e) {
      print('Error inserting order detail: $e');
    }
  }

  // 获取订单详情
  static Future<List<OrderDetail>> getOrderDetails(String pedidoKey) async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> maps = await db.query(
        'orderDetails',
        where: 'pedidoKey = ?',
        whereArgs: [pedidoKey],
      );

      return List.generate(maps.length, (i) {
        return OrderDetail.fromJson(maps[i]);
      });
    } catch (e) {
      print('Error getting order details: $e');
      return [];
    }
  }

  // 获取缺货商品
  static Future<List<OrderDetail>> getOutOfStockItems(String pedidoKey) async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> maps = await db.query(
        'orderDetails',
        where: 'pedidoKey = ? AND finish = 0 AND scan = 0',
        whereArgs: [pedidoKey],
      );

      return List.generate(maps.length, (i) {
        return OrderDetail.fromJson(maps[i]);
      });
    } catch (e) {
      print('Error getting out of stock items: $e');
      return [];
    }
  }

  // 获取缺货商品数量
  static Future<int> getOutOfStockItemCount(String pedidoKey) async {
    final db = await database;
    try {
      final result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM orderDetails WHERE pedidoKey = ? AND finish = 0 AND scan = 0',
        [pedidoKey],
      );
      return Sqflite.firstIntValue(result) ?? 0;
    } catch (e) {
      print('Error getting out of stock item count: $e');
      return 0;
    }
  }

  // 根据状态获取订单
  static Future<List<Order>> getOrdersByStatus(int zhuangtai) async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> maps = await db.query(
        'orders',
        where: 'zhuangtai = ?',
        whereArgs: [zhuangtai],
      );

      return List.generate(maps.length, (i) {
        return Order.fromJson(maps[i]);
      });
    } catch (e) {
      print('Error getting orders by status: $e');
      return [];
    }
  }

  // 更新订单详情扫描状态
  static Future<void> updateOrderDetailScan(OrderDetail orderDetail) async {
    final db = await database;
    try {
      await db.update('orderDetails', orderDetail.toMap(), where: 'id = ?', whereArgs: [orderDetail.id]);
    } catch (e) {
      print('Error updating order detail scan: $e');
    }
  }

  // 更新订单详情的中包数（baozhuangshu）
  static Future<void> updateOrderDetailBaozhuangshu(int id, int baozhuangshu) async {
    final db = await database;
    try {
      await db.update(
        'orderDetails',
        {'baozhuangshu': baozhuangshu},
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      print('Error updating order detail baozhuangshu: $e');
    }
  }

  // 更新订单完成状态
  static Future<void> updateOrderFinishStatus(String pedidoKey) async {
    final db = await database;
    try {
      await db.update('orderDetails', {'finish': -1}, where: 'pedidoKey = ?', whereArgs: [pedidoKey]);
    } catch (e) {
      print('Error updating order finish status: $e');
    }
  }

  // 获取用户名列表
  static Future<List<Map<String, dynamic>>> getUsernames() async {
    final db = await database;
    try {
      return await db.query('empleados', columns: ['Nombre']);
    } catch (e) {
      print('Error getting usernames: $e');
      return [];
    }
  }

  // 获取订单完成率
  static Future<double> getOrderFulfillmentRate(String pedidoKey) async {
    final Database db = await database;

    try {
      final List<Map<String, dynamic>> result = await db.rawQuery(
        'SELECT SUM(scan) AS totalScan, SUM(zongshuliang) AS totalZongshuliang FROM orderDetails WHERE pedidoKey = ?',
        [pedidoKey],
      );

      if (result.isNotEmpty && result.first['totalZongshuliang'] != 0) {
        double totalScan = result.first['totalScan']?.toDouble() ?? 0.0;
        double totalZongshuliang = result.first['totalZongshuliang']?.toDouble() ?? 1.0;
        return totalScan / totalZongshuliang;
      } else {
        return 0.0;
      }
    } catch (e) {
      print('Error getting order fulfillment rate: $e');
      return 0.0;
    }
  }

  // 保存用户字段
  static Future<void> saveUserFields(Map<String, dynamic> fields) async {
    final db = await database;

    try {
      fields.remove('message');

      await db.delete('empleados', where: 'EmpleadoID = ?', whereArgs: [fields['EmpleadoID']]);

      // 保护：如果服务端未提供 Contrasena，尽量保留本地已有密码，避免离线验证失败
      try {
        final dynamic nombreRaw = fields['Nombre'];
        final String? nombre = nombreRaw == null ? null : nombreRaw.toString();
        final dynamic contrasenaRaw = fields['Contrasena'];
        final String? incomingPwd = contrasenaRaw == null ? null : contrasenaRaw.toString();
        if ((incomingPwd == null || incomingPwd.isEmpty) && nombre != null && nombre.isNotEmpty) {
          final List<Map<String, dynamic>> exist = await db.query(
            'empleados',
            columns: ['Contrasena'],
            where: 'lower(Nombre) = ?',
            whereArgs: [nombre.toLowerCase()],
            limit: 1,
          );
          if (exist.isNotEmpty) {
            final String preserved = (exist.first['Contrasena'] ?? '').toString();
            if (preserved.isNotEmpty) {
              fields['Contrasena'] = preserved;
            }
          }
        }
      } catch (e) {
        // 忽略密码保留过程中的异常，继续后续插入
        print('saveUserFields password preserve warning: $e');
      }

      await db.insert('empleados', fields, conflictAlgorithm: ConflictAlgorithm.replace);
    } catch (e) {
      print('Error saving user fields: $e');
    }
  }

  // 获取当前用户的修订版本
  static Future<int> getCurrentUserRevision() async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> result = await db.query(
        'empleados',
        columns: ['revision'],
        where: 'in_use = ?',
        whereArgs: [-1],
      );

      if (result.isNotEmpty) {
        return result.first['revision'] as int? ?? 0;
      }
      return 0;
    } catch (e) {
      print('Error getting current user revision: $e');
      return 0;
    }
  }

  // 更新用户的修订版本和 Revisor
  static Future<void> updateUserRevisionAndRevisor(String username, int revision, int revisor) async {
    final db = await database;
    try {
      await db.update(
        'empleados',
        {
          'revision': revision,
          'Revisor': revisor,
        },
        where: 'lower(Nombre) = ?',
        whereArgs: [username.toLowerCase()],
      );
    } catch (e) {
      print('Error updating user revision and revisor: $e');
    }
  }

  // 获取用户的 PoderMantener
  static Future<int?> getPoderMantener(int empleadoID) async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> result = await db.query(
        'empleados',
        columns: ['PoderMantener'],
        where: 'EmpleadoID = ?',
        whereArgs: [empleadoID],
      );

      if (result.isNotEmpty) {
        return result.first['PoderMantener'];
      }
      return null;
    } catch (e) {
      print('Error getting PoderMantener: $e');
      return null;
    }
  }

  // 更新用户的 in_use 状态
  static Future<void> updateInUse(String username, int inUseValue) async {
    final db = await database;
    try {
      await db.update(
        'empleados',
        {'in_use': inUseValue},
        where: 'lower(Nombre) = ?',
        whereArgs: [username.toLowerCase()],
      );
    } catch (e) {
      print('Error updating in_use status: $e');
    }
  }

  // 更新用户密码（用于在线登录成功后，保证离线校验可用）
  static Future<void> updateUserPassword(String username, String password) async {
    final db = await database;
    try {
      final hashed = md5.convert(utf8.encode(password)).toString();
      await db.update(
        'empleados',
        // 只存 MD5
        {'Contrasena': hashed},
        where: 'lower(Nombre) = ?',
        whereArgs: [username.toLowerCase()],
      );
    } catch (e) {
      print('Error updating user password: $e');
    }
  }

  // 获取用户的 in_use 状态
  static Future<int?> getInUse(String username) async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> result = await db.query(
        'empleados',
        columns: ['in_use'],
        where: 'lower(Nombre) = ?',
        whereArgs: [username.toLowerCase()],
      );

      if (result.isNotEmpty) {
        return result.first['in_use'] as int?;
      }
      return null;
    } catch (e) {
      print('Error getting in_use status: $e');
      return null;
    }
  }

  // 根据用户名获取用户
  static Future<Map<String, dynamic>?> getUserByName(String username) async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> result = await db.query(
        'empleados',
        where: 'lower(Nombre) = ?',
        whereArgs: [username.toLowerCase()],
      );

      if (result.isNotEmpty) {
        return result.first;
      }
      return null;
    } catch (e) {
      print('Error getting user by name: $e');
      return null;
    }
  }

  // 验证用户密码
  static Future<bool> verifyUserPassword(String username, String password) async {
    final db = await database;
    try {
      // 取出本地保存的密码（MD5），仅按 MD5 比对
      final List<Map<String, dynamic>> users = await db.query(
        'empleados',
        columns: ['Contrasena'],
        where: 'lower(Nombre) = ?',
        whereArgs: [username.toLowerCase()],
        limit: 1,
      );

      if (users.isEmpty) return false;
      final stored = (users.first['Contrasena'] ?? '').toString();
      if (stored.isEmpty) return false;

      final inputMd5Lower = md5.convert(utf8.encode(password)).toString();
      return stored.toLowerCase() == inputMd5Lower;
    } catch (e) {
      print('Error verifying user password: $e');
      return false;
    }
  }

  // 获取当前用户的 in_use 状态
  static Future<int> getCurrentUserInUse() async {
    final db = await database;
    try {
      String currentUsername = await getCurrentUsername();

      var result = await db.query(
        'empleados',
        columns: ['in_use'],
        where: 'lower(Nombre) = ?',
        whereArgs: [currentUsername.toLowerCase()],
      );

      if (result.isNotEmpty) {
        return result.first['in_use'] as int? ?? -1;
      } else {
        return -1;
      }
    } catch (e) {
      print("Error getting current user in_use status: $e");
      return -1;
    }
  }

  // 获取订单详情
  static Future<OrderDetail?> getOrderDetail(int id) async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> maps = await db.query(
        'orderDetails',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return OrderDetail.fromJson(maps.first);
      } else {
        return null;
      }
    } catch (e) {
      print('Error getting order detail: $e');
      return null;
    }
  }

  // 获取当前用户名
  static Future<String> getCurrentUsername() async {
    // 实现获取当前用户用户名的逻辑
    return "exampleUsername";
  }

  // 插入新用户
  static Future<void> insertNewUser(String username, String password) async {
    final db = await database;
    try {
      final existingUser = await getUserByName(username);

      if (existingUser == null) {
        final hashed = md5.convert(utf8.encode(password)).toString();
        await db.insert(
          'empleados',
          {
            'Nombre': username,
            // 只存 MD5
            'Contrasena': hashed,
            'PoderPDA': 0,
            'PoderMantener': 0,
            'in_use': -1
          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
        print('Inserted new user with username: $username');
      } else {
        final hashed = md5.convert(utf8.encode(password)).toString();
        await db.update(
          'empleados',
          {
            // 只存 MD5
            'Contrasena': hashed,
            'PoderPDA': 0,
            'PoderMantener': 0,
            'in_use': -1
          },
          where: 'lower(Nombre) = ?',
          whereArgs: [username.toLowerCase()],
        );
        print('Updated existing user with username: $username');
      }
    } catch (e) {
      print('Error inserting/updating user: $e');
    }
  }

  // 获取具有特定 in_use 值的用户
  static Future<List<Map<String, dynamic>>> getUserWithInUseValue(int inUseValue) async {
    final db = await database;
    try {
      return await db.query(
        'empleados',
        where: 'in_use = ?',
        whereArgs: [inUseValue],
      );
    } catch (e) {
      print('Error getting users with in_use value: $e');
      return [];
    }
  }

  // 删除用户名
  static Future<void> deleteUsername(String username) async {
    final db = await database;
    try {
      await db.delete('empleados', where: 'lower(Nombre) = ?', whereArgs: [username.toLowerCase()]);
    } catch (e) {
      print('Error deleting username: $e');
    }
  }

  // 保存按钮顺序
  static Future<void> saveButtonOrder(String username, List<String> order) async {
    final db = await database;
    try {
      await db.insert(
        'settings',
        {'key': 'button_order_$username', 'value': order.join(',')},
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      print('Error saving button order: $e');
    }
  }

  // 加载按钮顺序
  static Future<List<String>> loadButtonOrder(String username) async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> result = await db.query(
        'settings',
        where: 'key = ?',
        whereArgs: ['button_order_$username'],
      );

      if (result.isNotEmpty) {
        return (result.first['value'] as String).split(',');
      }
      return [];
    } catch (e) {
      print('Error loading button order: $e');
      return [];
    }
  }

  // 获取本地订单行数
  static Future<int> getLocalOrderLineCount(String pedidoKey) async {
    final db = await database;
    final countResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM orderDetails WHERE pedidoKey = ?',
      [pedidoKey],
    );
    return Sqflite.firstIntValue(countResult) ?? 0;
  }

  // 删除订单详情
  static Future<void> deleteOrderDetails(String pedidoKey) async {
    final db = await database;
    await db.delete('orderDetails', where: 'pedidoKey = ?', whereArgs: [pedidoKey]);
  }

  // 保存用户偏好
  static Future<void> saveUserPreference(String key, bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(key, value);
  }

  // 加载用户偏好
  static Future<bool> loadUserPreference(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(key) ?? false;
  }

  // 插入数据到本地数据库
  static Future<void> saveAlbaranDataToLocal(Map<String, dynamic> data) async {
    final db = await database;

    await db.insert(
      'albaranproveedor',
      data,
      conflictAlgorithm: ConflictAlgorithm.ignore,
    );
  }

  // 处理并保存 Albaran 数据
  Future<void> processAndSaveAlbaranData(Map<String, dynamic> jsonData) async {
    Map<String, dynamic> albaranData = {
      'AlbaranProveedorNo': jsonData['AlbaranProveedorNo'],
      'ArticuloID': jsonData['ArticuloID'],
      'CodigoBarra': jsonData['CodigoBarra'],
      'CodigoBarraBalanza': jsonData['CodigoBarraBalanza'],
      'NombreES': jsonData['NombreES'],
      'NombreCN': jsonData['NombreCN'],
      'Precio': jsonData['Precio'],
      'Cantidad': jsonData['Cantidad'],
      'FechaCaducada': jsonData['FechaCaducada'],
      'OrdenNo': jsonData['OrdenNo'],
      'Checked': jsonData['Checked'],
    };

    await saveAlbaranDataToLocal(albaranData);
  }

  // 根据 AlbaranProveedorNo 获取 Albaran 详情
  static Future<List<Map<String, dynamic>>> getAlbaranDetailsByProveedorNo(String albaranProveedorNo) async {
    final db = await database;

    try {
      final List<Map<String, dynamic>> results = await db.query(
        'albaranproveedor',
        where: 'AlbaranProveedorNo = ?',
        whereArgs: [albaranProveedorNo],
      );

      return results;
    } catch (e) {
      print('Error fetching albaran details: $e');
      return [];
    }
  }

  // 根据 AlbaranProveedorNo 和 CodigoBarra 获取 Albaran 详情
  static Future<List<Map<String, dynamic>>> getAlbaranDetailsByProveedorNoAndBarcode(
      String albaranProveedorNo, String codigoBarra) async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> result = await db.query(
        'albaranproveedor',
        where: 'AlbaranProveedorNo = ? AND CodigoBarra = ?',
        whereArgs: [albaranProveedorNo, codigoBarra],
      );
      return result;
    } catch (e) {
      print('Error querying albaranproveedor by ProveedorNo and CodigoBarra: $e');
      return [];
    }
  }

  // 根据条码获取 Albaran
  Future<Map<String, dynamic>?> getAlbaranByBarcode(
      String albaranProveedorNo, String barcode) async {
    final db = await database;
    List<Map<String, dynamic>> result = await db.query(
      'albaranproveedor',
      where: 'AlbaranProveedorNo = ? AND CodigoBarra = ?',
      whereArgs: [albaranProveedorNo, barcode],
    );
    if (result.isNotEmpty) {
      return result.first;
    } else {
      return null;
    }
  }

  // 更新 Checked 值
  Future<void> updateCheckedValue(
      String albaranProveedorNo, String identifier, int newCheckedValue, {bool isArticuloID = false}) async {
    final db = await database;
    try {
      await db.transaction((txn) async {
        String whereClause = isArticuloID
            ? 'AlbaranProveedorNo = ? AND ArticuloID = ?'
            : 'AlbaranProveedorNo = ? AND CodigoBarra = ?';

        final result = await txn.query(
          'albaranproveedor',
          where: whereClause,
          whereArgs: [albaranProveedorNo, identifier],
        );

        if (result.isEmpty) {
          print('No records found with identifier: $identifier for AlbaranProveedorNo: $albaranProveedorNo');
          return;
        }

        print('Found matching record: ${result.first}');

        int count = await txn.update(
          'albaranproveedor',
          {'Checked': newCheckedValue},
          where: whereClause,
          whereArgs: [albaranProveedorNo, identifier],
        );

        print('Updated $count records with Checked value: $newCheckedValue');
      });
    } catch (e) {
      print('Error updating Checked value: $e');
    }
  }

  // 根据 AlbaranProveedorNo 和 ArticuloID 获取 Albaran 详情
  static Future<List<Map<String, dynamic>>> getAlbaranDetailsByProveedorNoAndArticuloID(
      String albaranProveedorNo, String articuloID) async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> result = await db.query(
        'albaranproveedor',
        where: 'AlbaranProveedorNo = ? AND ArticuloID = ?',
        whereArgs: [albaranProveedorNo, articuloID],
      );
      return result;
    } catch (e) {
      print('Error querying albaranproveedor by ProveedorNo and ArticuloID: $e');
      return [];
    }
  }

  // 获取本地注册数据
  static Future<Map<String, dynamic>?> getLocalRegistrationData(String deviceSerial) async {
    final db = await database;
    try {
      List<Map<String, dynamic>> result = await db.query(
        'servicereg',
        where: 'SN = ?',
        whereArgs: [deviceSerial],
      );

      if (result.isNotEmpty) {
        return {
          'serial': result.first['SN'],
          'regnumber': result.first['regnumber'],
        };
      } else {
        return null;
      }
    } catch (e) {
      print('Error getting local registration data: $e');
      return null;
    }
  }

  // 获取 albaranproveedor 表中的所有数据
  static Future<List<Map<String, dynamic>>> getAllDataFromWktechDb() async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> results = await db.query('albaranproveedor');
      return results;
    } catch (e) {
      print('Error fetching data from albaranproveedor: $e');
      return [];
    }
  }

  // 删除与 AlbaranProveedorNo 相关的所有记录
  static Future<void> deleteAlbaranDetailsByProveedorNo(String albaranProveedorNo) async {
    final db = await database;
    try {
      await db.delete(
        'albaranproveedor',
        where: 'AlbaranProveedorNo = ?',
        whereArgs: [albaranProveedorNo],
      );
      print('Deleted Albaran details for AlbaranProveedorNo: $albaranProveedorNo');
    } catch (e) {
      print('Error deleting Albaran details: $e');
    }
  }

  // 获取 print_mode
  static Future<int> getPrintMode() async {
    final db = await database;
    final List<Map<String, dynamic>> results = await db.query('prints', columns: ['print_mode'], where: 'id = ?', whereArgs: [1]);
    if (results.isNotEmpty) {
      return results[0]['print_mode'] ?? 1;
    }
    return 1;
  }

  // 保存 print_mode
  static Future<void> savePrintMode(int mode) async {
    final db = await database;
    await db.update('prints', {'print_mode': mode}, where: 'id = ?', whereArgs: [1]);
  }

  // 获取 beizhu
  static Future<String> getBeizhu() async {
    final db = await database;
    final List<Map<String, dynamic>> results = await db.query('prints', columns: ['beizhu'], where: 'id = ?', whereArgs: [1]);
    if (results.isNotEmpty) {
      return results[0]['beizhu'] ?? '';
    }
    return '';
  }

  // 保存 beizhu
  static Future<void> saveBeizhu(String beizhu) async {
    final db = await database;
    await db.update('prints', {'beizhu': beizhu}, where: 'id = ?', whereArgs: [1]);
  }

  // 获取 decimal_places 值
  static Future<String> getDecimalPlaces() async {
    final db = await database;
    final List<Map<String, dynamic>> results = await db.query(
      'prints',
      columns: ['decimal_places'],
      where: 'id = ?',
      whereArgs: [1],
    );
    if (results.isNotEmpty) {
      return results.first['decimal_places'].toString();
    }
    return '2';
  }

  // 保存 decimal_places 值
  static Future<void> saveDecimalPlaces(String decimalPlaces) async {
    final db = await database;
    await db.update(
      'prints',
      {'decimal_places': decimalPlaces},
      where: 'id = ?',
      whereArgs: [1],
    );
  }

// 获取 YGID 值
  static Future<String?> getYGID() async {
    final db = await database;
    final List<Map<String, dynamic>> results = await db.query(
      'yginfo',
      columns: ['YGID'],
      where: 'id = ?',
      whereArgs: [1],
    );
    if (results.isNotEmpty) {
      return results.first['YGID'].toString();
    }
    return null; // 当没有找到结果时返回 null
  }

  // 获取 label_40x30_mode
  static Future<int> getLabel40x30Mode() async {
    final db = await database;
    final List<Map<String, dynamic>> results = await db.query('prints', columns: ['label_40x30_mode'], where: 'id = ?', whereArgs: [1]);
    if (results.isNotEmpty) {
      return results[0]['label_40x30_mode'] ?? 0;
    }
    return 0;
  }

  // 保存 label_40x30_mode
  static Future<void> saveLabel40x30Mode(int mode) async {
    final db = await database;
    await db.update('prints', {'label_40x30_mode': mode}, where: 'id = ?', whereArgs: [1]);
  }

  // ---------- 本地盘点（离线）功能 ----------

  static Future<void> upsertStocktakingRecord({
    required String username,
    required String warehouse,
    required String barcode,
    required int quantity,
    String updateMode = 'add', // add 或 set
    bool forceNewRecord = false,
    String? articleId,
    bool isUnknown = false,
    String? oldStock,
    String? duplicateWarehouses,
    int? inUse,
    DateTime? when,
  }) async {
    final db = await database;
    final DateTime now = when ?? DateTime.now();
    final String stdata = _formatDateTime(now);

    try {
      if (forceNewRecord) {
        await db.insert('stocktaking', {
          'user': username,
          'Weizhi': warehouse,
          'CodigoBarra': barcode,
          'ArticuloID': articleId ?? '',
          'Stock': quantity.toString(),
          'isUnknown': isUnknown ? 1 : 0,
          'oldstock': oldStock,
          'stdata': stdata,
          'isSynced': 0,
        });
        return;
      }

      // 1) 先按 Weizhi + CodigoBarra 匹配
      Map<String, dynamic>? current;
      if (barcode.isNotEmpty && barcode != 'NO') {
        final existingByBarcode = await db.query(
          'stocktaking',
          where: 'user = ? AND Weizhi = ? AND CodigoBarra = ?',
          whereArgs: [username, warehouse, barcode],
          limit: 1,
        );
        if (existingByBarcode.isNotEmpty) {
          current = existingByBarcode.first;
        }
      }

      // 2) 若未命中且 ArticuloID 有值，则按 Weizhi + ArticuloID 匹配
      if (current == null && (articleId != null && articleId.isNotEmpty && articleId != 'NO')) {
        final existingByArticulo = await db.query(
          'stocktaking',
          where: 'user = ? AND Weizhi = ? AND ArticuloID = ?',
          whereArgs: [username, warehouse, articleId],
          limit: 1,
        );
        if (existingByArticulo.isNotEmpty) {
          current = existingByArticulo.first;
        }
      }

      if (current == null) {
        // 插入新记录
        await db.insert('stocktaking', {
          'user': username,
          'Weizhi': warehouse,
          'CodigoBarra': barcode,
          'ArticuloID': articleId ?? '',
          'Stock': quantity.toString(),
          'isUnknown': isUnknown ? 1 : 0,
          'oldstock': oldStock,
          'stdata': stdata,
          'isSynced': 0,
        });
      } else {
        // 更新数量（add 累计 / set 覆盖）
        int newStockInt = quantity;
        if (updateMode != 'set') {
          final String currentStockStr = (current['Stock'] ?? '0').toString();
          final int currentStock = int.tryParse(currentStockStr) ?? 0;
          newStockInt = currentStock + quantity;
        }
        await db.update(
          'stocktaking',
          {
            'CodigoBarra': barcode.isNotEmpty ? barcode : (current['CodigoBarra'] ?? ''),
            'ArticuloID': (articleId ?? (current['ArticuloID'] ?? '')),
            'Stock': newStockInt.toString(),
            'isUnknown': isUnknown ? 1 : (current['isUnknown'] ?? 0),
            'oldstock': oldStock ?? current['oldstock'],
            'stdata': stdata,
            'isSynced': 0,
          },
          where: 'id = ?',
          whereArgs: [current['id']],
        );
      }
    } catch (e) {
      print('Error upserting stocktaking record: $e');
    }
  }

  static Future<List<Map<String, dynamic>>> getLocalStocktakingDataForUser(String username) async {
    final db = await database;
    try {
      final rows = await db.query(
        'stocktaking',
        where: 'user = ?',
        whereArgs: [username],
        orderBy: 'id DESC',
      );
      return rows;
    } catch (e) {
      print('Error querying local stocktaking data: $e');
      return [];
    }
  }

  static Future<List<Map<String, dynamic>>> getUnsyncedStocktakingForUser(String username) async {
    final db = await database;
    try {
      return await db.query(
        'stocktaking',
        where: 'user = ? AND (isSynced IS NULL OR isSynced = 0)',
        whereArgs: [username],
        orderBy: 'id ASC',
      );
    } catch (e) {
      print('Error querying unsynced stocktaking data: $e');
      return [];
    }
  }

  static Future<void> markStocktakingSynced(int id) async {
    final db = await database;
    try {
      await db.update('stocktaking', {'isSynced': 1}, where: 'id = ?', whereArgs: [id]);
    } catch (e) {
      print('Error marking stocktaking as synced: $e');
    }
  }

  static Future<void> deleteLocalStocktakingRecord(int id) async {
    final db = await database;
    try {
      await db.delete('stocktaking', where: 'id = ?', whereArgs: [id]);
    } catch (e) {
      print('Error deleting local stocktaking record: $e');
    }
  }

  static Future<void> clearLocalStocktakingForUser(String username) async {
    final db = await database;
    try {
      await db.delete('stocktaking', where: 'user = ?', whereArgs: [username]);
    } catch (e) {
      print('Error clearing local stocktaking data: $e');
    }
  }

  // 删除指定用户的已同步盘点记录
  static Future<void> deleteSyncedStocktakingForUser(String username) async {
    final db = await database;
    try {
      await db.delete(
        'stocktaking', 
        where: 'user = ? AND isSynced = 1', 
        whereArgs: [username]
      );
      print('已删除用户 $username 的已同步盘点记录');
    } catch (e) {
      print('删除已同步盘点记录时发生错误: $e');
      throw e;
    }
  }

  static String _formatDateTime(DateTime dt) {
    // 返回 YYYY-MM-DD HH:mm:ss
    String two(int v) => v.toString().padLeft(2, '0');
    return '${dt.year}-${two(dt.month)}-${two(dt.day)} ${two(dt.hour)}:${two(dt.minute)}:${two(dt.second)}';
  }

  // ---------- 本地盘点（离线）功能 End ----------

  // ---------- 本地商品（articulos）功能 ----------

  static Future<void> upsertArticulo(Map<String, dynamic> articulo) async {
    final db = await database;
    await db.insert('articulos', articulo, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  static Future<void> upsertArticulos(List<Map<String, dynamic>> articulos) async {
    final db = await database;
    final batch = db.batch();
    for (final row in articulos) {
      batch.insert('articulos', row, conflictAlgorithm: ConflictAlgorithm.replace);
    }
    await batch.commit(noResult: true);
  }

  // 兼容调用名：批量插入/更新 articulos
  static Future<void> batchUpsertArticulos(List<Map<String, dynamic>> articulos) async {
    await upsertArticulos(articulos);
  }

  // 记录最后同步时间（使用 settings 表存储）
  static Future<void> setLastSyncTime(String category, String iso8601) async {
    final db = await database;
    final key = 'sync:' + category;
    await db.insert(
      'settings',
      {'key': key, 'value': iso8601},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  // 获取最后同步时间
  static Future<String?> getLastSyncTime(String category) async {
    final db = await database;
    final key = 'sync:' + category;
    final res = await db.query('settings', columns: ['value'], where: 'key = ?', whereArgs: [key], limit: 1);
    if (res.isNotEmpty) return res.first['value']?.toString();
    return null;
  }

  // 获取 articulos 的统计数据
  static Future<Map<String, int>> getArticulosStats() async {
    final db = await database;
    final totalRes = await db.rawQuery('SELECT COUNT(1) AS c FROM articulos');
    final withBarcodeRes = await db.rawQuery("SELECT COUNT(1) AS c FROM articulos WHERE ifnull(CodigoBarra, '') <> ''");
    int total = 0;
    int withBarcode = 0;
    if (totalRes.isNotEmpty) {
      final v = totalRes.first['c'];
      total = v is int ? v : int.tryParse('$v') ?? 0;
    }
    if (withBarcodeRes.isNotEmpty) {
      final v = withBarcodeRes.first['c'];
      withBarcode = v is int ? v : int.tryParse('$v') ?? 0;
    }
    return {'total': total, 'withBarcode': withBarcode};
  }

  static Future<Map<String, dynamic>?> getArticuloByBarcode(String barcode) async {
    final db = await database;
    final res = await db.query('articulos', where: 'CodigoBarra = ?', whereArgs: [barcode], limit: 1);
    if (res.isNotEmpty) return res.first;
    return null;
  }

  static Future<Map<String, dynamic>?> getArticuloByArticuloId(String articuloId) async {
    final db = await database;
    final res = await db.query('articulos', where: 'ArticuloID = ?', whereArgs: [articuloId], limit: 1);
    if (res.isNotEmpty) return res.first;
    return null;
  }

  static Future<int> getArticulosCount() async {
    final db = await database;
    final res = await db.rawQuery('SELECT COUNT(1) as cnt FROM articulos');
    if (res.isNotEmpty) {
      final v = res.first['cnt'];
      if (v is int) return v;
      return int.tryParse('$v') ?? 0;
    }
    return 0;
  }

  // ---------- 本地商品（articulos）功能 End ----------
}
