import 'package:flutter/material.dart';
import 'package:mistoer/services/database_service.dart'; // 导入数据库服务类
import 'api_print_service.dart'; // 导入API服务类
import 'order_detail_print.dart'; // 导入 OrderDetailPrint 类
import 'edit_articulo_screen.dart'; // 导入编辑商品页面
import 'print_label_screen.dart'; // 导入打印页面
import 'package:audioplayers/audioplayers.dart'; // 导入音频播放器
import 'package:shared_preferences/shared_preferences.dart'; // 导入 shared_preferences 包
import 'package:mistoer/widgets/custom_keyboard.dart'; // 导入自定义键盘

class AlbaranDetailPage extends StatefulWidget {
  final String albaranProveedorNo;
  final List<OrderDetailPrint> details;

  AlbaranDetailPage({required this.albaranProveedorNo, required this.details});

  @override
  _AlbaranDetailPageState createState() => _AlbaranDetailPageState();
}

class _AlbaranDetailPageState extends State<AlbaranDetailPage> {
  final TextEditingController _barcodeController = TextEditingController();
  final FocusNode _barcodeFocusNode = FocusNode();
  late DatabaseService _dbService;
  String? _selectedArticuloID;
  int? _selectedIndex;
  final ScrollController _scrollController = ScrollController();
  double _lastScrollPosition = 0;
  bool _isScanMode = true;
  bool _errorDisplayed = false;
  bool _showAll = true;
  bool _showCustomKeyboard = false;  // 添加自定义键盘状态

  late List<OrderDetailPrint> _originalDetails;
  late List<OrderDetailPrint> _displayedDetails;

  int? _poderMantenerArticulo;

  @override
  void initState() {
    super.initState();
    _dbService = DatabaseService();
    _originalDetails = List.from(widget.details);
    _displayedDetails = List.from(_originalDetails);
    _loadUserPermissions();
    _loadInputMode();
    _barcodeController.addListener(_onBarcodeChanged);

    // 延迟请求焦点，确保 widget 已经完全构建
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        FocusScope.of(context).requestFocus(_barcodeFocusNode);
      }
    });
  }

  // 监听条码输入变化
  void _onBarcodeChanged() {
    if (!_showAll) {
      _filterDisplayedDetails(_barcodeController.text);
    }
  }

  // 加载用户权限
  Future<void> _loadUserPermissions() async {
    int? poderMantenerArticulo = await DatabaseService.getPoderMantenerArticulo();

    setState(() {
      _poderMantenerArticulo = poderMantenerArticulo;
    });
  }

  // 加载保存的输入模式
  Future<void> _loadInputMode() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      _isScanMode = prefs.getBool('isScanMode') ?? true; // 默认扫描模式为 true
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 恢复到之前的滚动位置
      _scrollController.jumpTo(_lastScrollPosition);
    });
  }

  // 切换输入模式并保存
  void _toggleInputMode(bool value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      _isScanMode = value;
      prefs.setBool('isScanMode', _isScanMode); // 保存用户的选择
      final snackBar = SnackBar(
        content: Text(_isScanMode ? '扫描输入已开启' : '手动输入已开启'),
        duration: Duration(seconds: 2),
      );
      ScaffoldMessenger.of(context).showSnackBar(snackBar); // 显示提示
    });
  }

  // 显示设置下拉菜单
  void _showSettingsDropdown(BuildContext context) {
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(100, 100, 0, 0), // 设置显示位置
      items: [
        PopupMenuItem(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text("扫描输入"),
              Switch(
                value: _isScanMode,
                onChanged: (value) {
                  _toggleInputMode(value); // 切换输入模式
                  Navigator.pop(context); // 关闭菜单
                },
                activeColor: Colors.blue,
                inactiveThumbColor: Colors.grey,
              ),
              Text("手动输入"),
            ],
          ),
        ),
      ],
    );
  }

  // 切换显示模式
  void _toggleShowAll() {
    setState(() {
      _showAll = !_showAll;
      if (_showAll) {
        // 显示全部内容
        _displayedDetails = List.from(_originalDetails);
      } else {
        // 隐藏核对数和订购数一致的记录
        _displayedDetails = _originalDetails
            .where((item) => item.checked.toInt() != item.cantidad.toInt())
            .toList();
        
        // 如果输入框有内容，则进一步过滤
        if (_barcodeController.text.isNotEmpty) {
          _filterDisplayedDetails(_barcodeController.text);
        }
      }
    });
  }

  // 过滤显示的列表
  void _filterDisplayedDetails(String value) {
    setState(() {
      if (value.isEmpty) {
        // 如果输入框为空且处于隐藏模式，则只显示核对数和订购数不一致的项
        if (!_showAll) {
          _displayedDetails = _originalDetails
              .where((item) => item.checked.toInt() != item.cantidad.toInt())
              .toList();
        } else {
          _displayedDetails = List.from(_originalDetails);
        }
      } else {
        // 先根据显示模式决定基础列表
        List<OrderDetailPrint> baseList = _showAll 
            ? _originalDetails 
            : _originalDetails.where((item) => item.checked.toInt() != item.cantidad.toInt()).toList();
        
        // 然后根据输入值进一步过滤
        _displayedDetails = baseList
            .where((item) => item.articuloID == value || item.codigoBarra == value)
            .toList();
      }
    });
  }

  // 监听输入框完成输入后，回车触发处理逻辑
  void _onBarcodeSubmitted(String value) {
    setState(() {
      if (value.isEmpty) {
        _displayedDetails = List.from(_originalDetails);
      } else {
        if (!_showAll) {
          // 如果是只显示匹配项，更新显示列表
          _filterDisplayedDetails(value);
        }
        // 同时使用输入内容匹配 ArticuloID 和 codigoBarra
        int index = _displayedDetails.indexWhere(
                (item) => item.articuloID == value || item.codigoBarra == value); // 匹配 ArticuloID 或 codigoBarra

        if (index != -1) {
          // 如果找到匹配的条目
          OrderDetailPrint matchedItem = _displayedDetails.removeAt(index);
          _displayedDetails.insert(0, matchedItem); // 将匹配的条目顶置

          // 根据模式处理
          if (_isScanMode) {
            // 扫描模式，+1 累计
            _updateCheckedWithNewValue(
              matchedItem.codigoBarra,
              articuloID: matchedItem.articuloID,
            );
            // 清空输入框并保持焦点
            _barcodeController.clear();
            FocusScope.of(context).requestFocus(_barcodeFocusNode);
          } else {
            // 手动模式，弹出对话框
            _showDetailDialog(matchedItem);
          }
        } else {
          // 如果没有匹配的条目，并且错误提示未显示
          if (!_errorDisplayed) {
            _errorDisplayed = true; // 设置错误提示已显示
            AudioPlayer().play(AssetSource('err.mp3'));
            final snackBar = SnackBar(
              content: Text('商品信息错误'),
              duration: Duration(seconds: 2),
            );
            ScaffoldMessenger.of(context).showSnackBar(snackBar).closed.then((_) {
              // 当SnackBar消失时，重置错误提示显示状态
              setState(() {
                _errorDisplayed = false;
              });
            });
          }
          // 清空输入框并保持焦点
          _barcodeController.clear();
          FocusScope.of(context).requestFocus(_barcodeFocusNode);
        }
      }
    });
  }

  // 更新 Checked 值时的逻辑，优先使用 codigoBarra，如果为空则使用 ArticuloID
  Future<void> _updateCheckedWithNewValue(String codigoBarra,
      {int? newValue, String? articuloID}) async {
    try {
      bool itemMatched = false;

      // 根据情况择使用哪个标识符
      String identifier =
      codigoBarra.isNotEmpty ? codigoBarra.trim() : articuloID!.trim();
      bool useArticuloID = codigoBarra.isEmpty;

      // 遍历原始列表，寻找匹配的记录
      for (int i = 0; i < _originalDetails.length; i++) {
        if (_originalDetails[i].codigoBarra == codigoBarra ||
            (codigoBarra.isEmpty && _originalDetails[i].articuloID == articuloID)) {
          itemMatched = true;

          // 次匹配到时，将 Checked 的值增加 1
          final newChecked = newValue ?? _originalDetails[i].checked.toInt() + 1;

          // 调用数据库服务，更新数据库中的核对数值
          await _dbService.updateCheckedValue(
            widget.albaranProveedorNo,
            identifier, // 使用优先选择的标识符
            newChecked,
            isArticuloID: useArticuloID, // 如果没有条码则使用 ArticuloID
          );

          setState(() {
            // 更新本地列表中的 Checked 值
            _updateCheckedInList(_originalDetails, identifier, newChecked);
            _updateCheckedInList(_displayedDetails, identifier, newChecked);
          });

          // 播放成功扫描的声音
          AudioPlayer().play(AssetSource('scan.mp3'));
          break;
        }
      }

      // 如果没有匹配到，播放错误的提示音
      if (!itemMatched) {
        if (!_errorDisplayed) {
          _errorDisplayed = true;
          AudioPlayer().play(AssetSource('err.mp3'));
          final snackBar = SnackBar(
            content: Text('商品信息错误'),
            duration: Duration(seconds: 2),
          );
          ScaffoldMessenger.of(context).showSnackBar(snackBar).closed.then((_) {
            setState(() {
              _errorDisplayed = false;
            });
          });
        }
      }

      // 清空输入框并保持焦点
      _barcodeController.clear();
      if (mounted) {
        FocusScope.of(context).requestFocus(_barcodeFocusNode);
      }
    } catch (e) {
      print("Error updating Checked value: $e");
    }
  }

  // 更新列表中的核对数量
  void _updateCheckedInList(
      List<OrderDetailPrint> list, String identifier, int newChecked) {
    int index = list.indexWhere(
            (item) => item.codigoBarra == identifier || item.articuloID == identifier);
    if (index != -1) {
      list[index] = OrderDetailPrint(
        albaranProveedorNo: list[index].albaranProveedorNo,
        articuloID: list[index].articuloID,
        codigoBarra: list[index].codigoBarra,
        nombreES: list[index].nombreES,
        precio: list[index].precio,
        cantidad: list[index].cantidad,
        fechaCaducada: list[index].fechaCaducada,
        ordenNo: list[index].ordenNo,
        checked: newChecked.toDouble(),
      );
    }
  }

  Future<void> _showDetailDialog(OrderDetailPrint item) async {
    // 控制器用于输入价格和数量
    TextEditingController _inputController = TextEditingController();
    TextEditingController _precioDetalleController = TextEditingController();
    TextEditingController _precioMayorController = TextEditingController();
    // 增加焦点节点，用于自动聚焦
    final FocusNode _inputFocusNode = FocusNode();
    final FocusNode _precioDetalleFocusNode = FocusNode();
    final FocusNode _precioMayorFocusNode = FocusNode();

    // 从服务器获取价格数据
    final precioData = await ApiPrintService.fetchPrecioData(
      articuloID: item.articuloID,
      codigoBarra: item.codigoBarra,
    );

    // 初始化价格输入框的值
    if (precioData != null) {
      _precioDetalleController.text = double.parse(precioData['PrecioDetalle'].toString()).toStringAsFixed(2);
      _precioMayorController.text = double.parse(precioData['PrecioMayor'].toString()).toStringAsFixed(2);
    }

    // 定义保存数据的函数，供按钮调用
    Future<void> _saveData() async {
      int? newCheckedValue = int.tryParse(_inputController.text);
      double? newPrecioDetalle = double.tryParse(_precioDetalleController.text);
      double? newPrecioMayor = double.tryParse(_precioMayorController.text);

      if (newCheckedValue != null) {
        int totalChecked = item.checked.toInt() + newCheckedValue;

        // 更新数据库和界面显示
        await _updateCheckedWithNewValue(item.codigoBarra,
            newValue: totalChecked, articuloID: item.articuloID);
      }

      // 更新服务器上的价格
      if (newPrecioDetalle != null && newPrecioMayor != null) {
        bool success = await ApiPrintService.updatePrecio(
          articuloID: item.articuloID,
          precioDetalle: newPrecioDetalle,
          precioMayor: newPrecioMayor,
        );

        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('价格更新成功')),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('价格更新失败')),
          );
        }
      }
    }

    await showDialog(
      context: context,
      builder: (context) {
        // 使用延迟执行，确保弹窗显示后自动聚焦
        WidgetsBinding.instance.addPostFrameCallback((_) {
          FocusScope.of(context).requestFocus(_inputFocusNode);
        });
        
        return AlertDialog(
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 显示货号
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 6.0),
                  child: Text('货号: ${item.articuloID}', style: TextStyle(fontSize: 16)),
                ),
                // 现核对数和进货价水平排列
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 6.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '现核对数: ${item.checked.toInt()}',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.green,
                        ),
                      ),
                      Text(
                        '进货价: ${item.precio.toStringAsFixed(2)}',
                        style: TextStyle(fontSize: 18,color: Colors.red,),
                      ),
                    ],
                  ),
                ),
                // 并排显示零售价和批发价
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 6.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _precioDetalleController,
                          focusNode: _precioDetalleFocusNode,
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            labelText: '零售价',
                            border: OutlineInputBorder(),
                          ),
                          onTap: () {
                            // 点击时自动全选
                            if (_precioDetalleController.text.isNotEmpty) {
                              _precioDetalleController.selection = TextSelection(
                                baseOffset: 0,
                                extentOffset: _precioDetalleController.text.length,
                              );
                            }
                          },
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: TextField(
                          controller: _precioMayorController,
                          focusNode: _precioMayorFocusNode,
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            labelText: '批发价',
                            border: OutlineInputBorder(),
                          ),
                          onTap: () {
                            // 点击时自动全选
                            if (_precioMayorController.text.isNotEmpty) {
                              _precioMayorController.selection = TextSelection(
                                baseOffset: 0,
                                extentOffset: _precioMayorController.text.length,
                              );
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                // 增加核对数量
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 6.0),
                  child: TextField(
                    controller: _inputController,
                    focusNode: _inputFocusNode, // 添加焦点节点
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: '增加核对数量',
                      border: OutlineInputBorder(),
                    ),
                    onTap: () {
                      // 点击时自动全选
                      if (_inputController.text.isNotEmpty) {
                        _inputController.selection = TextSelection(
                          baseOffset: 0,
                          extentOffset: _inputController.text.length,
                        );
                      }
                    },
                  ),
                ),
                // 在输入框和按钮之间增加间距
                SizedBox(height: 5),
              ],
            ),
          ),
          actionsAlignment: MainAxisAlignment.spaceBetween,
          actions: [
            // 替换"修改商品"按钮为"保存并打印"按钮
            TextButton(
              onPressed: () async {
                await _saveData(); // 先保存数据
                // 释放焦点节点
                _inputFocusNode.dispose();
                _precioDetalleFocusNode.dispose();
                _precioMayorFocusNode.dispose();
                Navigator.of(context).pop(); // 关闭对话框

                // 跳转到标签打印页面
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => PrintLabelScreen(
                      articuloID: item.articuloID,
                      fromAlbaran: true,
                    ),
                  ),
                ).then((_) {
                  // 返回后清空条形码输入框并保持焦点
                  _barcodeController.clear();
                  FocusScope.of(context).requestFocus(_barcodeFocusNode);
                });
              },
              child: Text('保存并打印', style: TextStyle(fontSize: 18)),
            ),
            // 确认按钮，功能保持不变
            TextButton(
              onPressed: () async {
                await _saveData(); // 保存数据
                // 释放焦点节点
                _inputFocusNode.dispose();
                _precioDetalleFocusNode.dispose();
                _precioMayorFocusNode.dispose();
                Navigator.of(context).pop(); // 关闭对话框

                // 清空条形码输入框并保持焦点
                _barcodeController.clear();
                FocusScope.of(context).requestFocus(_barcodeFocusNode);
              },
              child: Text('确认', style: TextStyle(fontSize: 18)),
            ),
          ],
        );
      },
    );
  }

  // 打印标签的功能，集成 print_label_screen.dart
  void _printLabel() {
    if (_selectedArticuloID != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PrintLabelScreen(
            articuloID: _selectedArticuloID!,
            fromAlbaran: true, // 设置为 true，打印完成后返回
          ),
        ),
      ).then((_) {
        // 页面返回后恢复到之前的滚动位置
        _scrollController.jumpTo(_lastScrollPosition);

        // 重置选中的卡片
        setState(() {
          _selectedArticuloID = null;
          _selectedIndex = null;
        });
      });
    }
  }

  // 修改商品详情
  Future<void> _modifyItem(String articuloID) async {
    if (_poderMantenerArticulo != -1) {
      // 显示没有权限的提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('您没有权限修改商品')),
      );
      return;
    }

    final articuloData = await ApiPrintService.fetchArticuloData(articuloID);
    if (articuloData != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => EditArticuloScreen(articuloData: articuloData),
        ),
      ).then((_) {
        // 页面返回后恢复到之前的滚动位置
        _scrollController.jumpTo(_lastScrollPosition);

        // 保持弹窗打开，无需重置选中状态
      });
    }
  }

  @override
  void dispose() {
    _barcodeFocusNode.dispose();
    _barcodeController.removeListener(_onBarcodeChanged);
    _barcodeController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          '入库详单: ${widget.albaranProveedorNo}',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 18.0,  // 缩小字体大小
            overflow: TextOverflow.ellipsis,  // 防止溢出显示省略号
          ),
        ),
        centerTitle: true,
        elevation: 1.5,  // 轻微降低阴影
        backgroundColor: Colors.white,  // 背景设为白色
        foregroundColor: Colors.black87,  // 前景色设为深色
        actions: [
          IconButton(
            icon: Icon(Icons.settings, size: 22.0),  // 缩小图标尺寸
            onPressed: () => _showSettingsDropdown(context),
            tooltip: '设置',
          ),
        ],
      ),
      body: Stack(
        children: [
          Column(
            children: [
              // 搜索栏和显示/隐藏按钮
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 12.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.3),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: Offset(0, 1),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _barcodeController,
                        focusNode: _barcodeFocusNode,
                        keyboardType: TextInputType.none,  // 使用none来禁用系统键盘
                        showCursor: true,
                        style: TextStyle(fontSize: 16.0),
                        decoration: InputDecoration(
                          labelText: '输入条形码或货号',
                          labelStyle: TextStyle(color: Colors.grey[600]),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                            borderSide: BorderSide(color: Colors.grey[300]!),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                            borderSide: BorderSide(color: Colors.blue, width: 2.0),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                            borderSide: BorderSide(color: Colors.grey[300]!),
                          ),
                          contentPadding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 14.0),
                          suffixIcon: IconButton(
                            icon: Icon(
                              _showCustomKeyboard ? Icons.keyboard_hide : Icons.keyboard,
                              size: 28,
                              color: Colors.grey[700],
                            ),
                            onPressed: () {
                              setState(() {
                                _showCustomKeyboard = !_showCustomKeyboard;
                              });
                            },
                          ),
                        ),
                        onTap: () {
                          // 点击输入框时保持焦点，这样可以接收扫码输入
                          FocusScope.of(context).requestFocus(_barcodeFocusNode);
                        },
                        onSubmitted: (value) {
                          // 处理回车事件
                          _onBarcodeSubmitted(value);
                        },
                        onChanged: (value) {
                          // 处理扫码设备的输入
                          if (value.isNotEmpty && value.endsWith('\n')) {
                            // 移除换行符并处理输入
                            final cleanValue = value.replaceAll('\n', '');
                            _onBarcodeSubmitted(cleanValue);
                          } else if (!_showAll) {
                            _filterDisplayedDetails(value);
                          }
                        },
                      ),
                    ),
                    SizedBox(width: 8.0),
                    Container(
                      decoration: BoxDecoration(
                        color: _showAll ? Colors.blue.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      child: IconButton(
                        icon: Icon(
                          _showAll ? Icons.visibility : Icons.visibility_off,
                          color: _showAll ? Colors.blue : Colors.grey[600],
                          size: 28,
                        ),
                        onPressed: _toggleShowAll,
                        tooltip: _showAll ? '隐藏已完成的核对记录' : '显示全部记录',
                        padding: EdgeInsets.all(8.0),
                      ),
                    ),
                  ],
                ),
              ),
              
              // 按钮区域
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: (_poderMantenerArticulo == -1 && _selectedArticuloID != null)
                            ? () => _modifyItem(_selectedArticuloID!)
                            : null,
                        icon: Icon(Icons.edit, size: 20),
                        label: Text('修改', style: TextStyle(fontSize: 16)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          elevation: 2,
                          shadowColor: Colors.grey.withOpacity(0.5),
                          padding: EdgeInsets.symmetric(vertical: 12.0),
                        ),
                      ),
                    ),
                    SizedBox(width: 16.0),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _selectedArticuloID != null ? _printLabel : null,
                        icon: Icon(Icons.print, size: 20),
                        label: Text('打印标签', style: TextStyle(fontSize: 16)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          elevation: 2,
                          shadowColor: Colors.grey.withOpacity(0.5),
                          padding: EdgeInsets.symmetric(vertical: 12.0),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // 列表区域
              Expanded(
                child: ListView.builder(
                  controller: _scrollController,
                  itemCount: _displayedDetails.length,
                  itemBuilder: (context, index) {
                    final item = _displayedDetails[index];
                    final bool isComplete = item.checked.toInt() >= item.cantidad.toInt();
                    
                    return Container(
                      margin: EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10.0),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.2),
                            spreadRadius: 1,
                            blurRadius: 3,
                            offset: Offset(0, 1),
                          ),
                        ],
                      ),
                      child: Card(
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10.0),
                          side: BorderSide(
                            color: _selectedIndex == index
                                ? Colors.blue
                                : Colors.grey.withOpacity(0.2),
                            width: _selectedIndex == index ? 2.0 : 1.0,
                          ),
                        ),
                        color: _selectedIndex == index
                            ? Colors.blue.withOpacity(0.1)
                            : Colors.white,
                        child: InkWell(
                          onTap: () {
                            setState(() {
                              if (_selectedIndex == index) {
                                _selectedArticuloID = null;
                                _selectedIndex = null;
                              } else {
                                _selectedArticuloID = item.articuloID;
                                _selectedIndex = index;
                                _scrollController.animateTo(
                                  index * 110.0,
                                  duration: Duration(milliseconds: 300),
                                  curve: Curves.easeInOut,
                                );
                              }
                            });
                          },
                          borderRadius: BorderRadius.circular(10.0),
                          child: Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: Row(
                              children: [
                                Expanded(
                                  flex: 3,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        '货号: ${item.articuloID}',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                          color: Colors.black87,
                                        ),
                                      ),
                                      SizedBox(height: 4),
                                      Text(
                                        '进货价: ${item.precio.toStringAsFixed(2)}',
                                        style: TextStyle(
                                          color: Colors.red[700],
                                          fontSize: 15,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Expanded(
                                  flex: 2,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          Text(
                                            '订购: ',
                                            style: TextStyle(
                                              fontSize: 15,
                                              color: Colors.black54,
                                            ),
                                          ),
                                          Text(
                                            '${item.cantidad.toInt()}',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.black87,
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 4),
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          Text(
                                            '核对: ',
                                            style: TextStyle(
                                              fontSize: 15,
                                              color: Colors.black54,
                                            ),
                                          ),
                                          Text(
                                            '${item.checked.toInt()}',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                              color: isComplete ? Colors.green : Colors.red,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(width: 8.0),
                                Container(
                                  decoration: BoxDecoration(
                                    color: Colors.grey.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8.0),
                                  ),
                                  child: IconButton(
                                    icon: Icon(
                                      Icons.edit,
                                      color: Colors.blue[700],
                                      size: 22,
                                    ),
                                    onPressed: () => _showDetailDialog(item),
                                    padding: EdgeInsets.all(8.0),
                                    constraints: BoxConstraints(),
                                    tooltip: '编辑商品',
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
          if (_showCustomKeyboard)
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: Material(
                elevation: 8,
                color: Colors.grey[100],
                child: CustomKeyboard(
                  controller: _barcodeController,
                  showKeyboard: _showCustomKeyboard,
                  onSubmit: (value) {
                    _onBarcodeSubmitted(value);
                    setState(() {
                      _showCustomKeyboard = false;
                    });
                  },
                ),
              ),
            ),
        ],
      ),
    );
  }
}
