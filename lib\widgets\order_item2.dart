import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mistoer/models/order.dart';
import 'package:mistoer/services/api_service.dart';

class OrderItem2 extends StatelessWidget {
  final Order order;
  final VoidCallback onTap;
  final VoidCallback onGenerateReport;
  final int userRevision; // 用户的 revision 值
  final VoidCallback onReview;

  OrderItem2({
    required this.order,
    required this.onTap,
    required this.onGenerateReport,
    required this.userRevision,
    required this.onReview,
  });

  String _formatDate(String date) {
    DateTime parsedDate = DateTime.parse(date);
    return DateFormat('yyyy年MM月dd日').format(parsedDate);
  }

  Future<void> _confirmReview(BuildContext context) async {
    // Show a confirmation dialog before proceeding with the review
    bool confirm = await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('确认复核'),
          content: Text('您确定要复核此订单吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text('确认'),
            ),
          ],
        );
      },
    ) ?? false;

    if (confirm) {
      // If confirmed, perform the review and refresh
      try {
        await ApiService.updateOrderStatus(order.pedidoKey, 5);
        print('进入复核成功');
        onReview(); // Trigger the refresh callback
      } catch (e) {
        print('Error: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        color: Colors.white,
        margin: EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
        child: Padding(
          padding: EdgeInsets.all(8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(order.name, style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold)),
                    SizedBox(height: 4.0),
                    Text('订单编号: ${order.pedidoKey}'),
                    Text('日期: ${_formatDate(order.riqi)}'),
                    Text('金额: ${order.amount}'),
                    Text('备注: ${order.beizhu}'),
                  ],
                ),
              ),
              SizedBox(width: 10),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  SizedBox(height: 40),
                  Text(
                    '出库率: ${(order.fulfillmentRate * 100).toStringAsFixed(1)}%',
                    style: TextStyle(
                      color: order.fulfillmentRate < 1.0 ? Colors.red : Colors.green,
                      fontSize: 18.0,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 1.0),
                  ElevatedButton(
                    onPressed: () async {
                      if (userRevision == -1) {
                        await _confirmReview(context); // Ask for confirmation before review
                      } else {
                        onGenerateReport();
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      minimumSize: Size(10, 30),
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                    child: Text(
                      userRevision == -1 ? '进入复核' : '生成出库单',
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
