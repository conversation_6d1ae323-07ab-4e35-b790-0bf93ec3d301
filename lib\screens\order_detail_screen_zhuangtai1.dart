import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:mistoer/models/order.dart';
import 'package:mistoer/models/order_detail.dart';
import 'package:mistoer/services/api_service.dart';
import 'package:mistoer/services/database_service.dart';
import 'package:mistoer/providers/zhuangtai_provider.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:mistoer/models/dotteddivider.dart';
import 'package:mistoer/providers/settings_provider.dart';
import 'package:mistoer/widgets/custom_keyboard.dart';
import 'dart:async';
import 'package:mistoer/services/scan_api_service.dart';
class OrderDetailScreenZhuangtai1 extends StatefulWidget {
  final Order order;

  OrderDetailScreenZhuangtai1({required this.order});

  @override
  _OrderDetailScreenZhuangtai1State createState() =>
      _OrderDetailScreenZhuangtai1State();
}

class _OrderDetailScreenZhuangtai1State
    extends State<OrderDetailScreenZhuangtai1> {
  bool _isLoading = true;
  double _progress = 0.0;
  TextEditingController _barcodeController = TextEditingController();
  TextEditingController _searchController = TextEditingController();
  bool _isBulkCount = true;
  String _scannedBarcode = '';
  final FocusNode _barcodeFocusNode = FocusNode();
  bool _isEditing = false;
  final AudioPlayer _audioPlayer = AudioPlayer();
  String _searchQuery = '';
  bool _isSearchVisible = false;
  Timer? _uploadTimer;
  bool _isKeyboardVisible = false;

  @override
  void initState() {
    super.initState();
    _loadOrderDetails();
    _uploadRevision();
    _uploadTimer = Timer.periodic(Duration(seconds: 10), (timer) {
      _uploadRevision();
    });
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusBarcodeInput();
      setState(() {
        _isKeyboardVisible = false;
      });
    });
  }

  @override
  void dispose() {
    _barcodeFocusNode.dispose();
    _barcodeController.dispose();
    _searchController.dispose();
    _audioPlayer.dispose();
    _uploadTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadOrderDetails() async {
    try {
      List<OrderDetail> localDetails =
      await DatabaseService.getOrderDetails(widget.order.pedidoKey);
      if (localDetails.isNotEmpty) {
        Provider.of<ZhuangtaiProvider>(context, listen: false)
            .loadOrderDetails(localDetails);
        setState(() {
          _isLoading = false;
        });
      } else {
        List<OrderDetail> remoteDetails =
        await ApiService.fetchOrderDetails(widget.order.pedidoKey);
        int totalDetails = remoteDetails.length;
        int count = 0;
        for (var detail in remoteDetails) {
          await DatabaseService.insertOrderDetail(detail);
          count++;
          setState(() {
            _progress = count / totalDetails;
          });
        }
        Provider.of<ZhuangtaiProvider>(context, listen: false)
            .loadOrderDetails(remoteDetails);
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showNetworkErrorDialog();
    }
  }

  void _handleBarcodeScan(String barcode) async {
    if (barcode.isEmpty) {
      _showEmptyBarcodeErrorDialog();
      return;
    }

    List<OrderDetail> matchingDetails =
    Provider.of<ZhuangtaiProvider>(context, listen: false)
        .orderDetails
        .where((detail) => detail.bianhao == barcode)
        .toList();
    if (matchingDetails.isNotEmpty) {
      var detail = matchingDetails.first;
      if (detail.scan >= detail.zongshuliang) {
        _showOutStockDialog(
            'El pedido ha sido enviado, o verifique si el número de paquetes es correcto.订单已出库, 或检查中包数是否正确?');
        _playSound('err.mp3');
      } else {
        int count = _isBulkCount ? detail.baozhuangshu : 1;
        await _updateOrderDetails(barcode, count);
        _barcodeController.clear();
        _focusBarcodeInput();
      }
    } else {
      _showBarcodeErrorDialog();
    }
  }

  void _showEmptyBarcodeErrorDialog() {
    _playSound('err.mp3');
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Error'),
          content: Text('El campo no puede estar vacío, por favor ingrese el código de barras.'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _focusBarcodeInput();
              },
              child: Text('Si'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _updateOrderDetails(String barcode, int count) async {
    List<OrderDetail> matchingDetails =
    Provider.of<ZhuangtaiProvider>(context, listen: false)
        .orderDetails
        .where((detail) => detail.bianhao == barcode)
        .toList();
    if (matchingDetails.isNotEmpty) {
      var detail = matchingDetails.first;
      int newScanCount = detail.scan + count;
      if (newScanCount > detail.zongshuliang) {
        _showOutStockDialog('已出库数量不能大于订单总数');
        return;
      } else {
        detail = detail.copyWith(scan: newScanCount);
        await DatabaseService.updateOrderDetailScan(detail);
        Provider.of<ZhuangtaiProvider>(context, listen: false)
            .updateScan(detail.id, newScanCount);
        setState(() {
          _scannedBarcode = barcode;
        });
        _playSound('scan.mp3');
        _showBarcodeDetailDialog(detail);
        _startBlinking();
      }
    } else {
      _showBarcodeErrorDialog();
    }
  }

  void _playSound(String sound) async {
    try {
      await _audioPlayer.play(AssetSource(sound));
    } catch (e) {
      print('Error playing sound: $e');
    }
  }

  void _showOutStockDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('提示'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                _barcodeController.clear();
                Navigator.of(context).pop();
                _focusBarcodeInput();
              },
              child: Text('确定'),
            ),
          ],
        );
      },
    );
  }

  void _showBarcodeErrorDialog() {
    _playSound('err.mp3');
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Error'),
          content: Text('El artículo no pertenece al pedido 商品不属于该订单'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _barcodeController.clear();
                _focusBarcodeInput();
              },
              child: Text('Si'),
            ),
          ],
        );
      },
    );
  }

  void _showNetworkErrorDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Error de red'),
          content: Text('No se puede conectar al servidor, por favor verifique la conexión a Internet.无法连接到服务器，请检查网络连接。'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _focusBarcodeInput();
              },
              child: Text('Si'),
            ),
          ],
        );
      },
    );
  }

  void _startBlinking() {
    Future.delayed(Duration(milliseconds: 500), () {
      setState(() {
        _scannedBarcode = '';
      });
    });
    Future.delayed(Duration(seconds: 1), () {
      setState(() {
        _scannedBarcode = _barcodeController.text;
      });
    });
  }

  void _focusBarcodeInput() {
    if (mounted) {
      _barcodeFocusNode.requestFocus();
    }
  }

  void _toggleKeyboard() {
    setState(() {
      _isKeyboardVisible = !_isKeyboardVisible;
    });
    _focusBarcodeInput();
  }

  void _toggleSearch() {
    setState(() {
      _isSearchVisible = !_isSearchVisible;
      if (_isSearchVisible) {
        _isKeyboardVisible = false;
      } else {
        _searchQuery = '';
        _searchController.clear();
        _focusBarcodeInput();
      }
    });
  }

  void _applySearchQuery(String query) {
    setState(() {
      _searchQuery = query;
    });
  }

  void _toggleEdit() {
    setState(() {
      _isEditing = !_isEditing;
    });
  }

  Future<void> _saveChanges() async {
    for (var detail in Provider.of<ZhuangtaiProvider>(context, listen: false)
        .orderDetails) {
      if (detail.scan > detail.zongshuliang) {
        _showOutStockDialog('La cantidad despachada no puede ser mayor que el total del pedido.已出库数量不能大于订单总数');
        return;
      }
      await DatabaseService.updateOrderDetailScan(detail);
    }
    setState(() {
      _isEditing = false;
    });
  }

  void _showEditScanDialog(OrderDetail detail) {
    TextEditingController _editController = TextEditingController();
    _editController.text = detail.scan.toString();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('修改已出库数量'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('现出库数量为: ${detail.scan}'),
              TextField(
                controller: _editController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(labelText: '修改后数量'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _focusBarcodeInput();
              },
              child: Text('取消'),
            ),
            TextButton(
              onPressed: () {
                int newScan = int.tryParse(_editController.text) ?? detail.scan;
                if (newScan > detail.zongshuliang) {
                  _showOutStockDialog('La cantidad despachada no puede ser mayor que el total del pedido.已出库数量不能大于订单总数');
                } else {
                  Provider.of<ZhuangtaiProvider>(context, listen: false)
                      .updateScan(detail.id, newScan);
                  DatabaseService.updateOrderDetailScan(
                      detail.copyWith(scan: newScan));
                  setState(() {
                    _isEditing = false;
                  });
                  Navigator.of(context).pop();
                  _focusBarcodeInput();
                }
              },
              child: Text('确定'),
            ),
          ],
        );
      },
    );
  }
  void _showBarcodeDetailDialog(OrderDetail detail) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        double remaining = (detail.zongshuliang / detail.baozhuangshu) - (detail.scan / detail.baozhuangshu);
        int pcsPerPackage = detail.baozhuangshu;
        return AlertDialog(
          title: Text('Número de paquetes adicionales para escanear:'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '${remaining.toStringAsFixed(0)}',
                style: TextStyle(fontSize: 70, color: Colors.red, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 10),
              Text(
                'Cada paquete contiene: $pcsPerPackage pcs',
                style: TextStyle(fontSize: 18, color: Colors.blue),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _focusBarcodeInput();
              },
              child: Text('Sí'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildOrderCountDetailItem(OrderDetail detail, bool isHighlighted) {
    return ListTile(
      tileColor: isHighlighted
          ? Colors.blue
          : detail.scan == detail.zongshuliang
          ? Colors.white
          : Colors.blue.withOpacity(0.15),
      title: Row(
        children: [
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.location_on, color: Colors.blue),
                    SizedBox(width: 4),
                    Text(
                      '${detail.weizhi}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8),
                GestureDetector(
                  onTap: () => _showDetailDialog(detail),
                  child: Text(
                    '${detail.codigo}',
                    style: TextStyle(
                      fontSize: 25,
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: 100,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      '订单数: ',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    Text(
                      '${detail.zongshuliang}',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      '已出库: ',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    Text(
                      '${detail.scan}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          if (_isEditing)
            IconButton(
              icon: Icon(Icons.edit),
              onPressed: () {
                _showEditScanDialog(detail);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildPackageCountDetailItem(OrderDetail detail, bool isHighlighted) {
    return ListTile(
      tileColor: isHighlighted
          ? Colors.blue
          : detail.scan == detail.zongshuliang
          ? Colors.white
          : Colors.blue.withOpacity(0.15),
      title: Row(
        children: [
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.location_on, color: Colors.blue),
                    SizedBox(width: 4),
                    Text(
                      '${detail.weizhi}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8),
                GestureDetector(
                  onTap: () => _showDetailDialog(detail),
                  child: Text(
                    '${detail.codigo}',
                    style: TextStyle(
                      fontSize: 25,
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: 100,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      '包数: ',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    Text(
                      '${(detail.zongshuliang / detail.baozhuangshu).toStringAsFixed(0)}',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      '已出库: ',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    Text(
                      '${(detail.scan / detail.baozhuangshu).toStringAsFixed(0)}',
                      style: TextStyle(
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          if (_isEditing)
            IconButton(
              icon: Icon(Icons.edit),
              onPressed: () {
                _showEditScanDialog(detail);
              },
            ),
        ],
      ),
    );
  }

  Future<String?> _getLocalUsername() async {
    final empleados = await DatabaseService.getUserWithInUseValue(-1);
    if (empleados.isEmpty) {
      return 'Default';
    }
    return empleados.first['Nombre'] as String? ?? 'Default';
  }

  double _calculateRevision(List<OrderDetail> orderDetails) {
    int totalScan = orderDetails.fold(0, (sum, detail) => sum + detail.scan);
    int totalZongshuliang = orderDetails.fold(0, (sum, detail) => sum + detail.zongshuliang);

    return totalZongshuliang > 0
        ? double.parse((totalScan / totalZongshuliang).toStringAsFixed(3))
        : 0.0;
  }

  Future<void> _uploadRevision() async {
    String? username = await _getLocalUsername();
    if (username == null) {
      print('No username found.');
      return;
    }

    List<OrderDetail> orderDetails =
        Provider.of<ZhuangtaiProvider>(context, listen: false).orderDetails;
    double revision = _calculateRevision(orderDetails);

    try {
      await ApiService.uploadRevision(
        pedidoKey: widget.order.pedidoKey,
        Revisor: username,
        revision: revision,
      );
      print('Tasa de revision uploaded successfully.');
    } catch (e) {
      print('Error uploading tasa de revision: $e');
    }
  }

  Widget _buildDetailedModeItem(
      OrderDetail detail, bool isHighlighted, bool isOrderCountMode) {
    return ListTile(
      tileColor: isHighlighted
          ? Colors.blue
          : detail.scan == detail.zongshuliang
          ? Colors.white
          : Colors.blue.withOpacity(0.15),
      title: Row(
        children: [
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.location_on, color: Colors.blue),
                    SizedBox(width: 4),
                    Text(
                      '${detail.weizhi}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      isOrderCountMode ? '订单数: ' : '包数: ',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    Text(
                      isOrderCountMode
                          ? '${detail.zongshuliang}'
                          : '${(detail.zongshuliang / detail.baozhuangshu).toStringAsFixed(0)}',
                      style: TextStyle(
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      '已出库: ',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    Text(
                      isOrderCountMode
                          ? '${detail.scan}'
                          : '${(detail.scan / detail.baozhuangshu).toStringAsFixed(0)}',
                      style: TextStyle(
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.only(left: 20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('货号: ${detail.codigo}'),
                  Text('条形码: ${detail.bianhao}'),
                  Text('商品名: ${detail.name_ce}'),
                ],
              ),
            ),
          ),
          if (_isEditing)
            IconButton(
              icon: Icon(Icons.edit),
              onPressed: () {
                _showEditScanDialog(detail);
              },
            ),
        ],
      ),
    );
  }

  void _showDetailDialog(OrderDetail detail) async {
    List<Map<String, dynamic>> bigstockResult = [];
    try {
      bigstockResult = await ScanApiService.bigstockqueryDatabase(detail.bianhao, 'warehouse');
    } catch (e) {
      print('Failed to fetch stock info: $e');
    }

    String weizhi = bigstockResult.isNotEmpty ? bigstockResult.first['weizhi'] ?? '未知库位' : '未知库位';
    String caja = bigstockResult.isNotEmpty ? bigstockResult.first['caja']?.toString() ?? '0' : '0';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            '商品信息 Datos del producto',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('货号: ${detail.codigo}', textAlign: TextAlign.left),
              Text('条形码: ${detail.bianhao}', textAlign: TextAlign.left),
              Text('商品名: ${detail.name_ce}', textAlign: TextAlign.left),
              SizedBox(height: 10),
              Row(
                children: [
                  Icon(Icons.location_on, color: Colors.blue),
                  SizedBox(width: 4),
                  Text(
                    '库位: ',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '$weizhi',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 10),
              Row(
                children: [
                  Icon(Icons.inventory, color: Colors.green),
                  SizedBox(width: 4),
                  Text(
                    '箱数: ',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '$caja',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _focusBarcodeInput();
              },
              child: Text('关闭'),
            ),
          ],
        );
      },
    );
  }

  void _showKeyboardDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('键盘选项'),
          content: Text('是否显示键盘？'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  _isKeyboardVisible = false;
                });
                _focusBarcodeInput();
              },
              child: Text('不显示'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  _isKeyboardVisible = true;
                });
                _focusBarcodeInput();
              },
              child: Text('显示'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final settings = Provider.of<SettingsProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${widget.order.name.length > 15 ? widget.order.name.substring(0, 15) + '...' : widget.order.name} ',
              style: TextStyle(fontSize: 16.0),
              overflow: TextOverflow.ellipsis,
            ),
            if (widget.order.pedidoKey != null)
              Text(
                '${widget.order.pedidoKey}',
                style: TextStyle(fontSize: 16.0),
              ),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(_isEditing ? Icons.save : Icons.edit),
            onPressed: _isEditing ? _saveChanges : _toggleEdit,
          ),
          IconButton(
            icon: Icon(_isSearchVisible ? Icons.search_off : Icons.search),
            onPressed: _toggleSearch,
          ),
          PopupMenuButton<String>(
            onSelected: (String result) {
              if (result == 'ToggleBulk') {
                setState(() {
                  _isBulkCount = !_isBulkCount;
                  _showBulkToggleDialog();
                });
              }
            },
            itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
              PopupMenuItem<String>(
                value: 'ToggleBulk',
                child: Row(
                  children: [
                    Text(_isBulkCount ? '关闭中包' : '开启中包'),
                    Spacer(),
                    Switch(
                      value: _isBulkCount,
                      onChanged: (value) {
                        setState(() {
                          _isBulkCount = value;
                          _showBulkToggleDialog();
                        });
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          if (_isLoading)
            Container(
              color: Colors.grey[100],
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('加载中...'),
                  ],
                ),
              ),
            )
          else
            Expanded(
              child: Column(
                children: [
                  if (!_isSearchVisible)
                    Container(
                      padding: EdgeInsets.all(8.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _barcodeController,
                              focusNode: _barcodeFocusNode,
                              readOnly: false,
                              showCursor: true,
                              autofocus: true,
                              enableInteractiveSelection: true,
                              keyboardType: TextInputType.none,
                              cursorColor: Colors.blue,
                              cursorWidth: 2.0,
                              decoration: InputDecoration(
                                labelText: '扫描条形码',
                                filled: true,
                                fillColor: Colors.white,
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _isKeyboardVisible ? Icons.keyboard_hide : Icons.keyboard,
                                    color: Colors.blue,
                                  ),
                                  onPressed: _toggleKeyboard,
                                ),
                              ),
                              onTap: () {
                                if (!_isKeyboardVisible) {
                                  FocusScope.of(context).requestFocus(_barcodeFocusNode);
                                }
                              },
                              onChanged: (value) {
                                if (value.contains('\n')) {
                                  String barcode = value.trim();
                                  _barcodeController.clear();
                                  _handleBarcodeScan(barcode);
                                }
                              },
                              onSubmitted: (value) {
                                if (value.isNotEmpty) {
                                  _handleBarcodeScan(value);
                                  _barcodeController.clear();
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  if (_isSearchVisible)
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TextField(
                        controller: _searchController,
                        autofocus: true,
                        readOnly: false,
                        decoration: InputDecoration(
                          labelText: '搜索货号或条码',
                          filled: true,
                          fillColor: Colors.white,
                          border: OutlineInputBorder(),
                          suffixIcon: IconButton(
                            icon: Icon(Icons.search),
                            onPressed: () {
                              _applySearchQuery(_searchController.text);
                            },
                          ),
                        ),
                        onSubmitted: (value) {
                          _applySearchQuery(value);
                        },
                      ),
                    ),
                  Consumer<ZhuangtaiProvider>(
                    builder: (context, zhuangtaiProvider, child) {
                      double revisionProgress = _calculateRevision(zhuangtaiProvider.orderDetails);
                      return Transform.translate(
                        offset: Offset(0, -10.0),
                        child: Container(
                          width: double.infinity,
                          height: 10.0,
                          child: LinearProgressIndicator(
                            value: revisionProgress,
                            backgroundColor: Colors.grey[300],
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
                          ),
                        ),
                      );
                    },
                  ),
                  Expanded(
                    child: Consumer<ZhuangtaiProvider>(
                      builder: (context, zhuangtaiProvider, child) {
                        List<OrderDetail> filteredDetails = zhuangtaiProvider
                            .orderDetails
                            .where((detail) =>
                        _searchQuery.isEmpty ||
                            detail.codigo.contains(_searchQuery) ||
                            detail.bianhao.contains(_searchQuery))
                            .toList();

                        filteredDetails.sort((a, b) {
                          int aCompletion = a.zongshuliang - a.scan;
                          int bCompletion = b.zongshuliang - b.scan;
                          return aCompletion.compareTo(bCompletion);
                        });

                        return filteredDetails.isEmpty
                            ? Center(child: Text('No order details found.'))
                            : ListView.builder(
                          itemCount: filteredDetails.length,
                          itemBuilder: (context, index) {
                            var detail = filteredDetails[index];
                            bool isHighlighted =
                                detail.bianhao == _scannedBarcode;
                            return Padding(
                              padding:
                              const EdgeInsets.symmetric(vertical: 0),
                              child: Column(
                                children: [
                                  if (settings.isDetailedMode)
                                    _buildDetailedModeItem(
                                        detail,
                                        isHighlighted,
                                        settings.isOrderCountMode)
                                  else if (settings.isOrderCountMode)
                                    _buildOrderCountDetailItem(
                                        detail, isHighlighted)
                                  else
                                    _buildPackageCountDetailItem(
                                        detail, isHighlighted),
                                  DottedDivider(
                                    color: Colors.black,
                                    height: 1,
                                    dashWidth: 2,
                                  ),
                                ],
                              ),
                            );
                          },
                        );
                      },
                    ),
                  ),
                  if (_isKeyboardVisible && !_isSearchVisible)
                    CustomKeyboard(
                      controller: _barcodeController,
                      onSubmit: _handleBarcodeScan,
                      showKeyboard: _isKeyboardVisible,
                      onKeyboardVisibilityChanged: () {
                        setState(() {
                          _isKeyboardVisible = !_isKeyboardVisible;
                        });
                      },
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  void _showBulkToggleDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('中包切换'),
          content: Text(_isBulkCount ? '中包已开启' : '中包已关闭'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _focusBarcodeInput();
              },
              child: Text('确定'),
            ),
          ],
        );
      },
    );
  }
}
