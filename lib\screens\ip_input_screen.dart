import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // 用于获取设备序列号
import 'package:mistoer/services/api_service.dart'; // 用于 IP 验证
import 'package:mistoer/services/sn_service.dart';  // 用于设备序列号注册
import 'package:mistoer/services/database_service_sn.dart'; // 本地数据库服务 (DatabaseServiceSN)
import 'package:mistoer/services/database_service.dart';
import 'dart:io';  // 用于文件操作
import 'package:path_provider/path_provider.dart';  // 获取应用目录路径
import 'package:http/http.dart' as http;  // 用于网络请求

class IpInputScreen extends StatefulWidget {
  @override
  _IpInputScreenState createState() => _IpInputScreenState();
}

class _IpInputScreenState extends State<IpInputScreen> {
  final TextEditingController _ipController = TextEditingController();
  bool _isError = false;
  bool _isRegistered = false; // 控制是否已注册
  String _deviceSerial = '获取中...'; // 用于获取设备序列号
  bool _isSaveButtonEnabled = false; // 控制保存按钮状态
  bool _isInputEnabled = false; // 控制输入框状态
  bool _isActivated = false; // 控制是否激活
  bool _isLoading = false; // 添加加载状态控制

  static const platform = MethodChannel('com.example.mistoer/serial'); // 用于与原生代码通信

  @override
  void initState() {
    super.initState();
    _getDeviceSerial(); // 在页面初始化时获取设备序列号
    _checkRegistrationStatus(); // 检查设备的注册状态
    _loadSavedIpAddress(); // 加载保存的IP地址
  }

  // 获取设备序列号并写入数据库
  Future<void> _getDeviceSerial() async {
    try {
      final String serial = await platform.invokeMethod('getDeviceSerial');
      setState(() {
        _deviceSerial = serial;
      });
      print('Device Serial: $serial');
    } on PlatformException catch (e) {
      setState(() {
        _deviceSerial = '获取设备序列号失败';
        _isRegistered = false; // 获取序列号失败时切换按钮回发送注册信息
      });
      _showErrorDialog('无法获取设备序列号: ${e.message}');
    }
  }

  // 检查设备的注册状态
  Future<void> _checkRegistrationStatus() async {
    // 获取本地数据库中 servicereg 表的 serial 和 ok 值
    final Map<String, dynamic>? localData = await DatabaseServiceSN.getLocalRegistrationData(_deviceSerial);

    if (localData != null && localData.containsKey('ok') && localData['ok'] == 1) {
      // 如果 OK 值为 1，表示设备已经注册
      setState(() {
        _isInputEnabled = true;  // 只有在已注册的情况下才启用输入框
        _isSaveButtonEnabled = true;
        _isActivated = true; // 已激活
      });
    } else {
      // 设备未注册，输入框保持禁用状态
      setState(() {
        _isRegistered = false;
        _isActivated = false;
        _isInputEnabled = false;  // 未注册时输入框禁用
      });
    }
  }

  // 从本地数据库加载已保存的IP地址
  Future<void> _loadSavedIpAddress() async {
    try {
      String? savedIp = await DatabaseService.getIp(); // 从数据库获取已保存的IP地址
      if (savedIp != null) {
        setState(() {
          _ipController.text = savedIp; // 显示到输入框中
        });
      }
    } catch (e) {
      print('加载已保存的IP地址时出错: $e');
    }
  }

  // 获取注册码逻辑
  Future<void> _getRegNumber() async {
    if (_isLoading) return;
    setState(() {
      _isLoading = true;
    });

    try {
      // 先检查本地数据库中的注册状态
      Map<String, dynamic>? localData = await DatabaseServiceSN.getLocalRegistrationData(_deviceSerial);
      
      // 尝试连接服务器获取注册信息
      bool isServerConnected = false;
      Map<String, dynamic>? serverResponse;
      
      try {
        serverResponse = await SNservice.checkSerial(_deviceSerial);
        isServerConnected = true;
      } catch (e) {
        print('无法连接到服务器: $e');
        isServerConnected = false;
      }

      if (!isServerConnected) {
        // 离线模式：使用本地数据
        if (localData != null && localData['ok'] == 1) {
          // 检查试用期
          if (localData['esPrueba'] == 1) {
            DateTime? trialEndDate = DateTime.tryParse(localData['tiempoPrueba'] ?? '');
            if (trialEndDate != null && trialEndDate.isBefore(DateTime.now())) {
              // 试用期已过，清空本地数据
              await DatabaseServiceSN.clearRegistrationData();
              setState(() {
                _isInputEnabled = false;
                _isSaveButtonEnabled = false;
                _isActivated = false;
              });
              _showErrorDialog('试用期已过，请联系管理员');
              return;
            }
          }
          
          setState(() {
            _isInputEnabled = true;
            _isSaveButtonEnabled = true;
            _isActivated = true;
          });
          
          _showSuccessDialog('使用本地注册信息：${localData['corporatename'] ?? '未知公司'}');
        } else {
          _showErrorDialog('无法连接服务器且无有效的本地注册信息');
        }
      } else {
        // 在线模式：使用服务器响应
        if (serverResponse == null || !serverResponse.containsKey('data')) {
          // 服务器无法获取到序列号信息，清空本地数据
          await DatabaseServiceSN.clearRegistrationData();
          setState(() {
            _isInputEnabled = false;
            _isSaveButtonEnabled = false;
            _isActivated = false;
          });
          _showErrorDialog('无法获取设备序列号信息，请重新注册');
          return;
        }

        var data = serverResponse['data'];
        if (data == null || !data.containsKey('regnumber')) {
          // 无法获取注册码，清空本地注册码
          await DatabaseServiceSN.clearRegistrationCode(_deviceSerial);
          setState(() {
            _isInputEnabled = false;
            _isSaveButtonEnabled = false;
            _isActivated = false;
          });
          _showErrorDialog('无法获取注册码，请联系管理员');
          return;
        }

        String regNumber = data['regnumber'];
        String corporatename = data['corporatename'] ?? '未知公司';
        String shopid = data['shopid'] ?? '店铺ID';
        bool bohao = (data['bohao'] ?? 0) == 1;
        bool mayorista = (data['mayorista'] ?? 0) == 1;
        bool minorista = (data['minorista'] ?? 0) == 1;
        bool esPrueba = (data['esPrueba'] ?? 0) == 1;
        String? tiempoPrueba = data['tiempoPrueba'];

        // 检查试用期
        if (esPrueba && tiempoPrueba != null) {
          DateTime? trialEndDate = DateTime.tryParse(tiempoPrueba);
          if (trialEndDate != null && trialEndDate.isBefore(DateTime.now())) {
            // 试用期已过，清空本地数据
            await DatabaseServiceSN.clearRegistrationData();
            setState(() {
              _isInputEnabled = false;
              _isSaveButtonEnabled = false;
              _isActivated = false;
            });
            _showErrorDialog('试用期已过，请联系管理员');
            return;
          }
        }

        // 验证注册码
        bool isValid = SNservice.validateRegNumber(_deviceSerial, regNumber);
        if (isValid) {
          // 更新本地数据库
          await DatabaseServiceSN.upsertServiceRegRecord(
            sn: _deviceSerial,
            regnumber: regNumber,
            corporatename: corporatename,
            mayorista: mayorista ? 1 : 0,
            minorista: minorista ? 1 : 0,
            esPrueba: esPrueba ? 1 : 0,
            tiempoPrueba: tiempoPrueba,
            isOk: true,
            shopid: shopid,
            bohao: bohao ? 1 : 0,
          );

          await DatabaseServiceSN.updateRegistrationStatus(_deviceSerial, true);

          setState(() {
            _isInputEnabled = true;
            _isSaveButtonEnabled = true;
            _isActivated = true;
          });

          _showSuccessDialog('尊敬的客户：$corporatename，您的软件已可以使用！');
        } else {
          // 注册码验证失败，清空本地注册码
          await DatabaseServiceSN.clearRegistrationCode(_deviceSerial);
          setState(() {
            _isInputEnabled = false;
            _isSaveButtonEnabled = false;
            _isActivated = false;
          });
          _showErrorDialog('注册码验证失败，请联系管理员');
        }
      }
    } catch (e) {
      print('注册过程中出错: $e');
      _showErrorDialog('注册过程中出错，请重试');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 发送设备序列号到服务器并注册
  Future<void> _registerDevice() async {
    if (_isLoading) return;
    setState(() {
      _isLoading = true;
      _isRegistered = true;
    });

    try {
      // 检查本地注册状态
      Map<String, dynamic>? localData = await DatabaseServiceSN.getLocalRegistrationData(_deviceSerial);
      
      // 尝试连接服务器
      bool isServerConnected = false;
      Map<String, dynamic>? response;
      
      try {
        print('Sending device serial to server: $_deviceSerial');
        response = await SNservice.registerDevice(_deviceSerial);
        isServerConnected = true;
      } catch (e) {
        print('无法连接到服务器: $e');
        isServerConnected = false;
      }

      if (!isServerConnected) {
        // 离线模式：检查本地数据
        if (localData != null && localData['ok'] == 1) {
          // 检查试用期
          if (localData['esPrueba'] == 1) {
            DateTime? trialEndDate = DateTime.tryParse(localData['tiempoPrueba'] ?? '');
            if (trialEndDate != null && trialEndDate.isBefore(DateTime.now())) {
              _showErrorDialog('试用期已过，请联系管理员');
              return;
            }
          }
          
          setState(() {
            _isInputEnabled = true;
            _isSaveButtonEnabled = true;
            _isActivated = true;
          });
          
          print('使用本地注册信息');
        } else {
          _showErrorDialog('无法连接服务器且无有效的本地注册信息');
        }
      } else {
        // 在线模式：处理服务器响应
        if (response != null && response.containsKey('regnumber')) {
          String regNumber = response['regnumber'];
          String shopid = response['shopid'] ?? '店铺ID';
          int bohao = response['bohao'];
          
          await DatabaseServiceSN.upsertServiceRegRecord(
            sn: _deviceSerial,
            regnumber: regNumber,
            corporatename: '悟空科技',
            shopid: shopid,
            isOk: true,
            bohao: bohao,
          );

          await DatabaseServiceSN.updateRegistrationStatus(_deviceSerial, true);
          
          if (response.containsKey('image')) {
            String imageUrl = response['image'];
            await downloadAndReplaceLogo(imageUrl);
          }
          
          setState(() {
            _isInputEnabled = true;
            _isSaveButtonEnabled = true;
            _isActivated = true;
          });
        } else {
          print('Error: ${response?['error']}');
        }
      }
    } catch (e) {
      print('Error registering device: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 保存 IP 或域名
  Future<void> _saveIpOrDomain(BuildContext context) async {
    if (_isLoading) return; // 如果正在加载，直接返回
    setState(() {
      _isLoading = true; // 开始加载
    });

    // 检查输入框是否为空
    if (_ipController.text.isEmpty) {
      // 输入框为空，尝试从服务器获取IP
      if (_isActivated) {
        try {
          Map<String, dynamic>? shopIpResponse = await SNservice.getShopIpBySerial(_deviceSerial);
          if (shopIpResponse != null && 
              shopIpResponse['success'] == true && 
              shopIpResponse['data'] != null &&
              shopIpResponse['data']['shopip'] != null) {
            // 获取到shopip，更新输入框
            String shopIp = shopIpResponse['data']['shopip'];
            setState(() {
              _ipController.text = shopIp;
            });
            print('从服务器成功获取IP: $shopIp');
          } else {
            // 无法获取IP
            _showErrorDialog('无法从服务器获取IP地址，请手动输入');
            setState(() {
              _isLoading = false;
            });
            return;
          }
        } catch (e) {
          print('获取shopip时出错: $e');
          _showErrorDialog('获取服务器IP失败: ${e.toString()}');
          setState(() {
            _isLoading = false;
          });
          return;
        }
      } else {
        // 如果设备未激活且输入框为空
        _showErrorDialog('请输入服务器IP地址或域名');
        setState(() {
          _isLoading = false;
        });
        return;
      }
    } 
    // 此处继续使用输入框中的值（可能是用户输入的，也可能是刚从服务器获取的）
    String ipOrDomain = _ipController.text;
    if (ipOrDomain.isNotEmpty) {
      try {
        bool isValid = await ApiService.checkIpOrDomain(ipOrDomain);
        if (isValid) {
          await DatabaseService.setIp(ipOrDomain); // 将 IP 或域名写入本地数据库
          Navigator.pushReplacementNamed(context, '/login');
        } else {
          _showErrorDialog('IP 地址、域名或 IPv6 地址不正确，请确认后重新输入');
        }
      } catch (e) {
        // 如果与服务器断开连接，仍然保存输入的 IP 地址或域名
        print('与服务器连接失败，保存到本地数据库: $e');
        await DatabaseService.setIp(ipOrDomain); // 将 IP 或域名写入本地数据库
        _showSuccessDialog('服务器不可用，但 IP 地址已保存至本地');
      }
    }

    if (mounted) {
      setState(() {
        _isLoading = false; // 结束加载
      });
    }
  }

  // 下载并替换 logo.png
  Future<void> downloadAndReplaceLogo(String imageUrl) async {
    try {
      // 发起 GET 请求获取图片数据
      final response = await http.get(Uri.parse(imageUrl));

      if (response.statusCode == 200) {
        // 获取应用程序的文档目录路径（用于可写入的存储路径）
        Directory appDocDir = await getApplicationDocumentsDirectory();
        String logoPath = '${appDocDir.path}/logo.png';  // logo.png 的存储路径

        // 将获取到的图片数据写入文件
        File logoFile = File(logoPath);
        await logoFile.writeAsBytes(response.bodyBytes);

        print('Logo 下载并替换成功: $logoPath');
      } else {
        print('图片下载失败，状态码: ${response.statusCode}');
      }
    } catch (e) {
      print('下载或替换 logo 时发生错误: $e');
    }
  }

  // 显示错误对话框
  void _showErrorDialog(String message) {
    if (!mounted) return; // 如果组件已经被销毁，不显示对话框
    showDialog(
      context: context,
      barrierDismissible: false, // 防止点击对话框外部关闭
      builder: (context) => WillPopScope(
        onWillPop: () async => false, // 禁止返回键关闭对话框
        child: AlertDialog(
          title: Text('错误'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('确定'),
            ),
          ],
        ),
      ),
    );
  }

  // 显示成功对话框
  void _showSuccessDialog(String message) {
    if (!mounted) return; // 如果组件已经被销毁，不显示对话框
    showDialog(
      context: context,
      barrierDismissible: false, // 防止点击对话框外部关闭
      builder: (context) => WillPopScope(
        onWillPop: () async => false, // 禁止返回键关闭对话框
        child: AlertDialog(
          title: Text('软件已激活'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('确定'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('服务器配置'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 如果软件已激活，显示 "已激活" 和设备序列号；否则仅显示设备序列号
            _isActivated
                ? Column(
              children: [
                Text(
                  'SN: $_deviceSerial',
                  style: TextStyle(fontSize: 16, color: Colors.black),
                ),
                SizedBox(height: 10), // 在设备序列号与激活状态之间增加间距
                Text(
                  '已激活',
                  style: TextStyle(fontSize: 18, color: Colors.green),
                ),
              ],
            )
                : Text('设备序列号: $_deviceSerial'),
            SizedBox(height: 20),
            TextField(
              controller: _ipController,
              enabled: _isInputEnabled, // 根据状态启用或禁用输入框
              decoration: InputDecoration(
                labelText: '输入 IP 地址、域名',
                border: OutlineInputBorder(),
                errorText: _isError ? 'IP 地址、域名不正确' : null,
                suffixIcon: IconButton(
                  icon: Icon(Icons.clear),
                  onPressed: () {
                    _ipController.clear();
                  },
                ),
              ),
            ),
            SizedBox(height: 24.0),
            if (_isInputEnabled && _isSaveButtonEnabled)
              ElevatedButton(
                onPressed: _isLoading ? null : () => _saveIpOrDomain(context), // 禁用按钮当正在加载时
                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isLoading ? CircularProgressIndicator() : Text('连接服务器'),
              ),
            SizedBox(height: 24.0),
            // 按钮仅在未激活时显示
            if (!_isActivated && !_isRegistered)
              ElevatedButton(
                onPressed: _isLoading ? null : _registerDevice, // 禁用按钮当正在加载时
                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isLoading ? CircularProgressIndicator() : Text('发送注册信息'),
              ),
            if (!_isActivated && _isRegistered)
              ElevatedButton(
                onPressed: _isLoading ? null : _getRegNumber, // 禁用按钮当正在加载时
                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isLoading ? CircularProgressIndicator() : Text('注册设备'),
              ),
          ],
        ),
      ),
    );
  }
}
