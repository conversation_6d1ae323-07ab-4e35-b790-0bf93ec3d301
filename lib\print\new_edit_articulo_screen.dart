import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';
import 'package:mistoer/services/database_service_sn.dart';  // 从本地获取bohao值
import 'api_print_service.dart';  // 用于与服务器通信的服务类

class NewEditArticuloScreen extends StatefulWidget {
  final String? initialCodigo;
  final String? initialBarcode;

  NewEditArticuloScreen({this.initialCodigo, this.initialBarcode});

  @override
  _NewEditArticuloScreenState createState() => _NewEditArticuloScreenState();
}

class _NewEditArticuloScreenState extends State<NewEditArticuloScreen> {
  final TextEditingController _ArticuloIDController = TextEditingController();
  final TextEditingController _codigoBarraController = TextEditingController();
  final TextEditingController _nombreArticuloController = TextEditingController();
  final TextEditingController _precioVentaController = TextEditingController();
  final TextEditingController _precioEntradaController = TextEditingController();
  final TextEditingController _precioMayorController = TextEditingController();
  final TextEditingController _precioEspecialController = TextEditingController();
  final TextEditingController _precioSocioController = TextEditingController();
  final TextEditingController _precioFacturaController = TextEditingController();
  final TextEditingController _cantPaqueteController = TextEditingController();
  final TextEditingController _cantCajaController = TextEditingController();
  final TextEditingController _categoriaController = TextEditingController(text: '1');  // 默认值为1
  final TextEditingController _stockMinController = TextEditingController();
  final TextEditingController _stockMaxController = TextEditingController();
  final TextEditingController _unidadNombreController = TextEditingController();
  final TextEditingController _proveedorController = TextEditingController();
  final TextEditingController _sitioAlmacenController = TextEditingController();
  final TextEditingController _volumenPesoController = TextEditingController();

  bool isPorPaquete = false;
  bool isDescuentoCambioProhibido = false;
  bool isBloqueado = false;
  bool useUnidadUsarRegla = false;  // 根据bohao值控制是否使用UnidadUsarRegla
  List<Map<String, dynamic>> categories = [];
  bool _isGeneratingBarcode = false;  // 表示条形码是否正在生成

  @override
  void initState() {
    super.initState();

    // 根据输入的长度决定是商品编码还是商品条码
    if (widget.initialCodigo != null && widget.initialCodigo!.length <= 10) {
      _ArticuloIDController.text = widget.initialCodigo!;
    } else if (widget.initialBarcode != null && widget.initialBarcode!.length > 10) {
      _codigoBarraController.text = widget.initialBarcode!;
    }

    fetchCategories();  // 调用获取类别的方法
    checkBohao();  // 检查bohao的值
  }

  Future<void> checkBohao() async {
    final bohaoValue = await DatabaseServiceSN.getBohaoValue();

    if (bohaoValue != null) {
      print('bohao 值获取成功: $bohaoValue');
    } else {
      print('bohao 值获取失败，值为 null');
    }

    if (bohaoValue == 1) {
      setState(() {
        useUnidadUsarRegla = true;  // 当 bohao 为 1 时，启用 UnidadUsarRegla
      });
    }
  }

  Future<void> fetchCategories() async {
    final fetchedCategories = await ApiPrintService.fetchArticuloClase();
    if (fetchedCategories != null) {
      final uniqueCategories = fetchedCategories.toSet().toList();
      setState(() {
        categories = uniqueCategories;
      });
    }
  }

  Future<void> saveArticulo() async {
    // 检查必填项
    if (_ArticuloIDController.text.isEmpty || _codigoBarraController.text.isEmpty || _nombreArticuloController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('请填写所有带星号的必填项')));
      return;
    }

    // 获取本地数据库的 bohao 值
    final bohaoValue = await DatabaseServiceSN.getBohaoValue();

    // 保留“整包销售”标签，但在后台决定字段名
    String soloPorUnidadKey = 'SoloPorUnidad';  // 默认情况下为 SoloPorUnidad
    String soloPorUnidadValue = isPorPaquete ? '1' : '0';  // 根据复选框的选择决定传递的值

    // 默认的 ProveedorID 及其值
    String proveedorKey = 'ProveedorID';  // 默认情况下字段名为 ProveedorID
    String proveedorValue = _proveedorController.text.isNotEmpty ? _proveedorController.text : '0';  // 默认传递用户输入值或 0

    // 如果 bohao 为 1，则修改字段名为 UnidadUsarRegla 和 EmpresaID
    if (bohaoValue == 1) {
      soloPorUnidadKey = 'UnidadUsarRegla';  // 将字段名修改为 UnidadUsarRegla
      proveedorKey = 'EmpresaID';  // 将字段名修改为 EmpresaID
      proveedorValue = '0';  // 默认传递 0
    }

    // 生成 articuloData
    final articuloData = {
      'ArticuloID': _ArticuloIDController.text,
      'CodigoBarra': _codigoBarraController.text,
      'NombreES': _nombreArticuloController.text,
      'PrecioDetalle': _precioVentaController.text.isNotEmpty ? _precioVentaController.text : '0',
      'PrecioCoste': _precioEntradaController.text.isNotEmpty ? _precioEntradaController.text : '0',
      'PrecioMayor': _precioMayorController.text.isNotEmpty ? _precioMayorController.text : '0',
      'PrecioEspecial': _precioEspecialController.text.isNotEmpty ? _precioEspecialController.text : '0',
      'PrecioSocio': _precioSocioController.text.isNotEmpty ? _precioSocioController.text : '0',
      'PrecioFactura': _precioFacturaController.text.isNotEmpty ? _precioFacturaController.text : '0',
      'CantidadPorUnidad': _cantPaqueteController.text.isNotEmpty ? _cantPaqueteController.text : '0',
      'CantidadPorUnidad2': _cantCajaController.text.isNotEmpty ? _cantCajaController.text : '0',
      'ClaseID': _categoriaController.text.isNotEmpty ? _categoriaController.text : '1',  // 默认为1
      'MiniStock': _stockMinController.text.isNotEmpty ? _stockMinController.text : '0',
      'MaxiStock': _stockMaxController.text.isNotEmpty ? _stockMaxController.text : '0',
      'UnidadNombre': _unidadNombreController.text.isNotEmpty ? _unidadNombreController.text : '',
      'SitioEnAlmacen': _sitioAlmacenController.text.isNotEmpty ? _sitioAlmacenController.text : '0',
      'VolumenPeso': _volumenPesoController.text.isNotEmpty ? _volumenPesoController.text : '0',
      // 动态决定 SoloPorUnidad 或 UnidadUsarRegla
      soloPorUnidadKey: soloPorUnidadValue,
      // 动态决定 ProveedorID 或 EmpresaID
      proveedorKey: proveedorValue,
      'DescuentoCambioProhibido': isDescuentoCambioProhibido ? '-1' : '0',
      'Bloqueado': isBloqueado ? '-1' : '0',
    };

    print('Sending articuloData: $articuloData');  // 打印输出，调试查看

    final response = await ApiPrintService.createArticulo(articuloData);

    if (response) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('商品已创建')));
      Navigator.pop(context, true);  // 返回并通知上一页创建成功
    } else {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('创建失败，请重试')));
    }
  }

  /// 生成条形码并验证唯一性
  Future<void> generateAndCheckBarcode() async {
    // 如果初始条形码已存在，表示是扫描条形码新建商品，不再生成条形码
    if (widget.initialBarcode != null && widget.initialBarcode!.isNotEmpty) {
      return;
    }

    setState(() {
      _isGeneratingBarcode = true;  // 开始生成条形码时显示进度指示器
    });

    String barcode;
    bool exists;

    // 获取商品编码
    String articuloId = _ArticuloIDController.text;

    // 检查商品编码是否为空
    if (articuloId.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('请先填写商品编码')));
      setState(() {
        _isGeneratingBarcode = false;  // 停止进度指示器
      });
      return;
    }

    // 反复生成直到找到一个唯一的条形码
    do {
      barcode = generateEn13Barcode(articuloId);  // 传入商品编码
      exists = await ApiPrintService.checkBarcodeExistsInDatabase(barcode);
      if (exists) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('生成的条码已存在，重新生成')));
      }
    } while (exists);

    // 更新输入框显示生成的唯一条形码
    setState(() {
      _codigoBarraController.text = barcode;
      _isGeneratingBarcode = false;  // 停止进度指示器
    });
  }

  /// 生成 EN-13 条形码，包含校验位
  String generateEn13Barcode(String articuloId) {
    final random = Random();

    // 随机选择69、89或00到99之间的数字作为条形码的前两位
    String prefix = random.nextBool()
        ? (random.nextBool() ? "69" : "89")
        : random.nextInt(100).toString().padLeft(2, '0');

    // 自动生成中间几位的随机数，填充到9位
    String middle = List.generate(9 - articuloId.length, (index) => random.nextInt(10).toString()).join();

    // 将商品编码的数字插入到倒数第二位开始
    String barcodeBase = prefix + middle + articuloId.padLeft(6, '0');

    // 计算校验位
    int checksum = calculateChecksum(barcodeBase);

    // 返回完整的条形码：12位 + 校验位
    return barcodeBase + checksum.toString();
  }

  /// 计算 EN-13 条形码的校验位
  int calculateChecksum(String barcodeBase) {
    int sum = 0;

    // 从右到左遍历条形码的前 12 位（不包括最后一位校验位）
    for (int i = 0; i < barcodeBase.length; i++) {
      int digit = int.parse(barcodeBase[i]);

      // 奇数位乘以3，偶数位乘以1
      if (i % 2 == 0) {
        sum += digit;
      } else {
        sum += digit * 3;
      }
    }

    // 计算校验位（校验位 = 10 - (sum % 10)，如果sum % 10 为0，则校验位为0）
    return (10 - (sum % 10)) % 10;
  }

  @override
  void dispose() {
    _ArticuloIDController.dispose();
    _codigoBarraController.dispose();
    _nombreArticuloController.dispose();
    _precioVentaController.dispose();
    _precioEntradaController.dispose();
    _precioMayorController.dispose();
    _precioEspecialController.dispose();
    _precioSocioController.dispose();
    _precioFacturaController.dispose();
    _cantPaqueteController.dispose();
    _cantCajaController.dispose();
    _categoriaController.dispose();
    _stockMinController.dispose();
    _stockMaxController.dispose();
    _unidadNombreController.dispose();
    _proveedorController.dispose();
    _sitioAlmacenController.dispose();
    _volumenPesoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('创建新商品'),
      ),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  buildTextFieldWithButton(
                    '商品编码*:',
                    _ArticuloIDController,
                    onTextChanged: (value) {
                      generateAndCheckBarcode();  // 每当商品编码更改时自动生成条形码
                    },
                    keyboardType: TextInputType.number,
                  ),
                  buildTextFieldWithButton(
                    '商品条码*:',
                    _codigoBarraController,
                    isBarcode: true,
                    keyboardType: TextInputType.number,
                  ),
                  if (_isGeneratingBarcode)
                    Center(
                      child: CircularProgressIndicator(),
                    ),
                  buildTextField('西文名称*:', _nombreArticuloController),
                  Row(
                    children: [
                      Expanded(
                        child: buildTextField(
                          '零售价:',
                          _precioVentaController,
                          keyboardType: TextInputType.numberWithOptions(decimal: true),
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: buildTextField(
                          '进货价:',
                          _precioEntradaController,
                          keyboardType: TextInputType.numberWithOptions(decimal: true),
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: buildTextField(
                          '批发价:',
                          _precioMayorController,
                          keyboardType: TextInputType.numberWithOptions(decimal: true),
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: buildTextField(
                          '特别价:',
                          _precioEspecialController,
                          keyboardType: TextInputType.numberWithOptions(decimal: true),
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: buildTextField(
                          '会员价:',
                          _precioSocioController,
                          keyboardType: TextInputType.numberWithOptions(decimal: true),
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: buildTextField(
                          '发票价:',
                          _precioFacturaController,
                          keyboardType: TextInputType.numberWithOptions(decimal: true),
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: buildTextField(
                          '整包数:',
                          _cantPaqueteController,
                          keyboardType: TextInputType.numberWithOptions(decimal: true),
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: buildTextField(
                          '整箱数:',
                          _cantCajaController,
                          keyboardType: TextInputType.numberWithOptions(decimal: true),
                        ),
                      ),
                    ],
                  ),
                  categories.isEmpty
                      ? CircularProgressIndicator()  // 如果类别尚未加载，则显示加载指示器
                      : buildCategoryDropdown('类别:', _categoriaController),
                  Row(
                    children: [
                      Expanded(
                        child: buildTextField(
                          '库存下限:',
                          _stockMinController,
                          keyboardType: TextInputType.numberWithOptions(decimal: true),
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: buildTextField(
                          '库存上限:',
                          _stockMaxController,
                          keyboardType: TextInputType.numberWithOptions(decimal: true),
                        ),
                      ),
                    ],
                  ),
                  buildTextField('单位名称:', _unidadNombreController),
                  buildTextField('供应商:', _proveedorController),
                  buildTextField('仓库位置:', _sitioAlmacenController),
                  buildTextField(
                    '重量容积:',
                    _volumenPesoController,
                    keyboardType: TextInputType.numberWithOptions(decimal: true),
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: CheckboxListTile(
                          title: Text('整包销售:'),  // 无论 bohao 值如何，保持标签名称为“整包销售”
                          value: isPorPaquete,
                          onChanged: (bool? value) {
                            setState(() {
                              isPorPaquete = value ?? false;
                            });
                          },
                        ),
                      ),
                      Expanded(
                        child: CheckboxListTile(
                          title: Text('禁用:'),
                          value: isBloqueado,
                          onChanged: (bool? value) {
                            setState(() {
                              isBloqueado = value ?? false;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  CheckboxListTile(
                    title: Text('禁止修改折扣:'),
                    value: isDescuentoCambioProhibido,
                    onChanged: (bool? value) {
                      setState(() {
                        isDescuentoCambioProhibido = value ?? false;
                      });
                    },
                  ),
                  SizedBox(height: 80),  // 增加页面的长度
                ],
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              color: Colors.white,
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: double.infinity,  // 按钮宽度填满屏幕
                height: 50,  // 自定义按钮高度
                child: ElevatedButton(
                  onPressed: saveArticulo,  // 调用保存方法
                  child: Text('保存'),
                  style: ElevatedButton.styleFrom(
                    textStyle: TextStyle(fontSize: 20),  // 设置字体大小
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 带按钮的文本输入框（例如商品编码）
  Widget buildTextFieldWithButton(
      String label,
      TextEditingController controller, {
        Function(String)? onTextChanged,
        bool isBarcode = false,
        TextInputType keyboardType = TextInputType.text,
      }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: controller,
              onChanged: onTextChanged,
              keyboardType: keyboardType,
              style: TextStyle(
                fontWeight: FontWeight.bold,  // 设置输入内容为粗体
              ),
              decoration: InputDecoration(
                labelText: label,
                labelStyle: TextStyle(
                  fontSize: 18,  // 自定义标题字体大小
                ),
                border: OutlineInputBorder(),
              ),
            ),
          ),
          if (!isBarcode)  // 只为商品编码显示生成条形码按钮
            IconButton(
              icon: Icon(Icons.qr_code),
              onPressed: () {
                generateAndCheckBarcode();
              },
            ),
        ],
      ),
    );
  }

  Widget buildTextField(
      String label,
      TextEditingController controller, {
        bool enabled = true,
        TextInputType keyboardType = TextInputType.text,
      }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextField(
        controller: controller,
        enabled: enabled,
        keyboardType: keyboardType,
        style: TextStyle(
          fontWeight: FontWeight.bold,  // 设置输入内容为粗体
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            fontSize: 18,  // 自定义标题字体大小
          ),
          border: OutlineInputBorder(),
        ),
      ),
    );
  }

  Widget buildCategoryDropdown(String label, TextEditingController controller) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: DropdownButtonFormField<String>(
        value: categories.any((category) => category['ClaseID'].toString() == controller.text)
            ? controller.text
            : '1',  // 如果当前值不存在于类别中，则设置为1
        onChanged: (String? newValue) {
          setState(() {
            controller.text = newValue ?? '1';
          });
        },
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            fontSize: 18,  // 自定义标题字体大小
          ),
          border: OutlineInputBorder(),
        ),
        items: categories.map<DropdownMenuItem<String>>((category) {
          return DropdownMenuItem<String>(
            value: category['ClaseID'].toString(),
            child: Text(category['NombreES']),
          );
        }).toList(),
      ),
    );
  }
}
