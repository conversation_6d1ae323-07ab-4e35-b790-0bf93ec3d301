package com.example.mistoer;

import android.content.ComponentName;
import android.content.Intent;
import android.content.ServiceConnection;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import android.os.IBinder;
import android.provider.Settings;
import android.util.Log;

import androidx.annotation.NonNull;

import io.flutter.embedding.android.FlutterActivity;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

import com.sunmi.peripheral.printer.SunmiPrinterService;
import com.sunmi.peripheral.printer.WoyouConsts;
import com.sunmi.peripheral.printer.InnerPrinterManager;
import com.sunmi.peripheral.printer.InnerPrinterCallback;
import com.sunmi.peripheral.printer.InnerPrinterException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.io.File;
import android.os.Build;
import android.os.Environment;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Typeface;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;
import com.journeyapps.barcodescanner.BarcodeEncoder;


public class MainActivity extends FlutterActivity {
    private static final String CHANNEL = "com.example.mistoer/print";
    private static final String POS_PRINT_CHANNEL = "com.example.mistoer/POSprint";
    private static final String SERIAL_CHANNEL = "com.example.mistoer/serial";
    private static final String INSTALL_CHANNEL = "com.example.mistoer/install";
    private static final String SCAN_CHANNEL = "com.example.mistoer/scan";
    private static final String LIST_PRINT_CHANNEL = "com.example.mistoer/listprint";

    private SunmiPrinterService sunmiPrinterService;
    private MethodChannel.Result scanResult;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 尝试绑定商米打印服务
        bindPrinterService();

        // 初始化 MethodChannel 用于处理 Flutter 的打印请求
        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), CHANNEL)
                .setMethodCallHandler(new MethodChannel.MethodCallHandler() {
                    @Override
                    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
                        if (call.method.equals("printLabel")) {
                            handlePrintLabel(call, result);
                        } else if (call.method.equals("printReceipt")) {
                            handlePrintReceipt(call, result);
                        } else {
                            result.notImplemented();
                        }
                    }
                });

        // 初始化 MethodChannel 用于获取 Android 序列号
        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), SERIAL_CHANNEL)
                .setMethodCallHandler((call, result) -> {
                    if (call.method.equals("getDeviceSerial")) {
                        String androidId = getAndroidId();
                        if (androidId != null) {
                            result.success(androidId);
                        } else {
                            result.error("UNAVAILABLE", "Failed to get Android ID", null);
                        }
                    } else {
                        result.notImplemented();
                    }
                });

        // 初始化 MethodChannel 用于处理 APK 安装请求
        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), INSTALL_CHANNEL)
                .setMethodCallHandler((call, result) -> {
                    if (call.method.equals("installApk")) {
                        String filePath = call.argument("filePath");
                        String fileName = call.argument("fileName");
                        installApk(filePath, fileName, result);
                    } else {
                        result.notImplemented();
                    }
                });

        // 初始化 MethodChannel 用于处理扫码请求
        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), SCAN_CHANNEL)
                .setMethodCallHandler(new MethodChannel.MethodCallHandler() {
                    @Override
                    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
                        if (call.method.equals("startScan")) {
                            startScan(result);
                        } else {
                            result.notImplemented();
                        }
                    }
                });

        // 初始化新的 POS 打印通道
        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), POS_PRINT_CHANNEL)
                .setMethodCallHandler(new MethodChannel.MethodCallHandler() {
                    @Override
                    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
                        if (call.method.equals("printReceipt")) {
                            handlePOSPrintReceipt(call, result);
                        } else if (call.method.equals("checkPrinterStatus")) {
                            checkPrinterStatus(result);
                        } else {
                            result.notImplemented();
                        }
                    }
                });

        // 初始化销售历史打印通道
        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), LIST_PRINT_CHANNEL)
                .setMethodCallHandler(new MethodChannel.MethodCallHandler() {
                    @Override
                    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
                        if (call.method.equals("printReceipt")) {
                            handlePOSPrintReceipt(call, result);
                        } else {
                            result.notImplemented();
                        }
                    }
                });
    }

    // 绑定商米打印服务
    private void bindPrinterService() {
        try {
            boolean result = InnerPrinterManager.getInstance().bindService(this, innerPrinterCallback);
            if (result) {
                Log.d("PrinterService", "Successfully bound to Sunmi Printer Service.");
            } else {
                Log.e("PrinterService", "Failed to bind to Sunmi Printer Service.");
            }
        } catch (InnerPrinterException e) {
            Log.e("PrinterService", "Failed to bind printer service: " + e.getMessage(), e);
        }
    }

    // 打印机连接回调
    private final InnerPrinterCallback innerPrinterCallback = new InnerPrinterCallback() {
        @Override
        protected void onConnected(SunmiPrinterService service) {
            sunmiPrinterService = service;
            Log.d("PrinterService", "Sunmi Printer Service connected.");
        }

        @Override
        protected void onDisconnected() {
            sunmiPrinterService = null;
            Log.d("PrinterService", "Sunmi Printer Service disconnected.");
        }
    };

    // 处理打印请求
    private void handlePrintLabel(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        try {
            // 添加日志，记录每次用的参数和时间
            Log.d("PrintService", "handlePrintLabel called with arguments: " + call.arguments + " at " + System.currentTimeMillis());
            // 进入打印事模式，清理上一次的事务内容
            sunmiPrinterService.enterPrinterBuffer(true);
            // 获取调用参数
            String productNumberLabel = call.argument("productNumberLabel");
            String productNameLabel = call.argument("productNameLabel");
            String retailPriceLabel = call.argument("retailPriceLabel");
            String memberPriceLabel = call.argument("memberPriceLabel");
            String articuloID = call.argument("articuloID");
            String nombreES = call.argument("nombreES");
            String codigoBarra = call.argument("codigoBarra");
            String precioDetalle = call.argument("precioDetalle");
            String precioMayor = call.argument("precioMayor");

            SQLiteDatabase db = openOrCreateDatabase("wktech.db", MODE_PRIVATE, null);
            Cursor cursor = db.rawQuery("SELECT store_name, labe1, labe2, print_mode, beizhu, small_labe, font, label_40x30_mode, label_58x40_mode FROM prints WHERE id = 1", null);

            if (cursor != null && cursor.moveToFirst()) {
                String storeName = cursor.getString(cursor.getColumnIndex("store_name"));
                int labe1 = cursor.getInt(cursor.getColumnIndex("labe1"));
                int labe2 = cursor.getInt(cursor.getColumnIndex("labe2"));
                int printMode = cursor.getInt(cursor.getColumnIndex("print_mode"));
                String beizhu = cursor.getString(cursor.getColumnIndex("beizhu"));
                int smallLabe = cursor.getInt(cursor.getColumnIndex("small_labe"));
                int fontSize = cursor.getInt(cursor.getColumnIndex("font"));
                int label40x30Mode = cursor.getInt(cursor.getColumnIndex("label_40x30_mode"));
                int label58x40Mode = cursor.getInt(cursor.getColumnIndex("label_58x40_mode"));
                cursor.close();

                Log.d("PrintService", "从数据库读取的设置: smallLabe=" + smallLabe + ", label40x30Mode=" + label40x30Mode + ", label58x40Mode=" + label58x40Mode + ", printMode=" + printMode);

                // 小标签模式处理30*20（支持标签模式和热敏模式）
                if (smallLabe == 1) {
                    Log.d("PrintService", "进入30*20mm标签处理分支, printMode=" + printMode);

                    if (printMode == 2) {
                        // 标签模式
                        sunmiPrinterService.labelLocate();
                    }

                    sunmiPrinterService.printText("\n", null);
                    sunmiPrinterService.setAlignment(1, null);
                    sunmiPrinterService.setFontSize(30, null);
                    sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.ENABLE);  // 开启加粗
                    sunmiPrinterService.printText(storeName + "\n", null);
                    sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.DISABLE);  // 取消加粗
                    sunmiPrinterService.setAlignment(1, null);
                    sunmiPrinterService.setFontSize(fontSize, null);
                    if (labe1 == 2 && labe2 != 2) {
                        sunmiPrinterService.printText(precioDetalle + "\n", null);
                    } else if (labe2 == 2 && labe1 != 2) {
                        sunmiPrinterService.printText(precioMayor + "\n", null);
                    }

                    if (printMode == 2) {
                        // 只有标签模式才调用labelOutput
                        sunmiPrinterService.labelOutput();
                    } else {
                        // 热敏模式下方增加2个空行
                        sunmiPrinterService.printText("\n\n", null);
                    }

                } else if (smallLabe == 2) {
                    // 40*30mm标签处理（支持标签模式和热敏模式）
                    Log.d("PrintService", "进入40*30mm标签处理分支, label40x30Mode=" + label40x30Mode + ", printMode=" + printMode);

                    if (printMode == 2) {
                        // 标签模式
                        sunmiPrinterService.labelLocate();
                    }
                    
                    if (label40x30Mode == 1) {
                        // 简化模式: 仅显示REF:货号和价格
                        Log.d("PrintService", "40*30mm使用简化模式打印: REF:" + articuloID);
                        
                        // 根据货号长度决定字体大小和布局
                        int articuloIDLength = articuloID.length();
                        
                        if (articuloIDLength <= 12) {
                            // 较短货号，使用单行格式 "REF: 货号"，居中显示
                            sunmiPrinterService.setAlignment(1, null); // 居中对齐
                            
                            // 根据"REF: 货号"的总长度动态调整字体大小，确保不超出40mm宽度
                            String fullText = "REF:" + articuloID;
                            int textLength = fullText.length();
                            
                            // 为不同长度的货号优化字体大小，特别处理6位货号
                            int refFontSize;
                            
                            // 基于实际测试结果的字体大小调整策略
                            if (articuloIDLength <= 5) {
                                refFontSize = 60; // 短货号，使用最大字体
                            } else if (articuloIDLength == 6) {
                                // 特别处理6位货号，确保不会在打印时换行
                                refFontSize = 55; // 稍微降低字体大小确保整行显示
                                Log.d("PrintService", "检测到6位货号，设置特殊字体大小: " + refFontSize);
                            } else if (articuloIDLength <= 8) {
                                refFontSize = 50; // 中等长度货号
                            } else if (articuloIDLength <= 10) {
                                refFontSize = 40; // 较长货号
                            } else {
                                refFontSize = 35; // 长货号
                            }
                            
                            // 额外检查文本是否包含较宽字符，如果存在则进一步缩小字体
                            boolean hasWideChars = false;
                            for (char c : articuloID.toCharArray()) {
                                if (c == 'W' || c == 'M' || c == 'm' || c == 'w') {
                                    hasWideChars = true;
                                    break;
                                }
                            }
                            
                            if (hasWideChars && refFontSize > 40) {
                                refFontSize -= 5; // 对于包含宽字符的情况，进一步缩小字体
                                Log.d("PrintService", "货号包含宽字符，字体调整为: " + refFontSize);
                            }
                            
                            Log.d("PrintService", "REF行长度: " + textLength + ", 货号长度: " + articuloIDLength + ", 使用字体大小: " + refFontSize);
                            sunmiPrinterService.setFontSize(refFontSize, null);
                            sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.ENABLE);
                            
                            // 直接打印 "REF: 货号" 格式
                            sunmiPrinterService.printText(fullText + "\n", null);
                            sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.DISABLE);
                        } else {
                            // 货号较长，使用两行模式，都居中显示
                            Log.d("PrintService", "货号较长，使用两行模式");
                            
                            // 第一行打印REF标识，居中对齐
                            sunmiPrinterService.setAlignment(1, null); // 居中对齐
                            sunmiPrinterService.setFontSize(60, null);
                            sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.ENABLE);
                            sunmiPrinterService.printText("REF:\n", null);
                            
                            // 第二行打印货号，居中对齐，根据长度调整字体大小
                            int refFontSize;
                            
                            // 更精确的货号长度字体大小映射
                            if (articuloIDLength > 20) {
                                refFontSize = 24; // 非常长的货号
                            } else if (articuloIDLength > 18) {
                                refFontSize = 26; // 很长的货号
                            } else if (articuloIDLength > 15) {
                                refFontSize = 30; // 较长的货号
                            } else {
                                refFontSize = 50; // 中等长度的货号
                            }
                            
                            Log.d("PrintService", "两行模式货号长度: " + articuloIDLength + ", 第二行字体大小: " + refFontSize);
                            sunmiPrinterService.setFontSize(refFontSize, null);
                            sunmiPrinterService.printText(articuloID + "\n", null);
                            sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.DISABLE);
                        }
                        
                        // 价格行保持居中显示
                        sunmiPrinterService.setAlignment(1, null); // 居中对齐
                        sunmiPrinterService.setFontSize(70, null);
                        sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.ENABLE);
                        if (labe1 == 2 && labe2 != 2) {
                            sunmiPrinterService.printText(precioDetalle + "\n", null);
                        } else if (labe2 == 2 && labe1 != 2) {
                            sunmiPrinterService.printText(precioMayor + "\n", null);
                        } else if (labe1 == 1 && labe2 == 1) {
                            // 双价模式下，优先显示零售价
                            sunmiPrinterService.printText(precioDetalle + "\n", null);
                        }
                        sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.DISABLE);
                    } else {
                        // 标准模式: 显示完整内容
                        Log.d("PrintService", "40*30mm使用标准模式打印");
                        
                        sunmiPrinterService.setAlignment(1, null);
                        sunmiPrinterService.setFontSize(26, null);
                        sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.ENABLE);  // 开启加粗
                        sunmiPrinterService.printText(storeName + "\n", null);
                        sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.DISABLE);  // 取消加粗
                        sunmiPrinterService.setAlignment(0, null);
                        sunmiPrinterService.setFontSize(20, null);
                        // 截断商品名称
                        int maxLength = 28; // 你可以根据实际字体和打印宽度调整最大长度
                        if (nombreES.length() > maxLength) {
                            nombreES = nombreES.substring(0, maxLength); // 截断字符串
                        }

                        sunmiPrinterService.printText(nombreES + "\n", null);
                        sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.ENABLE);  // 开启加粗
                        sunmiPrinterService.setAlignment(1, null);
                        sunmiPrinterService.setFontSize(70, null);
                        if (labe1 == 2 && labe2 != 2) {
                            sunmiPrinterService.printText(precioDetalle + "\n", null);
                        } else if (labe2 == 2 && labe1 != 2) {
                            sunmiPrinterService.printText(precioMayor + "\n", null);
                        }

                        sunmiPrinterService.setAlignment(2, null);
                        sunmiPrinterService.setFontSize(20, null);
                        sunmiPrinterService.printText(beizhu, null);
                        sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.DISABLE);  // 取消加粗
                        sunmiPrinterService.setAlignment(0, null);
                        sunmiPrinterService.printBarCode(codigoBarra, 2, 30, 2, 2, null);
                    }

                    if (printMode == 2) {
                        // 只有标签模式才调用labelOutput
                        sunmiPrinterService.labelOutput();
                    } else {
                        // 热敏模式下方增加2个空行
                        sunmiPrinterService.printText("\n\n", null);
                    }
                } else if (smallLabe == 3) {
                    // 58*40mm标签处理（支持标签模式和热敏模式）
                    Log.d("PrintService", "进入58*40mm标签处理分支, label58x40Mode=" + label58x40Mode + ", printMode=" + printMode);

                    if (printMode == 2) {
                        // 标签模式
                        sunmiPrinterService.labelLocate();
                    }

                    if (label58x40Mode == 1) {
                        // 货架模式: 仅显示条码和货号
                        Log.d("PrintService", "58*40mm使用货架模式打印: 货号=" + articuloID);
                        sunmiPrinterService.printText("\n", null);
                        sunmiPrinterService.setAlignment(1, null); // 居中对齐
                        sunmiPrinterService.setFontSize(100, null);
                        sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.ENABLE);
                        sunmiPrinterService.printText(articuloID + "\n", null);
                        sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.DISABLE);

                        // 打印条码
                        sunmiPrinterService.setAlignment(1, null);
                        sunmiPrinterService.printBarCode(codigoBarra, 8, 60, 2, 2, null);

                    } else if (label58x40Mode == 2) {
                        // 商品模式: 货号在上，商品名称字体小，条码后3个空白行
                        Log.d("PrintService", "58*40mm使用商品模式打印: 商品=" + nombreES + ", 货号=" + articuloID);
                        sunmiPrinterService.printText("\n", null);
                        sunmiPrinterService.setAlignment(1, null); // 居中对齐

                        // 先打印货号（大字体，加粗）
                        sunmiPrinterService.setFontSize(50, null);
                        sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.ENABLE);
                        sunmiPrinterService.printText(articuloID + "\n", null);
                        sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.DISABLE);

                        // 根据商品名称长度决定字体大小
                        int productNameFontSize;
                        if (nombreES.length() > 32) {
                            productNameFontSize = 25; // 超过32字符使用更小字体
                        } else {
                            productNameFontSize = 30; // 32字符以内使用小字体
                        }

                        // 打印商品名称（不截断）
                        sunmiPrinterService.setFontSize(productNameFontSize, null);
                        sunmiPrinterService.printText(nombreES + "\n", null);


                        // 打印条码
                        sunmiPrinterService.setAlignment(1, null);
                        sunmiPrinterService.printBarCode(codigoBarra, 8, 50, 2, 2, null);

                        // 条码后增加3个空白行
                        sunmiPrinterService.printText("\n", null);

                    } else {
                        // 标准模式: 显示完整内容
                        Log.d("PrintService", "58*40mm使用标准模式打印");

                        sunmiPrinterService.lineWrap(1, null);
                        sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.ENABLE);  // 开启加粗
                        sunmiPrinterService.setAlignment(1, null);
                        sunmiPrinterService.setFontSize(30, null);
                        sunmiPrinterService.printText(storeName + "\n", null);
                        sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.DISABLE);  // 取消加粗
                        sunmiPrinterService.setAlignment(0, null);
                        sunmiPrinterService.setFontSize(24, null);

                        // 截断商品名称
                        int maxLength = 32; // 你可以根据实际字体和打印宽度调整最大长度
                        if (nombreES.length() > maxLength) {
                            nombreES = nombreES.substring(0, maxLength); // 截断字符串
                        }

                        sunmiPrinterService.printText(productNameLabel + nombreES + "\n", null);
                        sunmiPrinterService.setAlignment(1, null);
                        sunmiPrinterService.setFontSize(100, null);
                        if (labe1 == 1 && labe2 == 1) {
                            sunmiPrinterService.printText(retailPriceLabel + precioDetalle + "  ", null);
                            sunmiPrinterService.printText(memberPriceLabel + precioMayor, null);
                            sunmiPrinterService.lineWrap(1, null);
                        } else if (labe1 == 2 && labe2 != 2) {
                            sunmiPrinterService.printText(retailPriceLabel + precioDetalle + "\n", null);
                        } else if (labe2 == 2 && labe1 != 2) {
                            sunmiPrinterService.printText(memberPriceLabel + precioMayor + "\n", null);
                        }
                        sunmiPrinterService.setAlignment(0, null);
                        sunmiPrinterService.setFontSize(20, null);
                        sunmiPrinterService.printText(beizhu, null);
                        sunmiPrinterService.setAlignment(2, null);
                        sunmiPrinterService.printBarCode(codigoBarra, 8, 20, 2, 2, null);
                    }

                    if (printMode == 2) {
                        // 只有标签模式才调用labelOutput
                        sunmiPrinterService.labelOutput();
                    } else {
                        // 热敏模式下方增加2个空行
                        sunmiPrinterService.printText("\n", null);
                    }
                } else {
                    // 其他标签和热敏打印逻辑保持不变
                    if (printMode == 2) {
                        // 标签模式打印逻辑（非58*40mm的其他尺寸）
                        sunmiPrinterService.labelLocate();
                        sunmiPrinterService.lineWrap(1, null);
                        sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.ENABLE);  // 开启加粗
                        sunmiPrinterService.setAlignment(1, null);
                        sunmiPrinterService.setFontSize(30, null);
                        sunmiPrinterService.printText(storeName + "\n", null);
                        sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.DISABLE);  // 取消加粗
                        sunmiPrinterService.setAlignment(0, null);
                        sunmiPrinterService.setFontSize(24, null);

                        // 截断商品名称
                        int maxLength = 32; // 你可以根据实际字体和打印宽度调整最大长度
                        if (nombreES.length() > maxLength) {
                            nombreES = nombreES.substring(0, maxLength); // 截断字符串
                        }

                        sunmiPrinterService.printText(productNameLabel + nombreES + "\n", null);
                        sunmiPrinterService.setAlignment(1, null);
                        sunmiPrinterService.setFontSize(100, null);
                        if (labe1 == 1 && labe2 == 1) {
                            sunmiPrinterService.printText(retailPriceLabel + precioDetalle + "  ", null);
                            sunmiPrinterService.printText(memberPriceLabel + precioMayor, null);
                            sunmiPrinterService.lineWrap(1, null);
                        } else if (labe1 == 2 && labe2 != 2) {
                            sunmiPrinterService.printText(retailPriceLabel + precioDetalle + "\n", null);
                        } else if (labe2 == 2 && labe1 != 2) {
                            sunmiPrinterService.printText(memberPriceLabel + precioMayor + "\n", null);
                        }
                        sunmiPrinterService.setAlignment(0, null);
                        sunmiPrinterService.setFontSize(20, null);
                        sunmiPrinterService.printText(beizhu, null);
                        sunmiPrinterService.setAlignment(2, null);
                        sunmiPrinterService.printBarCode(codigoBarra, 8, 20, 2, 2, null);
                        sunmiPrinterService.labelOutput();

                    } else if (printMode == 0) {
                        // 热敏模式打印逻辑58*40
                        sunmiPrinterService.setAlignment(1, null);
                        sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.ENABLE);  // 开启加粗
                        sunmiPrinterService.setFontSize(32, null);
                        sunmiPrinterService.printText(storeName + "\n", null);
                        sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.DISABLE);  // 取消加粗
                        sunmiPrinterService.setAlignment(0, null);
                        sunmiPrinterService.setFontSize(24, null);

                        // 截断商品名称
                        int maxLength = 32; // 你可以根据实际字体和打印宽度调整最大长度
                        if (nombreES.length() > maxLength) {
                            nombreES = nombreES.substring(0, maxLength); // 截断字符串
                        }

                        sunmiPrinterService.printText(productNameLabel + nombreES + "\n", null);
                        sunmiPrinterService.setAlignment(1, null);
                        sunmiPrinterService.setFontSize(95, null);
                        if (labe1 == 1 && labe2 == 1) {
                            sunmiPrinterService.printText(retailPriceLabel + precioDetalle + "  ", null);
                            sunmiPrinterService.printText(memberPriceLabel + precioMayor, null);
                            sunmiPrinterService.lineWrap(1, null);
                        } else if (labe1 == 2 && labe2 != 2) {
                            sunmiPrinterService.printText(retailPriceLabel + precioDetalle + "\n", null);
                        } else if (labe2 == 2 && labe1 != 2) {
                            sunmiPrinterService.printText(memberPriceLabel + precioMayor + "\n", null);
                        }
                        sunmiPrinterService.setAlignment(0, null);
                        sunmiPrinterService.setFontSize(20, null);
                        sunmiPrinterService.printText(beizhu, null);
                        sunmiPrinterService.setAlignment(2, null);
                        sunmiPrinterService.printBarCode(codigoBarra, 8, 30, 2, 2, null);
                        // 热敏模式下方增加2个空行
                        sunmiPrinterService.printText("\n\n", null);
                        sunmiPrinterService.lineWrap(1, null);
                    }
                }
                // 务，确保新打印内容被提交
                sunmiPrinterService.exitPrinterBuffer(true);
                result.success("Label printed successfully.");

            } else {
                if (cursor != null) {
                    cursor.close();
                }
                result.error("DB_ERROR", "Failed to retrieve print_mode and beizhu from database", null);
            }
        } catch (Exception e) {
            Log.e("PrintError", "Failed to print label: " + e.getMessage(), e);
            result.error("PRINT_ERROR", "Failed to print label: " + e.getMessage(), null);
        }
    }

    private void handlePrintReceipt(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        try {
            // 获取参数
            String saleOrderNumber = call.argument("saleOrderNumber");
            String storeName = call.argument("storeName");
            String dateTime = call.argument("dateTime");
            @SuppressWarnings("unchecked")
            ArrayList<HashMap<String, Object>> items = call.argument("items");
            double totalAmount = call.argument("totalAmount");
            double paidAmount = call.argument("paidAmount");
            double changeAmount = call.argument("changeAmount");
            String operator = call.argument("operator");

            // 打印逻辑
            if (sunmiPrinterService != null) {
                sunmiPrinterService.enterPrinterBuffer(true);
                
                // 店铺名称 (居中，大字体)
                sunmiPrinterService.setAlignment(1, null);
                sunmiPrinterService.setFontSize(24, null);
                sunmiPrinterService.printText(storeName + "\n", null);
                
                // 销售日期和单号 (居左，正常字体)
                sunmiPrinterService.setAlignment(0, null);
                sunmiPrinterService.setFontSize(18, null);
                sunmiPrinterService.printText("日期: " + dateTime + "\n", null);
                sunmiPrinterService.printText("单号: " + saleOrderNumber + "\n", null);
                
                // 分隔线
                sunmiPrinterService.printText("----------------------------------------\n", null);
                
                // 表头
                sunmiPrinterService.printText("商品          数量    单价    金额\n", null);
                sunmiPrinterService.printText("----------------------------------------\n", null);
                
                // 商品列表
                if (items != null) {
                    for (HashMap<String, Object> item : items) {
                        String name = (String) item.get("name");
                        int quantity = ((Number) item.get("quantity")).intValue();
                        double price = ((Number) item.get("price")).doubleValue();
                        double amount = ((Number) item.get("amount")).doubleValue();
                        
                        // 截断商品名称，确保不超过8个字符
                        if (name.length() > 8) {
                            name = name.substring(0, 8);
                        }
                        
                        // 格式化打印行
                        String line = String.format("%-8s %3d %8.2f %8.2f\n",
                            name, quantity, price, amount);
                        sunmiPrinterService.printText(line, null);
                    }
                }
                
                // 分隔线
                sunmiPrinterService.printText("----------------------------------------\n", null);
                
                // 合计信息
                sunmiPrinterService.setAlignment(2, null);
                sunmiPrinterService.printText(String.format("合计: %.2f\n", totalAmount), null);
                sunmiPrinterService.printText(String.format("实收: %.2f\n", paidAmount), null);
                if (changeAmount > 0) {
                    sunmiPrinterService.printText(String.format("找零: %.2f\n", changeAmount), null);
                }
                
                // 操作员信息
                sunmiPrinterService.setAlignment(0, null);
                sunmiPrinterService.setFontSize(16, null);
                sunmiPrinterService.printText("\n操作员: " + operator + "\n", null);
                
                // 页脚
                sunmiPrinterService.setAlignment(1, null);
                sunmiPrinterService.printText("\n谢谢惠顾，欢迎再次光临！\n\n", null);
                
                // 多走几行纸，替切纸功能
                sunmiPrinterService.lineWrap(6, null);
                
                sunmiPrinterService.exitPrinterBuffer(true);
                result.success("打印成功");
            } else {
                result.error("PRINTER_NOT_CONNECTED", "打印机未连接", null);
            }
        } catch (Exception e) {
            Log.e("PrintError", "打印失败: " + e.getMessage(), e);
            result.error("PRINT_ERROR", "打印失败: " + e.getMessage(), null);
        }
    }

    // 启动商米的扫码功能
    private void startScan(MethodChannel.Result result) {
        Intent intent = new Intent("com.summi.scan");
        intent.putExtra("PLAY_SOUND", true);  // 启用扫描提示音
        intent.putExtra("PLAY_VIBRATE", false);  // 禁用震动
        intent.putExtra("IS_SHOW_SETTING", true);  // 显示设置按钮
        intent.putExtra("IS_SHOW_ALBUM", false);  // 禁止从相册选择
        intent.putExtra("SCAN_MODE", false);  // 设置为单次扫描模式

        startActivityForResult(intent, 1);  // 启动扫描并期待结果
        this.scanResult = result;  // 将 result 存，以便在 onActivityResult 中返回扫描结果
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 1 && data != null) {
            Bundle bundle = data.getExtras();
            ArrayList<HashMap<String, String>> result =
                    (ArrayList<HashMap<String, String>>) bundle.getSerializable("data");

            if (result != null && scanResult != null) {
                for (HashMap<String, String> hashMap : result) {
                    String scanResultValue = hashMap.get("VALUE");  // 获取扫描结果
                    scanResult.success(scanResultValue);  // 返回结果给Flutter
                }
            }
        }
    }

    // 获取 ANDROID_ID 作为唯一标识符
    private String getAndroidId() {
        return Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
    }

    // 安装APK
    private void installApk(String filePath, String fileName, MethodChannel.Result result) {
        // 安装APK的逻辑...
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 断开商米打印服务
        try {
            InnerPrinterManager.getInstance().unBindService(this, innerPrinterCallback);
            Log.d("PrinterService", "Successfully unbound from Sunmi Printer Service.");
        } catch (InnerPrinterException e) {
            Log.e("PrinterService", "Failed to unbind printer service: " + e.getMessage(), e);
        }
    }

    // 处理 POS 打印请求
    private void handlePOSPrintReceipt(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        try {
            // 只需要从 Flutter 获取销售单号
            String saleOrderNumber = call.argument("saleOrderNumber");
            
            // 从数据库获取销售记录数据
            SQLiteDatabase db = openOrCreateDatabase("wktech.db", MODE_PRIVATE, null);
            
            // 获取销售汇总信息
            Cursor summaryCursor = db.rawQuery(
                "SELECT * FROM sale_summary WHERE sale_order_number = ?",
                new String[]{saleOrderNumber}
            );
            
            if (!summaryCursor.moveToFirst()) {
                summaryCursor.close();
                db.close();
                result.error("NOT_FOUND", "Sale record not found", null);
                return;
            }
            
            // 获取销售明细
            Cursor detailsCursor = db.rawQuery(
                "SELECT * FROM sale_details WHERE sale_order_number = ? ORDER BY sequence ASC",
                new String[]{saleOrderNumber}
            );
            
            // 获取打印设置，包括店铺名称和显示设置
            Cursor printsCursor = db.rawQuery("SELECT currency_symbol FROM prints WHERE id = 1", null);
            String currencySymbol = "€"; // 默认币符号
            if (printsCursor.moveToFirst()) {
                currencySymbol = printsCursor.getString(printsCursor.getColumnIndex("currency_symbol"));
                if (currencySymbol == null || currencySymbol.isEmpty()) {
                    currencySymbol = "€"; // 如果数据库中没有值，使用默认符号
                }
            }
            printsCursor.close();

            // 获取 POS 设置
            Cursor posSettingsCursor = db.rawQuery("SELECT store_name, show_store_name, show_order_number FROM possettings WHERE id = 1", null);
            String storeName = "";
            boolean showStoreName = false;
            boolean showOrderNumber = true; // 默认显示销售单号
            if (posSettingsCursor.moveToFirst()) {
                storeName = posSettingsCursor.getString(posSettingsCursor.getColumnIndex("store_name"));
                showStoreName = posSettingsCursor.getInt(posSettingsCursor.getColumnIndex("show_store_name")) == 1;
                showOrderNumber = posSettingsCursor.getInt(posSettingsCursor.getColumnIndex("show_order_number")) == 1;
            }
            posSettingsCursor.close();

            // 从销售汇总中获取数据
            double totalAmount = summaryCursor.getDouble(summaryCursor.getColumnIndex("original_amount")); // 使用原始金额
            double finalAmount = summaryCursor.getDouble(summaryCursor.getColumnIndex("final_amount")); // 最终金额（打折后）
            double paidAmount = summaryCursor.getDouble(summaryCursor.getColumnIndex("total_payment"));
            double changeAmount = summaryCursor.getDouble(summaryCursor.getColumnIndex("change_amount"));
            String operator = summaryCursor.getString(summaryCursor.getColumnIndex("operator"));
            double discountPercentage = summaryCursor.getDouble(summaryCursor.getColumnIndex("discount_percentage"));
            String dateTime = summaryCursor.getString(summaryCursor.getColumnIndex("created_at"));

            if (sunmiPrinterService != null) {
                sunmiPrinterService.enterPrinterBuffer(true);
                
                // 店铺名称 (居中，特大字体) - 根据 show_store_name 决定是否显示
                if (showStoreName && storeName != null && !storeName.isEmpty()) {
                    sunmiPrinterService.setAlignment(1, null);
                    sunmiPrinterService.setFontSize(40, null);
                    sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.ENABLE);
                    sunmiPrinterService.printText(storeName + "\n\n", null);
                    sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.DISABLE);
                }
                
                // 销售信息 (居左，中号字体)
                sunmiPrinterService.setAlignment(0, null);
                sunmiPrinterService.setFontSize(24, null);
                // 格式化日期时间
                String formattedDateTime = "";
                if (dateTime != null) {
                    formattedDateTime = dateTime.replace("T", " ").substring(0, 19);  // 替换T为空格并截取前19个字符
                }
                sunmiPrinterService.printText("Fecha: " + formattedDateTime + "\n", null);
                if (showOrderNumber) { // 根据设置决定是否显示销售单号
                    sunmiPrinterService.printText("No.POS" + saleOrderNumber + "\n", null);
                }
                sunmiPrinterService.printText("Operador: " + operator + "\n", null);
                
                // 获取并显示总件数
                int totalQuantity = summaryCursor.getInt(summaryCursor.getColumnIndex("total_quantity"));
                sunmiPrinterService.printText("Total piezas: " + totalQuantity + "\n", null);
                
                // 分隔
                sunmiPrinterService.printText("--------------------------------\n", null);
                
                // 表头 (全部加粗，中号字体)
                sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.ENABLE);
                sunmiPrinterService.setFontSize(24, null);
                sunmiPrinterService.printText("Producto\n", null);
                sunmiPrinterService.printText("Cant.  Precio  Desc.  Importe\n", null);
                sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.DISABLE);
                sunmiPrinterService.printText("--------------------------------\n", null);
                
                // 商品列表 (正常字体)
                sunmiPrinterService.setFontSize(24, null);
                double subtotalBeforeDiscount = 0.0;
                
                while (detailsCursor.moveToNext()) {
                    String articleId = detailsCursor.getString(detailsCursor.getColumnIndex("article_id"));
                    String name = detailsCursor.getString(detailsCursor.getColumnIndex("name"));
                    int quantity = detailsCursor.getInt(detailsCursor.getColumnIndex("quantity"));
                    double price = detailsCursor.getDouble(detailsCursor.getColumnIndex("unit_price"));
                    double discount = detailsCursor.getDouble(detailsCursor.getColumnIndex("discount"));
                    // 修正折扣金额计算
                    double amount = price * quantity * (1 - (discount / 100));  // 将折扣转换为百分比
                    
                    // 确保 article_id 不超过13位
                    if (articleId != null && articleId.length() > 13) {
                        articleId = articleId.substring(0, 13);
                    }
                    
                    // 计算可用于商品名称的最大长度
                    // 考虑到58mm打印宽度，假设每个字符平均宽度，预留适当空间
                    int maxTotalLength = 32; // 这个值可能需要根据实际打印效果调整
                    int articleIdLength = (articleId != null) ? articleId.length() : 0;
                    int maxNameLength = maxTotalLength - articleIdLength - 1; // -1 用于分隔符
                    
                    // 如果名称过长，进行截断
                    if (name.length() > maxNameLength) {
                        name = name.substring(0, maxNameLength);
                    }
                    
                    // 商品ID和名称 (加粗显示)
                    sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.ENABLE);
                    String productLine = String.format("%s %s\n", 
                        (articleId != null ? articleId : ""), 
                        name);
                    sunmiPrinterService.printText(productLine, null);
                    sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.DISABLE);
                    
                    // 打印数量、单价、折扣、金额
                    String discountStr = discount > 0 ? String.format("%.0f%%", discount) : "";
                    String line = String.format("%2d     %6.2f%s  %3s  %6.2f%s\n",
                        quantity, price, currencySymbol, discountStr, amount, currencySymbol);
                    sunmiPrinterService.printText(line, null);
                    
                    // 如果有折扣，显示折扣前金额
                    if (discount > 0) {
                        double originalAmount = price * quantity;
                        subtotalBeforeDiscount += originalAmount;
                    } else {
                        subtotalBeforeDiscount += amount;
                    }
                    
                    sunmiPrinterService.printText("\n", null);
                }
                
                // 分隔线
                sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.DISABLE);
                sunmiPrinterService.printText("--------------------------------\n", null);
                
                // 合计信息（左对齐，大字体）
                sunmiPrinterService.setAlignment(0, null);
                sunmiPrinterService.setFontSize(24, null);
                
                // 总计信息（加粗显示）
                sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.ENABLE);
                sunmiPrinterService.printText(String.format("Total:        %.2f%s\n", totalAmount, currencySymbol), null);
                
                // 如果有整单打折，显示打折信息
                if (discountPercentage > 0) {
                    double discountAmount = totalAmount - finalAmount;
                    sunmiPrinterService.printText(String.format("Descuento(%d%%): -%.2f%s\n", (int)discountPercentage, discountAmount, currencySymbol), null);
                    sunmiPrinterService.printText(String.format("Total final:  %.2f%s\n", finalAmount, currencySymbol), null);
                }
                sunmiPrinterService.printText(String.format("Pagado:        %.2f%s\n", paidAmount, currencySymbol), null);
                if (changeAmount > 0) {
                    sunmiPrinterService.printText(String.format("Cambio:        -%.2f%s\n", changeAmount, currencySymbol), null);
                }
                sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.DISABLE);
                sunmiPrinterService.printText("--------------------------------", null);
                
                // 页脚（居中，中号字体）
                sunmiPrinterService.setAlignment(1, null);
                sunmiPrinterService.setFontSize(28, null);
                sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.ENABLE);
                sunmiPrinterService.printText("\n¡Gracias por su compra!\n", null);
                sunmiPrinterService.printText("¡Bienvenido de nuevo!\n\n", null);
                sunmiPrinterService.setPrinterStyle(WoyouConsts.ENABLE_BOLD, WoyouConsts.DISABLE);
                
                // 多走几行纸
                sunmiPrinterService.lineWrap(2, null);
                
                sunmiPrinterService.exitPrinterBuffer(true);
                result.success("Impresión exitosa");
            } else {
                result.error("PRINTER_NOT_CONNECTED", "Impresora no conectada", null);
            }
            
            // 关闭游标和数据库
            summaryCursor.close();
            detailsCursor.close();
            db.close();
            
        } catch (Exception e) {
            Log.e("PrintError", "Error de impresión: " + e.getMessage(), e);
            result.error("PRINT_ERROR", "Error de impresión: " + e.getMessage(), null);
        }
    }

    // 辅助方法：左填充空格，于对齐
    private String padLeft(String str, int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = str.length(); i < length; i++) {
            sb.append(' ');
        }
        sb.append(str);
        return sb.toString();
    }

    // 添加检查打印机状态的方法
    private void checkPrinterStatus(MethodChannel.Result result) {
        try {
            if (sunmiPrinterService == null) {
                result.success(false);
                return;
            }

            // 获取打印机纸张状态
            int paperStatus = sunmiPrinterService.getPrinterPaper();
            // 获取打印机连接状态
            int printerStatus = sunmiPrinterService.updatePrinterState();

            // 检查打印机是否正常
            boolean isReady = (paperStatus == 1) && // 有纸
                            (printerStatus == 1); // 打印机正常工作

            result.success(isReady);
        } catch (Exception e) {
            Log.e("PrinterService", "检查打印机状态时出错: " + e.getMessage());
            result.success(false);
        }
    }
}
