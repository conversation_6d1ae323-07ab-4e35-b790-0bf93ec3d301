import 'dart:io';  // 用于文件操作
import 'package:path_provider/path_provider.dart';  // 获取应用目录路径
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:crypto/crypto.dart';  // 用于sha256加密
import 'package:mistoer/services/database_service_sn.dart';  // 引入本地数据库管理服务
import 'package:mistoer/services/database_service.dart'; // 引入数据库服务

class SNservice {
  static const String _primaryUrl = 'http://wktech.ltd:3000';
  static const String _backupUrl = 'http://8.152.2.183:3000';

  // 验证注册码是否有效
  static bool validateRegNumber(String serial, String regNumber) {
    String generatedCode = generateRegCode(serial);
    return generatedCode == regNumber;
  }

  // 根据序列号生成注册码
  static String generateRegCode(String serialNumber) {
    const String salt = "wukong科技";  // 自定义加盐值
    var bytes = utf8.encode(serialNumber + salt);
    var digest = sha256.convert(bytes);

    // 修改哈希以生成注册码
    String modifiedHash = digest.toString().substring(0, 5) + digest.toString().substring(6);
    String regCode = modifiedHash.substring(modifiedHash.length - 3) + modifiedHash.substring(0, modifiedHash.length - 3);

    return regCode.toUpperCase();
  }

  // 处理主 URL 和备份 URL 的请求逻辑
  static Future<http.Response> _tryRequestWithTimeout(
      String primaryUrl, String backupUrl, Function(Uri) requestFunction) async {
    try {
      final primaryResponse = await requestFunction(Uri.parse(primaryUrl)).timeout(Duration(seconds: 10));
      return primaryResponse;
    } catch (e) {
      print('Primary URL failed, switching to backup URL: $backupUrl');
      try {
        final backupResponse = await requestFunction(Uri.parse(backupUrl)).timeout(Duration(seconds: 10));
        return backupResponse;
      } catch (backupError) {
        print('Backup URL also failed: $backupError');
        rethrow;  // 抛出异常以便调用者处理
      }
    }
  }

  // 注册设备
  static Future<Map<String, dynamic>> registerDevice(String serial) async {
    final urlPath = '/registerDevice';
    final body = jsonEncode({'serial': serial});
    final headers = {'Content-Type': 'application/json'};

    try {
      print('Registering device with serial: $serial');
      final response = await _tryRequestWithTimeout(
        _primaryUrl + urlPath,
        _backupUrl + urlPath,
            (url) => http.post(url, body: body, headers: headers),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // 如果服务器返回了 image 字段，下载并替换 logo
        if (data.containsKey('image') && data['image'] != null) {
          String imageUrl = data['image'];
          await downloadAndReplaceLogo(imageUrl); // 下载并替换 logo
        }

        await saveToDatabase(serial, data);
        return data;
      } else {
        return {'error': '服务器错误: ${response.statusCode}'};
      }
    } catch (e) {
      return {'error': '无法连接服务器: $e'};
    }
  }

  // 检查设备序列号是否已注册
  static Future<Map<String, dynamic>> checkSerial(String serial) async {
    final urlPath = '/checkSerial?serial=$serial';

    try {
      final response = await _tryRequestWithTimeout(
        _primaryUrl + urlPath,
        _backupUrl + urlPath,
            (url) => http.get(url),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        print('设备序列号已存在: ${data['data']}');

        // 如果服务器返回了 image 字段，下载并替换 logo
        if (data.containsKey('image') && data['image'] != null) {
          String imageUrl = data['image'];
          await downloadAndReplaceLogo(imageUrl); // 下载并替换 logo
        }

        await saveToDatabase(serial, data);
        return data;
      } else if (response.statusCode == 404) {
        // 如果设备序列号不存在于服务器，则删除本地数据库中的注册码
        await deleteLocalRegCode(serial);
        return {'error': '设备序列号未找到，已删除本地注册码'};
      } else {
        return {'error': '查询失败: ${response.statusCode}'};
      }
    } catch (e) {
      return {'error': '请求出错: $e'};
    }
  }

  // 下载并替换 logo.png
  static Future<void> downloadAndReplaceLogo(String imageUrl) async {
    try {
      // 发起 GET 请求获取图片数据
      final response = await http.get(Uri.parse(imageUrl));
      if (response.statusCode == 200) {
        // 获取应用程序的文档目录路径
        Directory appDocDir = await getApplicationDocumentsDirectory();
        String logoPath = '${appDocDir.path}/logo.png';  // logo.png 的存储路径

        // 将获取到的图片数据写入文件
        File logoFile = File(logoPath);
        await logoFile.writeAsBytes(response.bodyBytes);

        print('Logo 下载并替换成功: $logoPath');
      } else {
        print('图片下载失败，状态码: ${response.statusCode}');
      }
    } catch (e) {
      print('下载或替换 logo 时发生错误: $e');
    }
  }

  // 保存数据到数据库并处理 bohao 字段
  static Future<void> saveToDatabase(String serial, Map<String, dynamic> response) async {
    try {
      // 直接从 response['data'] 获取数据，因为它是一个对象而不是数组
      if (response.containsKey('data') && response['data'] != null) {
        var data = response['data'];  // 直接获取 data 对象

        // 确保解析正确
        String corporatename = data['corporatename'] ?? '未知公司';
        String regnumber = data['regnumber'] ?? '';
        String shopid = data['shopid'] ?? '';
        int mayorista = (data['mayorista'] ?? 0) == 1 ? 1 : 0;
        int minorista = (data['minorista'] ?? 0) == 1 ? 1 : 0;
        int esPrueba = (data['esPrueba'] ?? 0) == 1 ? 1 : 0;
        String? tiempoPrueba = data['tiempoPrueba'];  // 保留 null 值
        int bohao = (data['bohao']?? 0) == 1 ? 1 : 0;

        // 打印解析结果，确保字段正确
        print('解析后的 corporatename: $corporatename');
        print('解析后的 regnumber: $regnumber');
        print('解析后的 shopid: $shopid');
        print('解析后的 mayorista: $mayorista');
        print('解析后的 minorista: $minorista');
        print('解析后的 esPrueba: $esPrueba');
        print('解析后的 tiempoPrueba: $tiempoPrueba');
        print('解析后的 bohao: $bohao');

        // 将数据保存到数据库
        await DatabaseServiceSN.upsertServiceRegRecord(
          sn: serial,
          regnumber: regnumber,
          corporatename: corporatename,
          isOk: true, // 表示注册成功
          mayorista: mayorista,
          minorista: minorista,
          esPrueba: esPrueba,
          tiempoPrueba: tiempoPrueba,
          shopid: shopid,
          bohao: bohao,  // 保存 bohao
        );

        print('数据保存成功: SN=$serial, 公司名称=$corporatename, 注册码=$regnumber, shopid=$shopid, mayorista=$mayorista, minorista=$minorista, esPrueba=$esPrueba, tiempoPrueba=${tiempoPrueba ?? 'null'}, bohao=$bohao');
      } else {
        print('服务器返回的数据为空或无效');
      }
    } catch (e) {
      print('保存到数据库时发生错误: $e');
    }
  }

  // 删除本地数据库中的注册码
  static Future<void> deleteLocalRegCode(String serial) async {
    try {
      await DatabaseServiceSN.deleteServiceRegRecord(serial);
      print('本地注册码已删除: SN=$serial');
    } catch (e) {
      print('删除本地注册码时发生错误: $e');
    }
  }
  // 检查应用版本更新
  static Future<Map<String, dynamic>> checkForUpdate() async {
    final urlPath = '/checkUpdate';  // 服务器版本检查API路径

    try {
      final response = await _tryRequestWithTimeout(
        _primaryUrl + urlPath,
        _backupUrl + urlPath,
            (url) => http.get(url),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        print('最新版本信息: ${data['latest_version']}');
        return data;  // 返回最新的版本信息
      } else {
        return {'error': '服务器错误: ${response.statusCode}'};
      }
    } catch (e) {
      return {'error': '无法连接服务器: $e'};
    }
  }

  // 获取设备对应的shopip
  static Future<Map<String, dynamic>> getShopIpBySerial(String serial) async {
    try {
      final urlPath = '/getShopIpBySerial';
      final body = jsonEncode({'serial': serial});
      final headers = {'Content-Type': 'application/json'};

      print('获取设备shopip，序列号: $serial');
      final response = await _tryRequestWithTimeout(
        _primaryUrl + urlPath,
        _backupUrl + urlPath,
        (url) => http.post(url, body: body, headers: headers),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        print('获取到shopip响应: $responseData');
        return responseData;
      } else {
        return {'success': false, 'message': '服务器错误: ${response.statusCode}'};
      }
    } catch (e) {
      print('获取shopip时出错: $e');
      return {'success': false, 'message': e.toString()};
    }
  }
}
