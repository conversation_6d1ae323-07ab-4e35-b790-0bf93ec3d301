import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:async';
import 'package:mistoer/services/database_service.dart';
import 'package:mistoer/print/order_detail_print.dart';
import 'package:mistoer/services/database_service_sn.dart'; // 导入定义 OrderDetail 的文件

class ApiPrintService {
  static const int port = 3000;
  static const Duration timeoutDuration = Duration(seconds: 10);

  // 获取服务器的基础 URL
  static Future<String> getBaseUrl() async {
    final ipOrDomain = await DatabaseService.getIp(); // 从数据库服务中获取IP地址或域名

    // 检查IP地址或域名是否有效
    if (ipOrDomain == null || ipOrDomain.isEmpty) {
      throw Exception('无法获取有效的IP地址或域名');
    }

    String url;

    // 判断域名是否已包含协议
    if (ipOrDomain.startsWith('http://') || ipOrDomain.startsWith('https://')) {
      // 如果已经包含协议，则直接使用域名，不加端口
      url = ipOrDomain;
    } else if (RegExp(r'^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(ipOrDomain)) {
      // 如果是有效的域名，使用 http 协议并不加端口
      url = 'http://$ipOrDomain'; // 仅使用域名，不加端口
    } else {
      // 对于 IP 地址，添加协议和端口
      url = 'http://$ipOrDomain:$port';
    }

    return url; // 返回最终构建的 URL
  }

  /// 创建新商品
  static Future<bool> createArticulo(Map<String, dynamic> articuloData) async {
    try {
      print('Sending articuloData: ${json.encode(articuloData)}');
      final baseUrl = await getBaseUrl();
      final url = Uri.parse('$baseUrl/create_articulo');

      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode(articuloData),
      ).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        final decodedResponse = json.decode(response.body);
        return decodedResponse['success'] ?? false;
      } else {
        print('Failed to create articulo. Status code: ${response.statusCode}');
        return false;
      }
    } on TimeoutException {
      print('Request timed out');
      return false;
    } catch (e) {
      print('An error occurred: $e');
      return false;
    }
  }

  // 根据ArticuloID获取文章信息
  static Future<Map<String, dynamic>?> fetchArticuloData(String articuloID) async {
    try {
      final baseUrl = await getBaseUrl();
      final url = Uri.parse('$baseUrl/get_articulo?articuloID=$articuloID');

      final response = await http.get(url).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        print('Failed to fetch data. Status code: ${response.statusCode}');
        return null;
      }
    } on TimeoutException {
      print('Request timed out');
      return null;
    } catch (e) {
      print('An error occurred: $e');
      return null;
    }
  }

  // 根据条形码获取文章信息及库存信息
  static Future<Map<String, dynamic>?> fetchArticuloDataByBarcode(String barcode) async {
    try {
      // 优先使用本地数据库查询
      try {
        final localRow = await DatabaseService.getArticuloByBarcode(barcode);
        if (localRow != null) {
          print('本地查询成功，条码: $barcode');
          return localRow;
        }
      } catch (e) {
        print('本地查询失败: $e');
      }
      
      // 本地查询失败时，尝试在线查询
      try {
        final baseUrl = await getBaseUrl();
        final url = Uri.parse('$baseUrl/get_articulo_by_barcode?barcode=$barcode');

        final response = await http.get(url).timeout(timeoutDuration);

        if (response.statusCode == 200) {
          return json.decode(response.body);
        } else {
          print('Failed to fetch data. Status code: ${response.statusCode}');
        }
      } on TimeoutException {
        print('Request timed out');
      } catch (e) {
        print('Online query error: $e');
      }
      
      return null;
    } catch (e) {
      print('An error occurred: $e');
      return null;
    }
  }

  // 获取类别列表
  static Future<List<Map<String, dynamic>>?> fetchArticuloClase() async {
    try {
      final baseUrl = await getBaseUrl();
      final url = Uri.parse('$baseUrl/get_articulo_clase');

      final response = await http.get(url).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        return jsonData.cast<Map<String, dynamic>>();
      } else {
        print('Failed to fetch data. Status code: ${response.statusCode}');
        return null;
      }
    } on TimeoutException {
      print('Request timed out');
      return null;
    } catch (e) {
      print('An error occurred: $e');
      return null;
    }
  }

  // 更新文章信息
  static Future<bool> updateArticulo(Map<String, dynamic> articuloData) async {
    try {
      final baseUrl = await getBaseUrl();
      final url = Uri.parse('$baseUrl/update_articulo');

      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode(articuloData),
      ).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        final decodedResponse = json.decode(response.body);
        return decodedResponse['success'] ?? false;
      } else {
        print('Failed to update data. Status code: ${response.statusCode}');
        return false;
      }
    } on TimeoutException {
      print('Request timed out');
      return false;
    } catch (e) {
      print('An error occurred: $e');
      return false;
    }
  }

  // 更新商品库存
  static Future<bool> updateStock(String articuloID, int newStock) async {
    final baseUrl = await getBaseUrl(); // 获取基础 URL
    final bohao = await DatabaseServiceSN.getBohaoValue();
    final stockField = (bohao == 1) ? 'stock' : 'Stock'; // 根据bohao的值判断字段名

    try {
      // 创建一个Map并动态添加 stockField
      Map<String, dynamic> body = {
        'ArticuloID': articuloID,
      };
      body[stockField] = newStock; // 动态设置stockField字段
      final response = await http.post(
        Uri.parse('$baseUrl/update_articulostock'),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode(body), // 将Map进行jsonEncode
      ).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        return true;
      } else {
        print('Error: Failed to update stock with status code: ${response.statusCode}');
        return false;
      }
    } on TimeoutException {
      print('Request timed out');
      return false;
    } catch (e) {
      print('An error occurred: $e');
      return false;
    }
  }

  // 获取未完成的入库单信息
  static Future<List<Map<String, dynamic>>?> fetchUncompletedOrders() async {
    try {
      final baseUrl = await getBaseUrl();
      final url = Uri.parse('$baseUrl/uncompleted_orders');

      final response = await http.get(url).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        return jsonData.cast<Map<String, dynamic>>();
      } else {
        print('Failed to fetch uncompleted orders. Status code: ${response.statusCode}');
        return null;
      }
    } on TimeoutException {
      print('Request timed out');
      return null;
    } catch (e) {
      print('An error occurred: $e');
      return null;
    }
  }

  // 根据 AlbaranProveedorNo 获取对应的订单详情数据
  static Future<List<OrderDetailPrint>> fetchAlbaranProveedor(String albaranProveedorNo) async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/get_albaran_data');

    try {
      final response = await http.post(
        url,
        body: json.encode({'albaranProveedorNo': albaranProveedorNo}),
        headers: {'Content-Type': 'application/json'},
      ).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        return await _processResponse(response);
      } else {
        print('Error response status: ${response.statusCode}');
        throw Exception('Failed to load albaran data');
      }
    } catch (e) {
      print('Error fetching albaran data: $e');
      throw Exception('Failed to load albaran data');
    }
  }

  static Future<List<OrderDetailPrint>> _processResponse(http.Response response) async {
    try {
      List<dynamic> data = json.decode(response.body);
      print('Albaran data: $data');

      List<OrderDetailPrint> orderDetails = data.map((item) {
        print('Processing item: $item');
        return OrderDetailPrint.fromJson(item as Map<String, dynamic>);
      }).toList();

      return orderDetails;
    } catch (e) {
      print('JSON decode error: $e');
      throw Exception('Failed to load albaran data');
    }
  }

  //
  static Future<void> updateCheckedValue({
    required String albaranProveedorNo,
    required String articuloID,
    required int checked,
  }) async {
    try {
      // 通过类名调用静态方法来获取 bohao 值
      final bohaoValue = await DatabaseServiceSN.getBohaoValue();

      // 根据 bohao 的值决定字段名
      final String keyName = (bohaoValue != null && bohaoValue == 1) ? 'DocumentoNo' : 'AlbaranProveedorNo';

      // 获取基础 URL，并根据 bohao 决定 URL 路径
      final baseUrl = await getBaseUrl();
      final String updateEndpoint = (bohaoValue != null && bohaoValue == 1)
          ? '/update_checked_value2'
          : '/update_checked_value';
      final url = Uri.parse('$baseUrl$updateEndpoint');

      // 构建要发送的数据
      final Map<String, dynamic> data = {
        keyName: albaranProveedorNo, // 动态设置字段名
        'ArticuloID': articuloID,
        'Checked': checked,
      };

      // 发送 HTTP POST 请求到服务器
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode(data),
      ).timeout(timeoutDuration);

      // 处理响应
      if (response.statusCode == 200) {
        print('Checked value updated successfully for ArticuloID: $articuloID');
      } else {
        print('Failed to update checked value. Status code: ${response.statusCode}');
        throw Exception('Failed to update checked value');
      }
    } on TimeoutException {
      print('Request to update checked value timed out');
      throw Exception('Request timed out');
    } catch (e) {
      print('An error occurred while updating checked value: $e');
      throw Exception('Failed to update checked value');
    }
  }

  static Future<void> updateFinishedStatus(String albaranProveedorNo, int status) async {
    try {
      // 通过类名调用静态方法来获取 bohao 值
      final bohaoValue = await DatabaseServiceSN.getBohaoValue();

      // 根据 bohao 的值决定字段名
      final String keyName = (bohaoValue != null && bohaoValue == 1) ? 'DocumentoNo' : 'AlbaranProveedorNo';

      // 获取基础 URL，并根据 bohao 决定 URL 路径
      final baseUrl = await getBaseUrl();
      final String updateEndpoint = (bohaoValue != null && bohaoValue == 1)
          ? '/update_finished_status2'
          : '/update_finished_status';
      final url = Uri.parse('$baseUrl$updateEndpoint');

      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
           keyName:  albaranProveedorNo,
          'FINISHED': status,
        }),
      );

      if (response.statusCode == 200) {
        print('Finished status updated successfully.');
      } else {
        print('Failed to update finished status. Status code: ${response.statusCode}');
        throw Exception('Failed to update finished status.');
      }
    } catch (e) {
      print('An error occurred while updating finished status: $e');
      throw Exception('Failed to update finished status.');
    }
  }
  static Future<bool> checkBarcodeExistsInDatabase(String barcode) async {
    try {
      final baseUrl = await getBaseUrl();  // Ensure this is correct
      final url = Uri.parse('$baseUrl/check_barcode?barcode=$barcode');  // Ensure the endpoint is correct

      print('Requesting URL: $url');  // Log the full URL

      final response = await http.get(url).timeout(Duration(seconds: 10));

      if (response.statusCode == 200) {
        final decodedResponse = json.decode(response.body);
        return decodedResponse['exists'] ?? false;
      } else {
        print('Failed to check barcode existence. Status code: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('An error occurred: $e');
      return false;
    }
  }
// 从服务器获取价格数据 (使用 GET 请求)
  static Future<Map<String, dynamic>?> fetchPrecioData({
    required String articuloID,
    required String codigoBarra,
  }) async {
    try {
      final baseUrl = await getBaseUrl();
      // 构建 URL 和查询参数
      final url = Uri.parse('$baseUrl/get_precio_data?articuloID=$articuloID&codigoBarra=$codigoBarra');

      // 日志打印
      print('Fetching price data for: ArticuloID=$articuloID, CodigoBarra=$codigoBarra');

      // 发起 GET 请求
      final response = await http.get(url).timeout(timeoutDuration);

      // 处理服务器响应
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        print('Server response: $data'); // 打印服务器返回的数据

        // 检查请求是否成功
        if (data['success'] == true) {
          final fetchedData = {
            'PrecioDetalle': data['data']['PrecioDetalle'], // 从返回数据中获取键
            'PrecioMayor': data['data']['PrecioMayor'],     // 从返回数据中获取键
          };
          print('Fetched price data: $fetchedData'); // 打印获取到的价格数据
          return fetchedData;
        } else {
          print('Fetch price data failed: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        print('Failed to fetch price data. Status code: ${response.statusCode}');
      }
    } on TimeoutException {
      print('Request to fetch price data timed out');
    } catch (e) {
      print('An error occurred while fetching price data: $e');
    }
    return null;
  }

// 更新零售价和批发价到服务器 (使用 GET 请求)
  static Future<bool> updatePrecio({
    required String articuloID,
    required double precioDetalle,
    required double precioMayor,
  }) async {
    try {
      final baseUrl = await getBaseUrl();
      // 构建 URL 和查询参数
      final url = Uri.parse('$baseUrl/update_precio?articuloID=$articuloID&precioDetalle=$precioDetalle&precioMayor=$precioMayor');

      // 日志打印
      print('Updating price for ArticuloID=$articuloID, PrecioDetalle=$precioDetalle, PrecioMayor=$precioMayor');

      // 发起 GET 请求
      final response = await http.get(url).timeout(timeoutDuration);

      // 处理服务器响应
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        print('Server response: $data'); // 打印服务器返回的数据
        return data['success'] ?? false;
      } else {
        print('Failed to update price. Status code: ${response.statusCode}');
      }
    } on TimeoutException {
      print('Request to update price timed out');
    } catch (e) {
      print('An error occurred while updating price: $e');
    }
    return false;
  }

  // 获取供应商名称
  static Future<Map<String, dynamic>?> fetchProveedorName(String proveedorId) async {
    try {
      if (proveedorId == null || proveedorId.isEmpty) {
        return null;
      }
      
      final baseUrl = await getBaseUrl();
      final bohaoValue = await DatabaseServiceSN.getBohaoValue();
      
      // 根据bohao值选择不同的API端点和参数名
      final endpoint = bohaoValue == 1 ? '/get_empresa_name' : '/get_proveedor_name';
      final paramName = bohaoValue == 1 ? 'empresaID' : 'proveedorId';
      
      // 构建URL
      final url = Uri.parse('$baseUrl$endpoint?$paramName=$proveedorId');
      
      // 添加日志，便于调试
      print('获取供应商信息，使用接口: $endpoint, 参数: $paramName=$proveedorId');

      final response = await http.get(url).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        // 直接返回服务器响应数据，后端已处理编码转换
        final data = json.decode(response.body);
        print('获取供应商信息成功: $data');
        return data;
      } else {
        print('Failed to fetch proveedor name. Status code: ${response.statusCode}');
        return null;
      }
    } on TimeoutException {
      print('Request timed out');
      return null;
    } catch (e) {
      print('An error occurred: $e');
      return null;
    }
  }

}
