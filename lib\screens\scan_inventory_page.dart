import 'package:flutter/material.dart';
import 'package:mistoer/services/scan_api_service.dart';
import 'package:intl/intl.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:mistoer/services/database_service_sn.dart';
import 'package:mistoer/services/database_service.dart';
import 'package:mistoer/print/api_print_service.dart';
import 'package:mistoer/widgets/custom_keyboard.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class ScanInventoryPage extends StatefulWidget {
  @override
  _ScanInventoryPageState createState() => _ScanInventoryPageState();
}

class _ScanInventoryPageState extends State<ScanInventoryPage> {
  final TextEditingController _controller = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final AudioPlayer _audioPlayer = AudioPlayer();
  List<Map<String, dynamic>> _results = [];
  List<Map<String, dynamic>> _filteredResults = [];
  bool _isLoading = false;
  bool _isSearchVisible = false;
  final NumberFormat stockFormat = NumberFormat('0'); // 不显示小数点
  final NumberFormat priceFormat = NumberFormat('0.0'); // 显示一位小数点
  String _highlightedSitioEnAlmacen = ''; // 用于存储需要高亮显示的库位信息
  String _inputValue = ''; // 用于存储用户输入的值
  String _noDataMessage = ''; // 用于存储无数据提示信息
  String _stockColumnName = 'Stock'; // 默认库存列名
  int? _poderCambioStock; // 添加权限变量
  bool _isKeyboardVisible = false; // 添加键盘显示状态控制

  @override
  void initState() {
    super.initState();
    _setStockColumnName();
    _loadUserPermissions();
    
    // 添加延迟聚焦，确保在页面构建完成后执行
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
      setState(() {
        _isKeyboardVisible = false; // 修改为false，默认不显示键盘
      });
    });
  }

  Future<void> _loadUserPermissions() async {
    int? poderCambioStock = await DatabaseService.getPoderCambioStock();
    setState(() {
      _poderCambioStock = poderCambioStock;
    });
  }

  Future<void> _setStockColumnName() async {
    int? bohaoValue = await DatabaseServiceSN.getBohaoValue();
    setState(() {
      _stockColumnName = (bohaoValue ?? 0) == 1 ? 'stock' : 'Stock';
      print('Stock column name set to: $_stockColumnName'); // 调试打印库存名称
    });
  }

  void _hideKeyboard() {
    FocusScope.of(context).requestFocus(FocusNode());
  }

  // 输入框提交时的处理逻辑
  Future<void> _queryDatabase(String query) async {
    if (query.isEmpty) {
      // 输入框为空时不查询
      setState(() {
        _noDataMessage = '输入不能为空';
      });
      return;
    }

    _hideKeyboard(); // 隐藏键盘
    setState(() {
      _isLoading = true;
      _results.clear();
      _filteredResults.clear();
      _highlightedSitioEnAlmacen = '';
      _inputValue = query; // 保存用户输入的值
      _noDataMessage = '';  // 清除无数据提示信息
    });

    String queryType;
    if (query.contains('-')) {
      queryType = 'location';
    } else if (query.length < 10) {
      queryType = 'reference'; // 货号查询
    } else {
      queryType = 'barcode'; // 条码查询
    }

    try {
      List<Map<String, dynamic>> data = await ScanApiService.queryDatabase(query, queryType);
      
      // 如果是库位查询且无结果，尝试查询 weizhi_1 和 weizhi_2
      if (data.isEmpty && queryType == 'location') {
        print('No results with weizhi query, trying weizhi_1 and weizhi_2 queries');
        // 创建一个副本的 ScanApiService.queryDatabase 方法的自定义版本，用于查询 weizhi_1 和 weizhi_2
        // 这里假设服务端已经修改支持了 weizhi_1 和 weizhi_2 的查询
        // 实际情况可能需要调整服务端代码
        List<Map<String, dynamic>> dataWeizhi1 = await _queryLocationWithField(query, 'weizhi_1');
        if (dataWeizhi1.isNotEmpty) {
          data = dataWeizhi1;
        } else {
          List<Map<String, dynamic>> dataWeizhi2 = await _queryLocationWithField(query, 'weizhi_2');
          if (dataWeizhi2.isNotEmpty) {
            data = dataWeizhi2;
          }
        }
      }
      // 如果以reference方式查询无结果，尝试以货号方式再次查询
      else if (data.isEmpty && queryType == 'barcode') {
        print('No results with barcode query, trying reference query');
        data = await ScanApiService.queryDatabase(query, 'reference');
      }
      // 如果以reference方式查询无结果，尝试以条码方式再次查询
      else if (data.isEmpty && queryType == 'reference') {
        print('No results with reference query, trying barcode query');
        data = await ScanApiService.queryDatabase(query, 'barcode');
      }
      
      // 打印获取JSON数据
      data.forEach((element) => print('Queried data element: $element')); // 调试打印每个返回的数据元素
      print('Queried data: $data'); // 调试打印返回的数据
      setState(() {
        _results = data;
        _filteredResults = data;

        if (data.isEmpty) {
          // 查询无结果处理
          if (queryType == 'location') {
            _noDataMessage = '没有在该库位存放商品\nNo hay artículos en esta ubicación';
          } else {
            _noDataMessage = '无此商品数据\nNo hay datos de este producto';
          }
        } else {
          // 查询成功，检查是否有库位信息
          if (!query.contains('-') && data.isNotEmpty) {
            // 修改这里：如果SitioEnAlmacen为空或只有空格，设置为特殊值
            String sitio = data.first['SitioEnAlmacen']?.toString() ?? '';
            if (sitio.trim().isEmpty) {
              _highlightedSitioEnAlmacen = 'NO DATA\n没有设置库位';
            } else {
              _highlightedSitioEnAlmacen = sitio;
            }
          }
        }
      });

      // Play sound on successful query
      if (data.isNotEmpty) {
        await _audioPlayer.play(AssetSource('scan.mp3'));
      }

      _controller.clear(); // 清空输入框内容
      _focusNode.requestFocus(); // 重新聚焦输入框
    } catch (e) {
      print('Error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 辅助方法，用于查询特定库位字段
  Future<List<Map<String, dynamic>>> _queryLocationWithField(String query, String field) async {
    try {
      final baseUrl = await ScanApiService.getBaseUrl();
      final url = Uri.parse('$baseUrl/query');
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'queryType': 'location_custom',
          'value': query,
          'field': field,
        }),
      );
      if (response.statusCode == 200) {
        final data = List<Map<String, dynamic>>.from(jsonDecode(response.body));
        print('Fetched $field data: $data');
        return data;
      }
    } catch (e) {
      print('Error querying $field: $e');
    }
    return [];
  }

  Future<void> _searchDatabase() async {
    String query = _searchController.text;
    setState(() {
      _isLoading = true;
    });

    try {
      List<Map<String, dynamic>> filteredResults = _results.where((result) {
        return result.values.any((value) => value.toString().contains(query));
      }).toList();

      setState(() {
        _filteredResults = filteredResults;
      });
    } catch (e) {
      print('Error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showStockEditDialog(Map<String, dynamic> itemData) {
    if (_poderCambioStock == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('您没有权限修改库存')),
      );
      return;
    }

    final TextEditingController _stockController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('修改库存', style: TextStyle(fontSize: 22)),
          content: TextField(
            controller: _stockController,
            keyboardType: TextInputType.number,
            style: TextStyle(fontSize: 20),
            decoration: InputDecoration(
              labelText: '输入新的库存数量',
              labelStyle: TextStyle(fontSize: 18),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _focusNode.requestFocus();
              },
              child: Text('取消', style: TextStyle(fontSize: 18)),
            ),
            ElevatedButton(
              onPressed: () async {
                try {
                  final newStock = int.parse(_stockController.text);
                  final articuloID = itemData['ArticuloID'].toString();

                  bool success = await ApiPrintService.updateStock(articuloID, newStock);

                  if (success) {
                    setState(() {
                      // 更新列表中的库存值
                      final index = _results.indexOf(itemData);
                      if (index != -1) {
                        _results[index][_stockColumnName] = newStock;
                        _filteredResults = List.from(_results);
                      }
                    });
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('库存更新成功')),
                    );
                    _focusNode.requestFocus();
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('库存更新失败，请重试')),
                    );
                  }
                } catch (e) {
                  print('Error updating stock: $e');
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('库存更新失败，请检查输入')),
                  );
                  _focusNode.requestFocus();
                }
              },
              child: Text('保存', style: TextStyle(fontSize: 18)),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('库位查询'),
        leading: BackButton(),
        actions: [
          IconButton(
            icon: Icon(_isSearchVisible ? Icons.search_off : Icons.search),
            onPressed: () {
              setState(() {
                _isSearchVisible = !_isSearchVisible;
                if (!_isSearchVisible) {
                  _searchController.clear();
                  _filteredResults = List.from(_results);
                }
              });
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _isKeyboardVisible = false;
                });
              },
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    TextField(
                      controller: _controller,
                      focusNode: _focusNode,
                      showCursor: true,
                      readOnly: false,  // 修改为false，允许输入
                      enableInteractiveSelection: false, // 禁用文本选择功能
                      keyboardType: TextInputType.none, // 禁用系统键盘
                      decoration: InputDecoration(
                        labelText: '扫描或输入',
                        border: OutlineInputBorder(),
                        suffixIcon: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: Icon(_isKeyboardVisible ? Icons.keyboard_hide : Icons.keyboard),
                              onPressed: () {
                                setState(() {
                                  _isKeyboardVisible = !_isKeyboardVisible;
                                });
                              },
                            ),
                          ],
                        ),
                      ),
                      onChanged: (value) {
                        // 处理扫描设备的输入（通常以回车或换行符结束）
                        if (value.contains('\n') || value.contains('\r')) {
                          String query = value.replaceAll('\n', '').replaceAll('\r', '').trim();
                          if (query.isNotEmpty) {
                            _queryDatabase(query);
                          }
                        }
                      },
                      onSubmitted: (value) {
                        // 处理回车键提交
                        if (value.isNotEmpty) {
                          _queryDatabase(value);
                        }
                      },
                    ),
                    SizedBox(height: 10),
                    if (_isSearchVisible)
                      TextField(
                        controller: _searchController,
                        readOnly: true,
                        decoration: InputDecoration(
                          labelText: '搜索',
                          border: OutlineInputBorder(),
                          suffixIcon: IconButton(
                            icon: Icon(Icons.search),
                            onPressed: _searchDatabase,
                          ),
                        ),
                      ),
                    if (_isLoading) 
                      CircularProgressIndicator(),
                    if (!_isLoading && _noDataMessage.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          _noDataMessage,
                          style: TextStyle(color: Colors.red, fontSize: 18.0),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    if (!_isLoading && _results.isNotEmpty)
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            children: [
                              if (_highlightedSitioEnAlmacen.isNotEmpty)
                                _buildHighlightedInfo(),
                              _buildResultTable(),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
          if (_isKeyboardVisible)
            CustomKeyboard(
              controller: _controller,
              onSubmit: _queryDatabase,
              showKeyboard: _isKeyboardVisible,
              onKeyboardVisibilityChanged: () {
                setState(() {
                  _isKeyboardVisible = !_isKeyboardVisible;
                });
              },
            ),
        ],
      ),
    );
  }

  Widget _buildResultTable() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        headingRowHeight: 30.0,
        dataRowMinHeight: 30.0,
        dataRowMaxHeight: 30.0,
        columnSpacing: 20.0,
        horizontalMargin: 10.0,
        dividerThickness: 1.0,
        showCheckboxColumn: false,
        columns: [
          DataColumn(label: Text('库位', style: TextStyle(fontWeight: FontWeight.bold))),
          DataColumn(label: Text('条形码', style: TextStyle(fontWeight: FontWeight.bold))),
          DataColumn(label: Text('货号', style: TextStyle(fontWeight: FontWeight.bold))),
          DataColumn(label: Text('库存', style: TextStyle(fontWeight: FontWeight.bold))),
          DataColumn(label: Container(
            alignment: Alignment.centerLeft,
            child: Text('主备货', 
              style: TextStyle(fontWeight: FontWeight.bold),
              textAlign: TextAlign.left,
              softWrap: true,
            ),
          )),
          DataColumn(label: Container(
            alignment: Alignment.centerLeft,
            child: Text('备货1', 
              style: TextStyle(fontWeight: FontWeight.bold),
              textAlign: TextAlign.left,
              softWrap: true,
            ),
          )),
          DataColumn(label: Container(
            alignment: Alignment.centerLeft,
            child: Text('备货2', 
              style: TextStyle(fontWeight: FontWeight.bold),
              textAlign: TextAlign.left,
              softWrap: true,
            ),
          )),
          DataColumn(label: Text('总箱数', style: TextStyle(fontWeight: FontWeight.bold))),
        ],
        rows: _filteredResults.map((data) {
          return DataRow(
            cells: [
              DataCell(
                Text(
                  data['SitioEnAlmacen'] == _inputValue
                      ? data['SitioEnAlmacen'] ?? 'NO DATA\n没有设置库位'
                      : data['weizhi'] ?? 'NO DATA\n没有设置库位',
                  style: TextStyle(
                    color: data['SitioEnAlmacen'] == _inputValue ? Colors.red : Colors.black,
                    fontSize: 12.0,
                  ),
                ),
                onTap: () => _showStockEditDialog(data),
              ),
              DataCell(
                Text(data['CodigoBarra'] ?? '', 
                style: TextStyle(color: Colors.black, fontSize: 12.0)),
                onTap: () => _showStockEditDialog(data),
              ),
              DataCell(
                Text(data['ArticuloID'] ?? '', 
                style: TextStyle(color: Colors.black)),
                onTap: () => _showStockEditDialog(data),
              ),
              DataCell(
                Text(stockFormat.format(double.tryParse(data[_stockColumnName].toString()) ?? 0)),
                onTap: () => _showStockEditDialog(data),
              ),
              DataCell(
                Container(
                  alignment: Alignment.centerLeft,
                  child: Text(data['caja']?.toString() ?? '0'),
                ),
                onTap: () => _showStockEditDialog(data),
              ),
              DataCell(
                Container(
                  alignment: Alignment.centerLeft,
                  child: Text(data['caja1']?.toString() ?? '0'),
                ),
                onTap: () => _showStockEditDialog(data),
              ),
              DataCell(
                Container(
                  alignment: Alignment.centerLeft,
                  child: Text(data['caja2']?.toString() ?? '0'),
                ),
                onTap: () => _showStockEditDialog(data),
              ),
              DataCell(
                Text(data['total_caja']?.toString() ?? '0'),
                onTap: () => _showStockEditDialog(data),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildHighlightedInfo() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Align(
        alignment: Alignment.center,
        child: GestureDetector(
          onTap: () {
            if (_results.isNotEmpty) {
              // 获取库存数并确保是整数形式
              int stockValue = double.tryParse(_results.first[_stockColumnName].toString())?.round() ?? 0;

              // 显示库存提示框，停留3秒
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('当前总库存数量: $stockValue', style: TextStyle(fontSize: 18)),
                  duration: Duration(seconds: 2), // 显示3秒
                ),
              );
            }
          },
          child: Container(
            width: 350, // Custom width
            height: 250, // 增加高度以容纳更多库位信息
            decoration: BoxDecoration(
              border: Border.all(color: Colors.red, width: 2.0),
              borderRadius: BorderRadius.circular(12),
            ),
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start, // 让内容从顶部开始对齐
              crossAxisAlignment: CrossAxisAlignment.start, // 修改为左侧对齐
              children: [
                // 拣货区部分
                Row(
                  mainAxisAlignment: MainAxisAlignment.start, // 修改为左侧对齐
                  children: [
                    Icon(Icons.shopping_cart, color: Colors.blue, size: 40.0), // 拣货区的图标
                    SizedBox(width: 10),
                    // 修改逻辑：如果SitioEnAlmacen为空，显示其他库位信息
                    _buildTopLocationText(),
                  ],
                ),
                // 添加银色横向分隔线
                Divider(
                  color: Colors.grey, // 线条颜色为银色
                  thickness: 2.0, // 线条的粗细
                  height: 20.0, // 线条的高度间距
                ),
                // 主备货区部分
                Row(
                  mainAxisAlignment: MainAxisAlignment.start, // 修改为左侧对齐
                  children: [
                    Icon(Icons.inventory, color: Colors.green, size: 40.0), // 使用 inventory 图标作为箱子图标
                    SizedBox(width: 10),
                    Text(
                      _results.isNotEmpty && _results.first['weizhi'] != null && _results.first['weizhi'].toString().isNotEmpty
                          ? '主备货: ${_results.first['weizhi']}' // 显示主备货区 (weizhi)
                          : '主备货: 没有设置', // 显示 NO DATA
                      style: TextStyle(
                        color: _results.isNotEmpty && _results.first['weizhi'] != null && _results.first['weizhi'].toString().isNotEmpty
                            ? Colors.green // 如果有 weizhi，使用绿色字体
                            : Colors.red, // 如果没有 weizhi，使用红色字体
                        fontSize: _results.isNotEmpty && _results.first['weizhi'] != null && _results.first['weizhi'].toString().isNotEmpty
                            ? 22.0 // 如果有 weizhi，字体大小为22
                            : 16.0, // 如果没有 weizhi，字体大小为16
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.left, // 修改为左侧对齐
                    ),
                  ],
                ),
                // 库位1部分
                Row(
                  mainAxisAlignment: MainAxisAlignment.start, // 修改为左侧对齐
                  children: [
                    Icon(Icons.inventory_2, color: Colors.orange, size: 30.0), // 使用不同的图标区分库位
                    SizedBox(width: 10),
                    Text(
                      _results.isNotEmpty && _results.first['weizhi_1'] != null && _results.first['weizhi_1'].toString().isNotEmpty
                          ? '备货1: ${_results.first['weizhi_1']}' // 显示库位1 (weizhi_1)
                          : '备货1: 没有设置',
                      style: TextStyle(
                        color: _results.isNotEmpty && _results.first['weizhi_1'] != null && _results.first['weizhi_1'].toString().isNotEmpty
                            ? Colors.orange
                            : Colors.grey,
                        fontSize: 20.0,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.left, // 修改为左侧对齐
                    ),
                  ],
                ),
                // 库位2部分
                Row(
                  mainAxisAlignment: MainAxisAlignment.start, // 修改为左侧对齐
                  children: [
                    Icon(Icons.inventory_2, color: Colors.purple, size: 30.0), // 使用不同的图标区分库位
                    SizedBox(width: 10),
                    Text(
                      _results.isNotEmpty && _results.first['weizhi_2'] != null && _results.first['weizhi_2'].toString().isNotEmpty
                          ? '备货2: ${_results.first['weizhi_2']}' // 显示库位2 (weizhi_2)
                          : '备货2: 没有设置',
                      style: TextStyle(
                        color: _results.isNotEmpty && _results.first['weizhi_2'] != null && _results.first['weizhi_2'].toString().isNotEmpty
                            ? Colors.purple
                            : Colors.grey,
                        fontSize: 20.0,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.left, // 修改为左侧对齐
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 添加新方法来构建顶部位置文本
  Widget _buildTopLocationText() {
    // 如果SitioEnAlmacen有值且不是"没有设置库位"，则显示它
    if (_highlightedSitioEnAlmacen.isNotEmpty && _highlightedSitioEnAlmacen != 'NO DATA\n没有设置库位') {
      return Text(
        '拣货区: $_highlightedSitioEnAlmacen',
        style: TextStyle(color: Colors.blue, fontSize: 25.0, fontWeight: FontWeight.bold),
      );
    }
    
    // 用户要求：如果拣货区没有数值或为空，直接显示"没有设置"
    return Text(
      '拣货区: 没有设置',
      style: TextStyle(color: Colors.red, fontSize: 25.0, fontWeight: FontWeight.bold),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _searchController.dispose();
    _focusNode.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }
}
