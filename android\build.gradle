// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext {
        // Kotlin版本
        kotlin_version = '1.9.0'  // 您可以根据需要更新为合适的版本
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath "com.android.tools.build:gradle:7.3.1"  // 降级到更稳定的版本
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        // 如果使用了其他插件，如Hilt、Google服务等，可以在此添加相应的classpath
        // classpath 'com.google.dagger:hilt-android-gradle-plugin:2.44'
        // classpath 'com.google.gms:google-services:4.3.15'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}

subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
