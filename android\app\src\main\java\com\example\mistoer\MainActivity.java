package com.example.mistoer;

import android.content.ComponentName;
import android.content.Intent;
import android.content.ServiceConnection;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import android.os.IBinder;
import android.provider.Settings;
import android.util.Log;

import androidx.annotation.NonNull;

import io.flutter.embedding.android.FlutterActivity;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

import com.sunmi.peripheral.printer.SunmiPrinterService;
import com.sunmi.peripheral.printer.WoyouConsts;
import com.sunmi.peripheral.printer.InnerPrinterManager;
import com.sunmi.peripheral.printer.InnerPrinterCallback;
import com.sunmi.peripheral.printer.InnerPrinterException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.io.File;
import android.os.Build;
import android.os.Environment;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Typeface;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;
import com.journeyapps.barcodescanner.BarcodeEncoder;

public class MainActivity extends FlutterActivity {
    private static final String CHANNEL = "com.example.mistoer/print";  // 打印标签通道
    private static final String SERIAL_CHANNEL = "com.example.mistoer/serial";  // 序列号通道
    private static final String INSTALL_CHANNEL = "com.example.mistoer/install";  // APK 安装通道
    private PrintUtil printUtil;  // 打印工具实例

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 初始化 PrintUtil 实例用于打印
        printUtil = PrintUtil.getInstance(this);

        // 设置打印机事件监听
        printUtil.setPrintEventListener(new PrintUtil.PrinterBinderListener() {
            @Override
            public void onPrintCallback(int state) {
                handlePrintCallback(state);
            }

            @Override
            public void onVersion(String version) {
                Log.d("PrintDebug", "Printer version: " + version);
            }
        });

        // 初始化 MethodChannel 用于处理 Flutter 的打印请求
        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), CHANNEL)
                .setMethodCallHandler(new MethodChannel.MethodCallHandler() {
                    @Override
                    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
                        if (call.method.equals("printLabel")) {
                            handlePrintLabel(call, result);
                        } else {
                            result.notImplemented();
                        }
                    }
                });

        // 初始化 MethodChannel 用于获取 Android 序列号
        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), SERIAL_CHANNEL)
                .setMethodCallHandler(new MethodChannel.MethodCallHandler() {
                    @Override
                    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
                        if (call.method.equals("getDeviceSerial")) {
                            String androidId = getAndroidId();
                            if (androidId != null) {
                                result.success(androidId);
                            } else {
                                result.error("UNAVAILABLE", "Failed to get Android ID", null);
                            }
                        } else {
                            result.notImplemented();
                        }
                    }
                });

        // 初始化 MethodChannel 用于处理 APK 安装请求
        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), INSTALL_CHANNEL)
                .setMethodCallHandler((call, result) -> {
                    if (call.method.equals("installApk")) {
                        String filePath = call.argument("filePath");  // 接收存储路径
                        String fileName = call.argument("fileName");  // 接收文件名
                        installApk(filePath, fileName, result);
                    } else {
                        result.notImplemented();
                    }
                });
    }

    // 获取 ANDROID_ID 作为唯一标识符
    private String getAndroidId() {
        return Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
    }

    // 安装APK，并终止现有的应用
    private void installApk(String filePath, String fileName, MethodChannel.Result result) {
        try {
            File apkFile = new File(filePath);
            if (apkFile.exists()) {
                Log.d("APK Install", "Installing APK from: " + filePath + " with file name: " + fileName);

                android.os.Process.killProcess(android.os.Process.myPid());

                Intent intent = new Intent(Intent.ACTION_VIEW);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    Uri apkUri = FileProvider.getUriForFile(this, getPackageName() + ".provider", apkFile);
                    intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
                    intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                } else {
                    intent.setDataAndType(Uri.fromFile(apkFile), "application/vnd.android.package-archive");
                }
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivity(intent);
            } else {
                result.error("FILE_NOT_FOUND", "APK file not found at " + filePath, null);
            }
        } catch (Exception e) {
            Log.e("APK Install", "Failed to install APK: " + e.getMessage(), e);
            result.error("INSTALL_ERROR", "Failed to install APK: " + e.getMessage(), null);
        }
    }

    // 终止当前应用的所有进程
    private void terminateAppProcesses() {
        ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> appProcesses = activityManager.getRunningAppProcesses();
        for (ActivityManager.RunningAppProcessInfo processInfo : appProcesses) {
            if (processInfo.processName.equals(getPackageName())) {
                android.os.Process.killProcess(processInfo.pid);
            }
        }
    }

    // 处理打印逻辑
    private void handlePrintLabel(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        try {
            String productNumberLabel = call.argument("productNumberLabel");
            String productNameLabel = call.argument("productNameLabel");
            String retailPriceLabel = call.argument("retailPriceLabel");
            String memberPriceLabel = call.argument("memberPriceLabel");
            String articuloID = call.argument("articuloID");
            String nombreES = call.argument("nombreES");
            String codigoBarra = call.argument("codigoBarra");
            String precioDetalle = call.argument("precioDetalle");
            String precioMayor = call.argument("precioMayor");

            SQLiteDatabase db = openOrCreateDatabase("wktech.db", MODE_PRIVATE, null);
            Cursor cursor = db.rawQuery("SELECT store_name, labe1, labe2, print_mode, beizhu, small_labe, font FROM prints WHERE id = 1", null);

            if (cursor != null && cursor.moveToFirst()) {
                String storeName = cursor.getString(cursor.getColumnIndex("store_name"));
                int labe1 = cursor.getInt(cursor.getColumnIndex("labe1"));
                int labe2 = cursor.getInt(cursor.getColumnIndex("labe2"));
                int printMode = cursor.getInt(cursor.getColumnIndex("print_mode"));
                String beizhu = cursor.getString(cursor.getColumnIndex("beizhu"));
                int smallLabe = cursor.getInt(cursor.getColumnIndex("small_labe"));
                int fontSize = cursor.getInt(cursor.getColumnIndex("font"));
                cursor.close();

                printUtil.setUnwindPaperLen (70);//设置回纸距离
                printUtil.printEnableMark(true);
                printUtil.printConcentration(35);
                printUtil.printText(PrintConfig.Align.ALIGN_CENTER, 5, true, false,  storeName + "\n" );
                printUtil.printText(PrintConfig.Align.ALIGN_LEFT, 3, false, false, nombreES+ "\n");
                printUtil.printText("\n");

                if (labe1 == 2 && labe2 != 2) {
                    printUtil.printText(PrintConfig.Align.ALIGN_CENTER, 8, true, false, precioDetalle +"\n" );
                } else if (labe2 == 2 && labe1 != 2) {
                    printUtil.printText(PrintConfig.Align.ALIGN_CENTER, 8, true, false, precioMayor +"\n" );
                } else if (labe1 == 1 && labe2 == 1) {
                    printUtil.printText(PrintConfig.Align.ALIGN_LEFT, 8, true, false, precioDetalle +"   " );
                    printUtil.printText(PrintConfig.Align.ALIGN_RIGHT, 8, true, false, precioMayor+"\n" );
                }
                printUtil.printText(PrintConfig.Align.ALIGN_RIGHT, 3, false, false, beizhu+ "\n");
                     printUtil.printBarcode(PrintConfig.Align.ALIGN_CENTER, 50, codigoBarra, 73, 3);
                printUtil.start();
                result.success("Label printed successfully.");
            } else {
                if (cursor != null) {
                    cursor.close();
                }
                result.error("DB_ERROR", "Failed to retrieve labe1 and labe2 from database", null);
            }
        } catch (Exception e) {
            Log.e("PrintError", "Failed to print label: " + e.getMessage(), e);
            result.error("PRINT_ERROR", "Failed to print label: " + e.getMessage(), null);
        }
    }

    // 处理打印回调逻辑
    private void handlePrintCallback(int state) {
        switch (state) {
            case PrintConfig.IErrorCode.ERROR_NO_ERROR:
                showToast("Print successful");
                break;
            case PrintConfig.IErrorCode.ERROR_PRINT_NOPAPER:
                showToast("No paper");
                break;
            case PrintConfig.IErrorCode.ERROR_DATA_INPUT:
                showToast("Input parameter error");
                break;
            default:
                showToast("Printer error: " + state);
                break;
        }
    }

    // 显示 Toast 消息
    private void showToast(String message) {
        runOnUiThread(() -> Toast.makeText(MainActivity.this, message, Toast.LENGTH_SHORT).show());
    }
}
