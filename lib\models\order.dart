class Order {
  final String pedidoKey;
  final String name;
  final String riqi;
  final double amount;
  final String note;
  final double fulfillmentRate;
  final String beizhu;


  Order({
    required this.pedidoKey,
    required this.name,
    required this.riqi,
    required this.amount,
    this.note = '',
    this.fulfillmentRate = 0.0,
    this.beizhu = '',

  });

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      pedidoKey: json['pedidoKey'] as String? ?? '',
      name: json['name'] as String? ?? '',
      riqi: json['riqi'] as String? ?? '',
      amount: json['amount'] is double
          ? json['amount']
          : double.tryParse(json['amount'].toString()) ?? 0.0,
      note: json['note'] as String? ?? '',
      fulfillmentRate: (json['fulfillmentRate'] as double?) ?? 0.0,
      beizhu: json['beizhu'] as String? ?? '',

    );
  }

  Order copyWith({
    String? pedidoKey,
    String? name,
    String? riqi,
    double? amount,
    String? note,
    double? fulfillmentRate,
    String? beizhu,

  }) {
    return Order(
      pedidoKey: pedidoKey ?? this.pedidoKey,
      name: name ?? this.name,
      riqi: riqi ?? this.riqi,
      amount: amount ?? this.amount,
      note: note ?? this.note,
      fulfillmentRate: fulfillmentRate ?? this.fulfillmentRate,
      beizhu: beizhu ?? this.beizhu,

    );
  }
}
