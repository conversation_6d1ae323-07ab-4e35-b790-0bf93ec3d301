import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'api_print_service.dart'; // 导入 API 服务，用于与在线数据库交互
import 'package:mistoer/services/database_service.dart';

class AlbaranComparePage extends StatefulWidget {
  final String albaranProveedorNo;

  AlbaranComparePage({required this.albaranProveedorNo});

  @override
  _AlbaranComparePageState createState() => _AlbaranComparePageState();
}

class _AlbaranComparePageState extends State<AlbaranComparePage> {
  bool isLocalMode = false; // 用于切换同步和本地模式
  bool isTableMode = false; // 默认进入卡片模式
  ValueNotifier<List<Map<String, dynamic>>> filteredData = ValueNotifier([]);
  TextEditingController _searchController = TextEditingController(); // 搜索框的控制器
  bool _isSearching = false; // 控制搜索框显示的布尔值

  @override
  void initState() {
    super.initState();
    _loadModePreference();
    _fetchAlbaranDetails();
    _searchController.addListener(_onSearchChanged); // 添加搜索监听器
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadModePreference() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      isLocalMode = prefs.getBool('isLocalMode') ?? false;
    });
  }

  Future<void> _saveModePreference(bool isLocal) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isLocalMode', isLocal);
  }

  Future<void> _fetchAlbaranDetails() async {
    List<Map<String, dynamic>> data = await DatabaseService.getAlbaranDetailsByProveedorNo(widget.albaranProveedorNo);
    filteredData.value = data; // 初始化时加载全部数据
  }

  void _onSearchChanged() {
    _filterData(_searchController.text); // 当输入内容改变时，调用过滤函数
  }

  // 实时模糊匹配货号
  void _filterData(String query) {
    if (query.isEmpty) {
      _fetchAlbaranDetails(); // 查询为空时，加载全部数据
    } else {
      List<Map<String, dynamic>> currentData = filteredData.value;
      filteredData.value = currentData
          .where((item) =>
      item['ArticuloID'].toString().toLowerCase().contains(query.toLowerCase()) &&
          (item['Checked'] - item['Cantidad']) != 0) // 模糊匹配货号，过滤掉差异为0的记录
          .toList();
    }
  }

  // 清空搜索框
  void _clearSearch() {
    _searchController.clear();
    _filterData('');
  }

  Future<void> _syncCheckedValues(BuildContext context) async {
    try {
      final localData = await DatabaseService.getAlbaranDetailsByProveedorNo(widget.albaranProveedorNo);
      for (var item in localData) {
        final articuloID = item['ArticuloID'];
        final checked = item['Checked'];
        await ApiPrintService.updateCheckedValue(
          albaranProveedorNo: widget.albaranProveedorNo,
          articuloID: articuloID,
          checked: checked,
        );
      }

      // 先更新完成状态
      await ApiPrintService.updateFinishedStatus(widget.albaranProveedorNo, 1);

      // 如果更新完成状态成功，再删除本地数据
      await DatabaseService.deleteAlbaranDetailsByProveedorNo(widget.albaranProveedorNo);
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('同步成功，已删除本地数据'),
          backgroundColor: Colors.green[700],
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
      Navigator.pop(context, true);
    } catch (e) {
      print("同步失败: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('同步失败: ${e.toString()}'),
          backgroundColor: Colors.red[700],
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  Future<void> _completeLocalCheck(BuildContext context) async {
    final bool? shouldDelete = await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('确认删除'),
          content: Text('完成比对后，是否删除本地数据？\n此功能不会调整服务器库存。'),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          actions: [
            TextButton(
              child: Text('取消'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey[700],
              ),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            TextButton(
              child: Text('删除'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red[700],
              ),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    );

    if (shouldDelete == true) {
      try {
        await ApiPrintService.updateFinishedStatus(widget.albaranProveedorNo, 1);
        await DatabaseService.deleteAlbaranDetailsByProveedorNo(widget.albaranProveedorNo);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('比对完成，已删除本地数据'),
            backgroundColor: Colors.green[700],
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
        Navigator.pop(context, true);
      } catch (e) {
        print('操作失败: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('比对失败'),
            backgroundColor: Colors.red[700],
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  Future<void> _showConfirmationDialog(BuildContext context) async {
    if (isLocalMode) {
      // 本地模式下直接调用本地完成方法，无需提示
      _completeLocalCheck(context);
    } else {
      // 仅在同步模式下显示库存扣减提示
      final bool? isAgreed = await showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text('库存扣减'),
            content: Text('此功能将根据比对的差值调整库存，是否同意继续操作？'),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            actions: [
              TextButton(
                child: Text('取消'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.grey[700],
                ),
                onPressed: () {
                  Navigator.of(context).pop(false); // 返回 false 表示取消操作
                },
              ),
              TextButton(
                child: Text('同意'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.blue[700],
                ),
                onPressed: () {
                  Navigator.of(context).pop(true); // 返回 true 表示同意操作
                },
              ),
            ],
          );
        },
      );

      if (isAgreed == true) {
        _syncCheckedValues(context);
      }
    }
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('核对详情'),
        elevation: 0,
        actions: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Row(
              children: [
                Text(isLocalMode ? '本地模式' : '同步模式', 
                    style: TextStyle(fontSize: 12)),
                Switch(
                  value: isLocalMode,
                  activeColor: Colors.blue[700],
                  onChanged: (bool newValue) {
                    setState(() {
                      isLocalMode = newValue;
                      _saveModePreference(isLocalMode);
                    });
                  },
                ),
              ],
            ),
          ),
          IconButton(
            icon: Icon(isTableMode ? Icons.view_list : Icons.grid_on),
            tooltip: isTableMode ? '切换到卡片视图' : '切换到表格视图',
            onPressed: () {
              setState(() {
                isTableMode = !isTableMode;
              });
            },
          ),
          IconButton(
            icon: Icon(Icons.search),
            tooltip: _isSearching ? '关闭搜索' : '搜索货号',
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) _clearSearch();
              });
            },
          ),
        ],
      ),
      body: Container(
        color: Colors.grey[50],
        child: Column(
          children: [
            if (_isSearching)
              Container(
                color: Colors.white,
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    labelText: '搜索货号',
                    labelStyle: TextStyle(color: Colors.blue[800]),
                    prefixIcon: Icon(Icons.search, color: Colors.blue[800]),
                    suffixIcon: IconButton(
                      icon: Icon(Icons.clear, color: Colors.grey[600]),
                      onPressed: _clearSearch,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10.0),
                      borderSide: BorderSide(color: Colors.blue[200]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10.0),
                      borderSide: BorderSide(color: Colors.blue[500]!, width: 2),
                    ),
                    filled: true,
                    fillColor: Colors.grey[50],
                  ),
                ),
              ),
            _buildCorrectCount(filteredData.value),
            Expanded(
              child: ValueListenableBuilder<List<Map<String, dynamic>>>(
                valueListenable: filteredData,
                builder: (context, data, child) {
                  if (data.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.search_off, size: 48, color: Colors.grey[400]),
                          SizedBox(height: 16),
                          Text(
                            '没有找到相关记录',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return isTableMode
                      ? _buildScrollableDataTable(data)
                      : _buildCardList(data);
                },
              ),
            ),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 5,
                    offset: Offset(0, -2),
                  ),
                ],
              ),
              child: ElevatedButton(
                onPressed: () {
                  _showConfirmationDialog(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: isLocalMode ? Colors.green[600] : Colors.blue[700],
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  elevation: 2,
                ),
                child: Text(
                  isLocalMode ? '本地完成' : '库存扣减',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardList(List<Map<String, dynamic>> data) {
    return ListView.builder(
      padding: EdgeInsets.all(12.0),
      itemCount: data.length,
      itemBuilder: (context, index) {
        final item = data[index];
        final difference = item['Checked'] - item['Cantidad'];
        if (difference == 0) return SizedBox.shrink();

        final textColor = difference < 0 ? Colors.red[700] : Colors.green[700];
        final backgroundColor = difference < 0 
            ? Colors.red[50] 
            : (difference > 0 ? Colors.green[50] : Colors.white);

        return Card(
          margin: EdgeInsets.only(bottom: 12.0),
          elevation: 2,
          shadowColor: Colors.black26,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: difference < 0 
                    ? Colors.red[200]! 
                    : (difference > 0 ? Colors.green[200]! : Colors.grey[300]!),
                width: 1,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          '货号: ${item['ArticuloID']}',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[800],
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: backgroundColor,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: difference < 0 
                                ? Colors.red[200]! 
                                : (difference > 0 ? Colors.green[200]! : Colors.grey[300]!),
                          ),
                        ),
                        child: Text(
                          '差异: $difference',
                          style: TextStyle(
                            fontSize: 16,
                            color: textColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Divider(
                    color: Colors.grey[300],
                    thickness: 1,
                    height: 24,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildInfoColumn('价格', item['Precio'].toString()),
                      _buildInfoColumn('订购数量', item['Cantidad'].toString()),
                      _buildInfoColumn('已核对数量', item['Checked'].toString()),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoColumn(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 6),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
      ],
    );
  }

  Widget _buildScrollableDataTable(List<Map<String, dynamic>> data) {
    return Container(
      color: Colors.white,
      child: SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: DataTable(
            headingRowColor: MaterialStateProperty.all(Colors.blue[50]),
            dataRowColor: MaterialStateProperty.resolveWith<Color?>(
              (Set<MaterialState> states) {
                if (states.contains(MaterialState.selected)) {
                  return Colors.blue[100]!.withOpacity(0.3);
                }
                return null;
              },
            ),
            columnSpacing: 16.0,
            horizontalMargin: 16.0,
            headingRowHeight: 48.0,
            dataRowMinHeight: 48.0,
            dataRowMaxHeight: 64.0,
            border: TableBorder.all(
              color: Colors.grey[300]!,
              width: 1,
              borderRadius: BorderRadius.circular(8),
            ),
            columns: const <DataColumn>[
              DataColumn(
                label: Text(
                  '货号',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              DataColumn(
                label: Text(
                  '价格',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              DataColumn(
                label: Text(
                  '订购数量',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              DataColumn(
                label: Text(
                  '已核对数量',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              DataColumn(
                label: Text(
                  '差异',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
            rows: data.where((item) => item['Checked'] - item['Cantidad'] != 0).map<DataRow>((item) {
              final difference = item['Checked'] - item['Cantidad'];
              final textColor = difference < 0 ? Colors.red[700] : Colors.green[700];

              return DataRow(cells: <DataCell>[
                DataCell(Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Text(
                    item['ArticuloID'].toString(),
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                )),
                DataCell(Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Text(item['Precio'].toString()),
                )),
                DataCell(Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Text(item['Cantidad'].toString()),
                )),
                DataCell(Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Text(item['Checked'].toString()),
                )),
                DataCell(
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      color: difference < 0 
                          ? Colors.red[50] 
                          : (difference > 0 ? Colors.green[50] : Colors.transparent),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      difference.toString(),
                      style: TextStyle(
                        color: textColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ]);
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildCorrectCount(List<Map<String, dynamic>> data) {
    final correctRecords = data.where((item) => item['Cantidad'] == item['Checked']).length;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey[200]!,
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 3,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(Icons.check_circle, color: Colors.green[600], size: 24),
          ),
          SizedBox(width: 12),
          Text(
            '核对正确: $correctRecords 条',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }
}
