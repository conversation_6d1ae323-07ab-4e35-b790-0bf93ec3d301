import 'package:flutter/material.dart';
import 'package:mistoer/services/database_service.dart';

class SettingsScreen extends StatefulWidget {
  @override
  _SettingsScreenState createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final TextEditingController _ipController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadIp();
  }

  void _loadIp() async {
    String? ip = await DatabaseService.getIp();
    if (ip != null) {
      _ipController.text = ip;
    }
  }

  void _saveIp() async {
    await DatabaseService.setIp(_ipController.text);
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('IP地址已保存')));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('设置'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextField(
              controller: _ipController,
              decoration: InputDecoration(
                labelText: '服务器IP地址',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 16.0),
            ElevatedButton(
              onPressed: _saveIp,
              child: Text('保存'),
            ),
          ],
        ),
      ),
    );
  }
}
