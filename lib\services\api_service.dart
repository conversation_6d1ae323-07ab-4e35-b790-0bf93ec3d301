import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:async';
import 'package:mistoer/models/order.dart';
import 'package:mistoer/models/order_detail.dart';
import 'package:mistoer/services/database_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ApiService {
  static const int port = 3000;
  static const Duration timeoutDuration = Duration(seconds: 10);

  // 获取服务器的基础 URL
  static Future<String?> getBaseUrl({bool useHttps = false}) async {
    final ipOrDomain = await DatabaseService.getIp();
    if (ipOrDomain != null && ipOrDomain.isNotEmpty) {
      final protocol = useHttps ? 'https' : 'http';

      // 检查是否是已包含协议的域名
      if (ipOrDomain.startsWith('http://') || ipOrDomain.startsWith('https://')) {
        return ipOrDomain; // 直接返回包含协议的域名
      }

      // 判断是否是有效的域名
      if (RegExp(r'^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(ipOrDomain)) {
        return '$protocol://$ipOrDomain'; // 仅使用域名，不加端口
      }

      // 对于 IP 地址，添加协议和端口
      if (ipOrDomain.contains(':') && !ipOrDomain.contains('[')) {
        return '$protocol://[$ipOrDomain]:$port'; // IPv6 地址用方括号括起来
      }

      return '$protocol://$ipOrDomain:$port'; // IPv4 地址
    }
    return null; // 返回 null 表示无法获取有效的 IP 地址或域名
  }

  // 检查 IP 地址或域名的可用性
  static Future<bool> checkIpOrDomain(String ipOrDomain, {bool useHttps = false}) async {
    String url;

    // 检查域名是否包含协议
    if (ipOrDomain.startsWith('http://') || ipOrDomain.startsWith('https://')) {
      url = '$ipOrDomain/ping'; // 直接使用它，不加端口
    } else {
      final protocol = useHttps ? 'https' : 'http';

      // 判断是否是有效的域名
      if (RegExp(r'^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(ipOrDomain)) {
        url = '$protocol://$ipOrDomain/ping'; // 域名不需要端口
      } else if (ipOrDomain.contains(':') && !ipOrDomain.contains('[')) {
        url = '$protocol://[$ipOrDomain]:$port/ping'; // IPv6 地址
      } else {
        url = '$protocol://$ipOrDomain:$port/ping'; // IPv4 地址
      }
    }

    try {
      final response = await http.get(Uri.parse(url)).timeout(timeoutDuration);
      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');
      if (response.statusCode == 200) {
        return true; // 可用
      } else {
        print('Error: Server responded with status code ${response.statusCode}');
      }
    } catch (e) {
      print('Error checking IP or Domain: $e');
    }
    return false; // 不可用
  }

// 验证 shopid
  static Future<Map<String, dynamic>> getShopIdFromServer() async {
    final baseUrl = await getBaseUrl();  // 确保 baseUrl 正确返回
    if (baseUrl != null) {
      final url = Uri.parse('$baseUrl/getShopId');  // 使用有效的 baseUrl 拼接完整的 URL
      print('正在请求 shopid 的 URL: $url');  // 输出调试信息

      try {
        final response = await http.get(url).timeout(Duration(seconds: 10));
        if (response.statusCode == 200) {
          return json.decode(response.body);  // 假设服务器返回的 body 是 JSON 格式
        } else {
          print('服务器响应异常，状态码: ${response.statusCode}');
          return {'error': '服务器响应异常'};
        }
      } catch (e) {
        print('请求 shopid 时出错: $e');
        return {'error': '网络错误或超时'};
      }
    } else {
      print('无法获取有效的 Base URL');
      return {'error': 'Base URL 无效'};
    }
  }

  static Future<Map<String, String>> checkForUpdate({bool useHttps = false}) async {
    final baseUrl = await getBaseUrl(useHttps: useHttps);
    if (baseUrl != null) {
      final url = Uri.parse('$baseUrl/api/latest-version');
      try {
        final response = await http.get(url).timeout(timeoutDuration);
        if (response.statusCode == 200) {
          final jsonResponse = json.decode(response.body);
          return {
            'latest_version': jsonResponse['latest_version'],
            'update_url': jsonResponse['update_url'],
          };
        }
      } catch (e) {
        print('Error checking for update: $e');
      }
    }
    return {};
  }

  static Future<bool> login(String username, String password, {bool useHttps = false}) async {
    final baseUrl = await getBaseUrl(useHttps: useHttps);
    if (baseUrl != null) {
      try {
        final response = await http.post(
          Uri.parse('$baseUrl/login'),
          body: {
            'username': username,
            'password': password,
          },
        ).timeout(timeoutDuration);

        print('Login response status: ${response.statusCode}');
        print('Login response body: ${response.body}');

        if (response.statusCode == 200) {
          try {
            var jsonResponse = json.decode(response.body);
            if (jsonResponse['message'] == 'Login successful') {
              await DatabaseService.saveUserFields(jsonResponse); // 直接保存所有用户信息
              final prefs = await SharedPreferences.getInstance();
              await prefs.setInt('PoderMantener', jsonResponse['PoderMantener']);

              // 保存 PoderCambioStock 和 PoderMantenerArticulo
              int poderCambioStock = jsonResponse['PoderCambioStock'] ?? 0;
              int poderMantenerArticulo = jsonResponse['PoderMantenerArticulo'] ?? 0;
              await prefs.setInt('PoderCambioStock', poderCambioStock);
              await prefs.setInt('PoderMantenerArticulo', poderMantenerArticulo);

              // 更新用户的 revision 和 Revisor
              int revision = jsonResponse['revision'] ?? -1;
              int revisor = jsonResponse['Revisor'] ?? 0;
              await DatabaseService.updateUserRevisionAndRevisor(username, revision, revisor);
              // 确保离线可用：总是用本次输入密码的 MD5 更新本地
              try {
                await DatabaseService.updateUserPassword(username, password);
              } catch (_) {}

              return true;
            }
          } catch (e) {
            print('JSON decode error during login: $e');
            return false;
          }
        } else if (response.statusCode == 401) {
          print('Invalid username or password');
          return false;
        } else if (response.statusCode == 403) {
          print('Access denied: PoderPDA is not -1');
          return false;
        }
      } catch (e) {
        print('Error during login: $e');
      }
    }
    return false;
  }

  static Future<List<Order>> fetchOrders({required int zhuangtai, bool useHttps = false}) async {
    final baseUrl = await getBaseUrl(useHttps: useHttps);
    if (baseUrl != null) {
      try {
        final response = await http.get(Uri.parse('$baseUrl/orders?zhuangtai=$zhuangtai')).timeout(timeoutDuration);

        if (response.statusCode == 200) {
          try {
            List<dynamic> data = json.decode(response.body);
            return data.map((item) => Order.fromJson(item)).toList();
          } catch (e) {
            print('JSON decode error: $e');
            throw Exception('Failed to load orders');
          }
        } else {
          print('Error response status: ${response.statusCode}');
          throw Exception('Failed to load orders');
        }
      } catch (e) {
        print('Error fetching orders: $e');
        throw Exception('Failed to load orders');
      }
    }
    throw Exception('Base URL is null');
  }

  static Future<List<OrderDetail>> fetchOrderDetails(String pedidoKey, {bool useHttps = false}) async {
    final baseUrl = await getBaseUrl(useHttps: useHttps);
    if (baseUrl != null) {
      try {
        final response = await http.post(
          Uri.parse('$baseUrl/order_details'),
          body: json.encode({'pedidoKey': pedidoKey}),
          headers: {'Content-Type': 'application/json'},
        ).timeout(Duration(seconds: 200));

        if (response.statusCode == 200) {
          try {
            List<dynamic> data = json.decode(response.body);
            print('Order details data: $data');

            // 解析数据并将 name 映射到 name_ce
            List<OrderDetail> orderDetails = data.map((item) {
              var orderDetailJson = item as Map<String, dynamic>;
              orderDetailJson['name_ce'] = orderDetailJson.remove('name');
              return OrderDetail.fromJson(orderDetailJson);
            }).toList();

            // 将 orderDetails 插入到本地数据库
            for (var orderDetail in orderDetails) {
              await DatabaseService.insertOrderDetail(orderDetail);
            }

            return orderDetails;
          } catch (e) {
            print('JSON decode error: $e');
            throw Exception('Failed to load order details');
          }
        } else {
          print('Error response status: ${response.statusCode}');
          throw Exception('Failed to load order details');
        }
      } catch (e) {
        print('Error fetching order details: $e');
        throw Exception('Failed to load order details');
      }
    }
    throw Exception('Base URL is null');
  }

  static Future<void> updateOrderStatus(String pedidoKey, num zhuangtai, {bool useHttps = false}) async {
    final baseUrl = await getBaseUrl(useHttps: useHttps);
    if (baseUrl != null) {
      try {
        if (zhuangtai == 5) {
          zhuangtai = 2.10;
        }

        final response = await http.post(
          Uri.parse('$baseUrl/update_order_status'),
          body: json.encode({'pedidoKey': pedidoKey,
            'zhuangtai': zhuangtai
          }),
          headers: {'Content-Type': 'application/json'},
        ).timeout(Duration(seconds: 30)); // 调整超时时间为30秒

        if (response.statusCode != 200) {
          print('Error response status: ${response.statusCode}');
          throw Exception('Failed to update order status');
        }
      } catch (e) {
        print('Error updating order status: $e');
        throw Exception('Failed to update order status');
      }
    } else {
      throw Exception('Base URL is null');
    }
  }

  static Future<int> checkOrderStatus(String pedidoKey, {bool useHttps = false}) async {
    final baseUrl = await getBaseUrl(useHttps: useHttps);
    if (baseUrl != null) {
      try {
        final response = await http.get(Uri.parse('$baseUrl/check_status?pedidoKey=$pedidoKey')).timeout(timeoutDuration);

        if (response.statusCode == 200) {
          try {
            final jsonResponse = json.decode(response.body);
            return jsonResponse['zhuangtai'];
          } catch (e) {
            print('JSON decode error: $e');
            throw Exception('Failed to load order status');
          }
        } else {
          print('Error response status: ${response.statusCode}');
          throw Exception('Failed to load order status');
        }
      } catch (e) {
        print('Error checking order status: $e');
        throw Exception('Failed to load order status');
      }
    }
    throw Exception('Base URL is null');
  }



  static Future<bool> uploadOrderDetails(String pedidoKey, List<Map<String, dynamic>> data, {bool useHttps = false}) async {
    final baseUrl = await getBaseUrl(useHttps: useHttps);
    if (baseUrl != null) {
      try {
        // 获取bohao值
        final prefs = await SharedPreferences.getInstance();
        int bohaoValue = prefs.getInt('bohao') ?? 0;
        print('Bohao value for upload: $bohaoValue');

        // 确定使用哪个字段名
        String documentFieldName = bohaoValue == 0 ? 'TicketColgandoNo' : 'DocumentoNo';
        print('使用字段名: $documentFieldName');
        
        // 检查pedidoKey是否已存在数据
        try {
          final checkResponse = await http.get(
            Uri.parse('$baseUrl/order_line_count?pedidoKey=$pedidoKey'),
          ).timeout(timeoutDuration);
          
          if (checkResponse.statusCode == 200) {
            final checkData = json.decode(checkResponse.body);
            if (checkData['lineCount'] > 0) {
              print('警告: 该订单(pedidoKey=$pedidoKey)已存在 ${checkData['lineCount']} 条记录。');
              // 可以考虑先删除已有数据或告知用户
            }
          }
        } catch (e) {
          print('检查订单行数时出错: $e');
          // 继续处理，因为这只是预检
        }
        
        // 获取当前最大单号
        int documentNo = 0;
        try {
          final String endpoint = bohaoValue == 0 ? '/max_ticket_colgando_no' : '/max_documento_no';
          final numberResponse = await http.get(Uri.parse('$baseUrl$endpoint')).timeout(timeoutDuration);
          if (numberResponse.statusCode == 200) {
            final numberData = json.decode(numberResponse.body);
            documentNo = bohaoValue == 0 
                ? (numberData['maxTicketColgandoNo'] ?? 0) + 1 
                : (numberData['maxDocumentoNo'] ?? 0) + 1;
            print('获取到新的${documentFieldName}: $documentNo');
          }
        } catch (e) {
          print('获取单号时出错: $e');
          // 继续处理，使用默认值1
          documentNo = 1;
        }
        
        // 处理数据，确保服务器要求的字段都存在
        List<Map<String, dynamic>> processedData = data.map((item) {
          Map<String, dynamic> newItem = {
            'ArticuloID': item['ArticuloID'],
            'CodigoBarra': item['CodigoBarra'] ?? '',
            'Cantidad': item['Cantidad'] ?? 0,
          };
          
          // 添加正确的单号字段
          newItem[documentFieldName] = documentNo;
          
          return newItem;
        }).toList();
        
        final requestBody = json.encode({'pedidoKey': pedidoKey, 'details': processedData});
        
        // 根据bohao值决定使用哪个接口URL
        final String endpointPath = bohaoValue == 0 ? '/upload_order_details' : '/upload_order_details_v3';
        final url = '$baseUrl$endpointPath';
        
        print('上传订单详情请求:');
        print('URL: $url');
        print('订单ID: $pedidoKey');
        print('详情条数: ${processedData.length}');
        print('使用的单号字段: $documentFieldName = $documentNo');
        print('使用的接口: $endpointPath');
        
        // 打印每个详情项的内容
        print('订单详情内容:');
        for (int i = 0; i < processedData.length; i++) {
          print('项目 ${i+1}:');
          processedData[i].forEach((key, value) {
            print('  $key: $value');
          });
        }
        
        final response = await http.post(
          Uri.parse(url),
          body: requestBody,
          headers: {'Content-Type': 'application/json'},
        ).timeout(timeoutDuration);

        if (response.statusCode == 200) {
          print('订单详情上传成功');
          return true;
        } else {
          print('订单详情上传失败:');
          print('状态码: ${response.statusCode}');
          print('响应内容: ${response.body}');
          print('请求数据: $requestBody');
          throw Exception('Failed to upload order details to server. Server responded with status ${response.statusCode}: ${response.body}');
        }
      } catch (e) {
        print('Error uploading order details: $e');
        return false;
      }
    }
    print('无法上传订单详情: 服务器基础URL为空');
    return false;
  }


  static Future<String> fetchContactPerson(String pedidoKey, {bool useHttps = false}) async {
    final baseUrl = await getBaseUrl(useHttps: useHttps);
    if (baseUrl != null) {
      try {
        final response = await http.get(Uri.parse('$baseUrl/contact_person?pedidoKey=$pedidoKey')).timeout(timeoutDuration);

        if (response.statusCode == 200) {
          try {
            final jsonResponse = json.decode(response.body);
            return jsonResponse['lianxiren'];
          } catch (e) {
            print('JSON decode error: $e');
            throw Exception('Failed to load contact person');
          }
        } else {
          print('Error response status: ${response.statusCode}');
          throw Exception('Failed to load contact person');
        }
      } catch (e) {
        print('Error fetching contact person: $e');
        throw Exception('Failed to load contact person');
      }
    }
    throw Exception('Base URL is null');
  }

  static Future<void> updateOrderStatusToComplete(String pedidoKey, {bool useHttps = false}) async {
    await updateOrderStatus(pedidoKey, 3, useHttps: useHttps);
  }

  static Future<List<Map<String, String>>> fetchCustomers({bool useHttps = false}) async {
    final baseUrl = await getBaseUrl(useHttps: useHttps);
    if (baseUrl != null) {
      try {
        final response = await http.get(Uri.parse('$baseUrl/customers')).timeout(timeoutDuration);

        if (response.statusCode == 200) {
          try {
            List<dynamic> data = json.decode(response.body);
            print('Customers data: $data');
            return data.map((item) {
              return {
                'clienteID': item['clienteID'].toString(),
                'name': '${item['clienteID']} - ${item['NombreCN']} - ${item['NombreES']}',
              };
            }).toList();
          } catch (e) {
            print('JSON decode error: $e');
            throw Exception('Failed to load customers');
          }
        } else {
          print('Error response status: ${response.statusCode}');
          throw Exception('Failed to load customers');
        }
      } catch (e) {
        print('Error fetching customers: $e');
        throw Exception('Failed to load customers');
      }
    }
    throw Exception('Base URL is null');
  }

  static Future<int> getMaxTicketColgandoNo({bool useHttps = false}) async {
    final baseUrl = await getBaseUrl(useHttps: useHttps);
    if (baseUrl != null) {
      try {
        final response = await http.get(Uri.parse('$baseUrl/max_ticket_colgando_no')).timeout(timeoutDuration);

        if (response.statusCode == 200) {
          try {
            final jsonResponse = json.decode(response.body);
            return jsonResponse['maxTicketColgandoNo'];
          } catch (e) {
            print('JSON decode error: $e');
            throw Exception('Failed to load max TicketColgandoNo');
          }
        } else {
          print('Error response status: ${response.statusCode}');
          throw Exception('Failed to load max TicketColgandoNo');
        }
      } catch (e) {
        print('Error fetching max TicketColgandoNo: $e');
        throw Exception('Failed to load max TicketColgandoNo');
      }
    }
    throw Exception('Base URL is null');
  }

  //添加获取最大DocumentoNo的方法
  static Future<int> getMaxDocumentoNo({bool useHttps = false}) async {
    final baseUrl = await getBaseUrl(useHttps: useHttps);
    if (baseUrl != null) {
      try {
        final response = await http
            .get(Uri.parse('$baseUrl/max_documento_no'))
            .timeout(timeoutDuration);

        if (response.statusCode == 200) {
          final jsonResponse = json.decode(response.body);
          return jsonResponse['maxDocumentoNo'];
        } else {
          print('Error: ${response.statusCode}');
          throw Exception('Failed to load max DocumentoNo');
        }
      } catch (e) {
        print('Error fetching max DocumentoNo: $e');
        throw Exception('Failed to load max DocumentoNo');
      }
    } else {
      throw Exception('Base URL is null');
    }
  }

  // static Future<void> updateTicketColgandoTable(
  //     int ticketColgandoNo,
  //     String clienteID,
  //     String colgarNota, {
  //       bool useHttps = false,
  //     }) async {
  //   final baseUrl = await getBaseUrl(useHttps: useHttps);
  //   if (baseUrl != null) {
  //     try {
  //       Map<String, dynamic> body = {
  //         'ticketColgandoNo': ticketColgandoNo,
  //         'clienteID': clienteID,
  //         'colgarNota': colgarNota,
  //       };
  //
  //       final response = await http.post(
  //         Uri.parse('$baseUrl/update_ticket_colgando'),
  //         body: json.encode(body),
  //         headers: {'Content-Type': 'application/json'},
  //       ).timeout(timeoutDuration);
  //
  //       if (response.statusCode != 200) {
  //         print('Error response status: ${response.statusCode}');
  //         throw Exception('Failed to update ticket colgando table');
  //       }
  //     } catch (e) {
  //       print('Error updating ticket colgando table: $e');
  //       throw Exception('Failed to update ticket colgando table');
  //     }
  //   } else {
  //     throw Exception('Base URL is null');
  //   }
  // }
  //
  // static Future<void> updateDocumentoNoTable(
  //     int documentoNo,
  //     String colgarNota, {
  //       bool useHttps = false,
  //     }) async {
  //   final baseUrl = await getBaseUrl(useHttps: useHttps);
  //   if (baseUrl != null) {
  //     try {
  //       Map<String, dynamic> body = {
  //         'DocumentoNo': documentoNo,
  //         'colgarNota': colgarNota,
  //         // 注意，这里不包含 'clienteID'
  //       };
  //
  //       final response = await http.post(
  //         Uri.parse('$baseUrl/update_documento_no'),
  //         body: json.encode(body),
  //         headers: {'Content-Type': 'application/json'},
  //       ).timeout(timeoutDuration);
  //
  //       if (response.statusCode != 200) {
  //         print('Error response status: ${response.statusCode}');
  //         throw Exception('Failed to update DocumentoNo table');
  //       }
  //     } catch (e) {
  //       print('Error updating DocumentoNo table: $e');
  //       throw Exception('Failed to update DocumentoNo table');
  //     }
  //   } else {
  //     throw Exception('Base URL is null');
  //   }
  // }


  static Future<void> updatePedidoZhuangtai(String pedidoKey, int zhuangtai, {bool useHttps = false}) async {
    final baseUrl = await getBaseUrl(useHttps: useHttps);
    if (baseUrl != null) {
      try {
        final response = await http.post(
          Uri.parse('$baseUrl/update_order_status'),
          body: json.encode({'pedidoKey': pedidoKey, 'zhuangtai': zhuangtai}),
          headers: {'Content-Type': 'application/json'},
        ).timeout(timeoutDuration);

        if (response.statusCode != 200) {
          print('Error response status: ${response.statusCode}');
          throw Exception('Failed to update order status');
        }
      } catch (e) {
        print('Error updating order status: $e');
        throw Exception('Failed to update order status');
      }
    } else {
      throw Exception('Base URL is null');
    }
  }


  static Future<int> fetchCantidadPorUnidad(String codigoBarra, {bool useHttps = false}) async {
    final baseUrl = await getBaseUrl(useHttps: useHttps);
    if (baseUrl != null) {
      try {
        final response = await http.get(Uri.parse('$baseUrl/articulo_cantidad?codigoBarra=$codigoBarra')).timeout(timeoutDuration);

        if (response.statusCode == 200) {
          try {
            final jsonResponse = json.decode(response.body);
            return jsonResponse['CantidadPorUnidad'];
          } catch (e) {
            print('JSON decode error: $e');
            throw Exception('Failed to load CantidadPorUnidad');
          }
        } else {
          print('Error response status: ${response.statusCode}');
          throw Exception('Failed to load CantidadPorUnidad');
        }
      } catch (e) {
        print('Error fetching CantidadPorUnidad: $e');
        throw Exception('Failed to load CantidadPorUnidad');
      }
    }
    throw Exception('Base URL is null');
  }

  static Future<String?> fetchWeizhi(String codigo, {bool useHttps = false}) async {
    final baseUrl = await getBaseUrl(useHttps: useHttps);
    if (baseUrl != null) {
      try {
        final response = await http.get(Uri.parse('$baseUrl/get_weizhi?codigo=$codigo')).timeout(timeoutDuration);
        if (response.statusCode == 200) {
          final jsonResponse = json.decode(response.body);
          return jsonResponse['weizhi'];
        } else {
          print('Error response status: ${response.statusCode}');
        }
      } catch (e) {
        print('Error fetching weizhi: $e');
      }
    }
    return null;
  }


  static Future<void> uploadOutOfStockItems(List<Map<String, dynamic>> data, {bool useHttps = false}) async {
    final baseUrl = await getBaseUrl(useHttps: useHttps);
    if (baseUrl != null) {
      try {
        const int batchSize = 50;
        for (int i = 0; i < data.length; i += batchSize) {
          List<Map<String, dynamic>> batch = data.sublist(
            i,
            i + batchSize > data.length ? data.length : i + batchSize,
          );

          final response = await http.post(
            Uri.parse('$baseUrl/upload_out_of_stock'),
            body: json.encode({'outOfStockItems': batch}),
            headers: {'Content-Type': 'application/json'},
          ).timeout(timeoutDuration);

          if (response.statusCode != 200) {
            print('Error response status: ${response.statusCode}');
            print('Error response body: ${response.body}');
            throw Exception('Failed to upload out of stock items');
          }
        }
      } catch (e) {
        print('Error uploading out of stock items: $e');
        throw Exception('Failed to upload out of stock items');
      }
    } else {
      throw Exception('Base URL is null');
    }
  }

  static Future<void> uploadRevision({
    required String pedidoKey,
    required double revision, // 使用小写 revision
    required String Revisor,  // 确保参数名称一致
    bool useHttps = false,
  }) async {
    final baseUrl = await getBaseUrl(useHttps: useHttps);
    if (baseUrl != null) {
      try {
        // Debug: Print the data being sent
        print('Uploading revision data:');
        print('pedidoKey: $pedidoKey');
        print('revision: $revision');
        print('Revisor: $Revisor');

        final response = await http.post(
          Uri.parse('$baseUrl/uploadrevision'),
          body: json.encode({
            'pedidoKey': pedidoKey,
            'revision': revision,
            'Revisor': Revisor,
          }),
          headers: {'Content-Type': 'application/json'},
        ).timeout(timeoutDuration);

        if (response.statusCode == 200) {
          print('Revision uploaded successfully');
        } else {
          print('Failed to upload revision: ${response.statusCode}');
        }
      } catch (e) {
        print('Error uploading revision: $e');
      }
    } else {
      print('Base URL is null');
    }
  }

  static Future<void> uploadOrderInfo(String pedidoKey, String username, double fulfillmentRate, {bool useHttps = false}) async {
    final baseUrl = await getBaseUrl(useHttps: useHttps);
    if (baseUrl != null) {
      try {
        final response = await http.post(
          Uri.parse('$baseUrl/upload_order_info'),
          body: json.encode({
            'pedidoKey': pedidoKey,
            'username': username,
            'fulfillmentRate': fulfillmentRate,
          }),
          headers: {'Content-Type': 'application/json'},
        ).timeout(timeoutDuration);

        if (response.statusCode == 200) {
          print('Order info uploaded successfully');
        } else {
          print('Failed to upload order info: ${response.statusCode}');
        }
      } catch (e) {
        print('Error uploading order info: $e');
      }
    } else {
      print('Base URL is null');
    }
  }
  static Future<void> markOrderAsFinished(String pedidoKey) async {
    final baseUrl = await getBaseUrl();
    if (baseUrl != null) {
      final requestBody = json.encode({'pedidoKey': pedidoKey});
      final requestUrl = '$baseUrl/mark_order_as_finished';

      try {
        print('Sending request to API:');
        print('URL: $requestUrl');
        print('Body: $requestBody');

        final response = await http
            .post(
          Uri.parse(requestUrl),
          body: requestBody,
          headers: {'Content-Type': 'application/json'},
        )
            .timeout(timeoutDuration);

        print('Response received from API:');
        print('Status code: ${response.statusCode}');
        print('Response body: ${response.body}');

        if (response.statusCode == 200) {
          print('Order marked as finished successfully.');
        } else {
          print('Failed to mark order as finished: ${response.statusCode}');
        }
      } catch (e) {
        print('Error marking order as finished: $e');
        throw Exception('无法将订单标记为已完成。');
      }
    } else {
      print('Base URL is null');
    }
  }
  static Future<void> verifyOrder(String pedidoKey) async {
    final baseUrl = await getBaseUrl();
    if (baseUrl != null) {
      try {
        final response = await http.post(
          Uri.parse('$baseUrl/verify_order'),
          body: json.encode({'pedidoKey': pedidoKey}),
          headers: {'Content-Type': 'application/json'},
        ).timeout(timeoutDuration);

        if (response.statusCode == 200) {
          print('Order verification updated successfully.');
        } else {
          print('Failed to update order verification: ${response.statusCode}');
        }
      } catch (e) {
        print('Error updating order verification: $e');
        throw Exception('无法更新订单验证状态。');
      }
    } else {
      print('Base URL is null');
    }
  }
  static Future<int> fetchOrderLineCount(String pedidoKey, {bool useHttps = false}) async {
    final String? baseUrl = await getBaseUrl(useHttps: useHttps);
    if (baseUrl != null && baseUrl.isNotEmpty) {
      final String url = '$baseUrl/order_line_count?pedidoKey=$pedidoKey';

      try {
        final response = await http.get(Uri.parse(url)).timeout(timeoutDuration);

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          print(response.body);

          // 检查返回数据是否包含 pedidoKey 和 lineCount
          if (data != null && data['pedidoKey'] == pedidoKey) {
            return data['lineCount'] as int;
          } else {
            throw Exception('Invalid response data or pedidoKey mismatch');
          }
        } else {
          throw Exception('Failed to fetch order line count with status code: ${response.statusCode}');
        }
      } catch (error) {
        print('Error details: ${error.toString()}'); // 记录详细错误信息
        throw Exception('Error fetching order line count: $error');
      }
    }

    throw Exception('Base URL is null or empty');
  }

}