//订单管理，清除本地数据退回到初始新订单
import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import 'package:mistoer/services/database_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:mistoer/models/order.dart';
import 'package:mistoer/services/api_service.dart';

class OrderDetailsScreenPurge extends StatefulWidget {
  @override
  _OrderDetailsScreenPurgeState createState() => _OrderDetailsScreenPurgeState();
}

class _OrderDetailsScreenPurgeState extends State<OrderDetailsScreenPurge> {
  List<Order> orderDetails = [];
  List<Order> filteredOrders = [];
  Set<String> selectedPedidoKeys = Set<String>();
  bool isPoderMantenerEnabled = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadOrderDetails();
    _checkPoderMantener();
    _searchController.addListener(_filterOrders);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadOrderDetails() async {
    try {
      final List<Order> orders = await ApiService.fetchOrders(zhuangtai: 2); // Fetch orders with status 2
      final Database db = await DatabaseService.database;

      // Filter orders based on local database existence
      final List<Order> validOrders = [];
      for (var order in orders) {
        final List<Map<String, dynamic>> result = await db.query(
          'orderDetails',
          where: 'pedidoKey = ?',
          whereArgs: [order.pedidoKey],
        );
        if (result.isNotEmpty) {
          validOrders.add(order);
        }
      }

      setState(() {
        orderDetails = validOrders;
        filteredOrders = validOrders; // Initialize filtered orders list
      });
    } catch (e) {
      print('Error loading orders: $e');
    }
  }

  Future<void> _checkPoderMantener() async {
    final prefs = await SharedPreferences.getInstance();
    final poderMantener = prefs.getInt('PoderMantener') ?? 0;
    setState(() {
      isPoderMantenerEnabled = (poderMantener == -1);
    });
  }

  void _filterOrders() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      filteredOrders = orderDetails.where((order) {
        return order.name.toLowerCase().contains(query) ||
            order.pedidoKey.toLowerCase().contains(query) ||
            order.riqi.toLowerCase().contains(query) ||
            order.amount.toString().contains(query) ||
            order.note.toLowerCase().contains(query);
      }).toList();
    });
  }

  void _toggleSelection(String pedidoKey) {
    setState(() {
      if (selectedPedidoKeys.contains(pedidoKey)) {
        selectedPedidoKeys.remove(pedidoKey);
      } else {
        selectedPedidoKeys.add(pedidoKey);
      }
    });
  }

  Future<void> _clearSelected() async {
    if (selectedPedidoKeys.isEmpty) {
      _showAlertDialog('请先选择要清除的订单');
      return;
    }

    bool confirmed = await _showConfirmationDialog("清除选中的订单吗？");
    if (!confirmed) return;

    final Database db = await DatabaseService.database;
    for (var pedidoKey in selectedPedidoKeys) {
      await db.delete('orderDetails', where: 'pedidoKey = ?', whereArgs: [pedidoKey]);
      await ApiService.updatePedidoZhuangtai(pedidoKey, 1); // 更新在线数据库的状态
    }
    setState(() {
      orderDetails.removeWhere((order) => selectedPedidoKeys.contains(order.pedidoKey));
      filteredOrders.removeWhere((order) => selectedPedidoKeys.contains(order.pedidoKey));
      selectedPedidoKeys.clear();
    });
  }

  Future<void> _clearAll() async {
    bool confirmed = await _showConfirmationDialog("清除所有订单吗？");
    if (!confirmed) return;

    final Database db = await DatabaseService.database;
    for (var order in orderDetails) {
      await db.delete('orderDetails', where: 'pedidoKey = ?', whereArgs: [order.pedidoKey]);
      await ApiService.updatePedidoZhuangtai(order.pedidoKey, 1); // 更新在线数据库的状态
    }
    setState(() {
      orderDetails.clear();
      filteredOrders.clear();
      selectedPedidoKeys.clear();
    });
  }

  Future<bool> _showConfirmationDialog(String message) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text("确认"),
          content: Text(message),
          actions: [
            TextButton(
              child: Text("取消"),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            TextButton(
              child: Text("确认"),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    ) ?? false;
  }

  Future<void> _showAlertDialog(String message) async {
    await showDialog<void>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text("提示"),
          content: Text(message),
          actions: [
            TextButton(
              child: Text("确定"),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  Future<Map<String, double>> _fetchOrderFulfillmentRates() async {
    Map<String, double> fulfillmentRates = {};
    Set<String> uniquePedidoKeys = orderDetails.map((e) => e.pedidoKey).toSet();
    for (var pedidoKey in uniquePedidoKeys) {
      double rate = await DatabaseService.getOrderFulfillmentRate(pedidoKey);
      fulfillmentRates[pedidoKey] = rate;
    }
    return fulfillmentRates;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('订单管理'),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: '搜索',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    '订单编号',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    '公司名称',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    '出库率',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      '选择',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: FutureBuilder<Map<String, double>>(
              future: _fetchOrderFulfillmentRates(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(child: CircularProgressIndicator());
                } else if (snapshot.hasError) {
                  return Center(child: Text('Error: ${snapshot.error}'));
                } else {
                  final fulfillmentRates = snapshot.data!;
                  return ListView.builder(
                    itemCount: filteredOrders.length,
                    itemBuilder: (context, index) {
                      final order = filteredOrders[index];
                      final rate = fulfillmentRates[order.pedidoKey] ?? 0.0;
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
                        child: Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: Text(order.pedidoKey),
                            ),
                            Expanded(
                              flex: 2,
                              child: Text(order.name),
                            ),
                            Expanded(
                              flex: 1,
                              child: Text(' ${(rate * 100).toStringAsFixed(2)}%'), // 修改为保留一位小数
                            ),
                            Expanded(
                              flex: 1,
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: Checkbox(
                                  value: selectedPedidoKeys.contains(order.pedidoKey),
                                  onChanged: (bool? value) {
                                    _toggleSelection(order.pedidoKey);
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  );
                }
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: isPoderMantenerEnabled ? _clearSelected : null,
                  child: Text('清除选择订单'),
                ),
                ElevatedButton(
                  onPressed: isPoderMantenerEnabled ? _clearAll : null,
                  child: Text('清除所有订单'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
