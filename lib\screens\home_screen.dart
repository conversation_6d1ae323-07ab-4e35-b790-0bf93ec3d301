import 'package:flutter/material.dart';
import 'package:mistoer/services/database_service_sn.dart'; // 用于获取 servicereg 数据
import 'package:mistoer/services/database_service.dart'; // 用于操作其他数据库
import 'package:provider/provider.dart';
import 'package:mistoer/screens/ip_input_screen.dart'; // 导入 IpInputScreen 界面
import 'package:mistoer/screens/order_screen_zhuangtai1.dart';
import 'package:mistoer/screens/order_screen_zhuangtai2.dart';
import 'package:mistoer/screens/order_screen_zhuangtai5.dart';
import 'package:mistoer/screens/order_details_screen_purge.dart';
import 'package:mistoer/screens/scan_inventory_page.dart';
import 'package:mistoer/screens/scan_stocking_page.dart';
import 'package:mistoer/print/print_label_screen.dart';
import 'package:mistoer/providers/settings_provider.dart';
import 'package:mistoer/print/goods_check_page.dart';
import 'package:intl/intl.dart'; // 用于处理时间
import 'package:http/http.dart' as http; // 用于网络请求
import 'dart:convert'; // 用于解析 JSON
import 'warehouse_storage_page.dart';
import 'package:mistoer/widgets/online_status_indicator.dart';

class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  List<Map<String, dynamic>> buttons = [];
  String? username;
  bool isListView = true;
  bool showScanInventoryButton = false;
  String appBarTitle = '悟空科技管理系统'; // 默认标题
  bool mayorista = false; // 控制 Mayorista 按钮显示
  bool minorista = false; // 控制 Minorista 按钮显示
  String? tiempoPrueba; // 试用到期时间
  bool trialExpired = false; // 新增状态变量，指示试用期是否已过期
  bool esPrueba = false; // 声明 esPrueba 变量
  String? shopId; // 用于存储 shopid 值

  @override
  void initState() {
    super.initState();
    _loadUsername().then((_) async {
      if (username != null && username != "未登录") {
        await _loadButtonOrder(username!);
        await _checkRevisorValue();
      }
      await _loadCorporateName(); // 获取公司名称和权限控制
    });
  }

  // 异步加载公司名称和权限
  Future<void> _loadCorporateName() async {
    try {
      final db = await DatabaseServiceSN.database;
      final List<Map<String, dynamic>> result = await db.query('servicereg');

      if (result.isNotEmpty) {
        final record = result.first;
        setState(() {
          mayorista = record['mayorista'] == 1;
          minorista = record['minorista'] == 1;
          tiempoPrueba = record['tiempoPrueba'];
          esPrueba = record['esPrueba'] == 1;
          shopId = record['shopid']; // 获取 shopid 值

          // 如果 corporatename 为空，使用默认的标题
          appBarTitle = esPrueba
              ? '试用版本'
              : (record['corporatename']?.isNotEmpty ?? false
              ? record['corporatename']
              : '悟空科技管理系统');
        });

        // 如果是试用版本，检查试用期是否到期
        if (esPrueba && tiempoPrueba != null) {
          await _checkTrialExpiration(tiempoPrueba!);
        }

        // 更新按钮显示逻辑
        _updateButtonsVisibility();
      } else {
        // 没有记录时使用默认标题
        setState(() {
          appBarTitle = '悟空科技管理系统';
        });
      }
    } catch (e) {
      // 处理异常，例如数据库查询错误
      print("Error loading corporate name and permissions: $e");
      setState(() {
        appBarTitle = '悟空科技管理系统'; // 出错时使用默认标题
      });
    }
  }

  // 获取服务器时间
  Future<DateTime?> _getServerTime() async {
    try {
      // 使用世界时钟 API 获取 UTC 时间
      final response =
      await http.get(Uri.parse('http://worldtimeapi.org/api/timezone/Etc/UTC'));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final datetime = data['utc_datetime'];
        return DateTime.parse(datetime);
      } else {
        print('Failed to get server time. Status code: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('Error getting server time: $e');
      return null;
    }
  }

  // 检查试用期是否过期
  Future<void> _checkTrialExpiration(String trialEndDate) async {
    try {
      // 获取服务器时间
      DateTime? serverTime = await _getServerTime();

      if (serverTime == null) {
        // 无法获取服务器时间，阻止访问或使用本地时间作为后备方案
        // 这里选择阻止访问并导航到 IpInputScreen
        setState(() {
          trialExpired = true;
        });
        // 导航到 IpInputScreen
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => IpInputScreen()),
        );
        return;
      }

      DateTime trialEnd = DateFormat('yyyy-MM-dd').parse(trialEndDate);

      if (serverTime.isAfter(trialEnd)) {
        // 试用期到期
        final db = await DatabaseServiceSN.database;

        // 更新 ok 为 0，并清空 regnumber (删除注册码)
        await db.update(
          'servicereg',
          {
            'ok': 0,
            'regnumber': null, // 删除本地注册码
          },
          where: 'id = ?',
          whereArgs: [1],
        );

        // 更新状态变量并导航到 IpInputScreen
        setState(() {
          trialExpired = true;
        });

        // 导航到 IpInputScreen
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => IpInputScreen()),
        );
      }
    } catch (e) {
      print("Error checking trial expiration: $e");
      // 发生异常，阻止访问并导航到 IpInputScreen
      setState(() {
        trialExpired = true;
      });
      // 导航到 IpInputScreen
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => IpInputScreen()),
      );
    }
  }

  // 根据权限控制按钮显示
  void _updateButtonsVisibility() {
    List<Map<String, dynamic>> baseButtons = [
      if (mayorista) ...[
        {'text': '订单下载 Descarga de pedidos', 'icon': Icons.outbound},
        {'text': '订单出库 Despacho de pedidos', 'icon': Icons.history},
        if (showScanInventoryButton)
          {'text': '订单复核 Revision de pedidos', 'icon': Icons.check_circle},
        {'text': '查询功能 Funcion de consulta', 'icon': Icons.search},
        {'text': '仓库盘点 Conteo de inventario', 'icon': Icons.inventory},
        {'text': '备品仓库 Almacen De Reserva', 'icon': Icons.storage},
      ],
      if (minorista) ...[
        {'text': '标签打印 Impresion de Etiquetas', 'icon': Icons.print},
        {
          'text': '商品核对 Verificacion de Productos',
          'icon': Icons.assignment_turned_in
        },
      ],
    ];

    setState(() {
      buttons = baseButtons;
    });
  }

  Future<void> _checkRevisorValue() async {
    final db = await DatabaseService.database;
    final List<Map<String, dynamic>> result = await db.query(
      'empleados',
      where: 'Nombre = ? AND in_use = ?',
      whereArgs: [username, -1],
    );

    if (result.isNotEmpty && result.first['Revisor'] == -1) {
      setState(() {
        showScanInventoryButton = true;
        _updateButtonsVisibility();
      });
    } else {
      setState(() {
        showScanInventoryButton = false;
        _updateButtonsVisibility();
      });
    }
  }

  Future<void> _saveButtonOrder(String username) async {
    List<String> buttonTexts =
    buttons.map((button) => button['text'] as String).toList();
    await DatabaseService.saveButtonOrder(username, buttonTexts);
  }

  Future<void> _loadButtonOrder(String username) async {
    List<String> buttonTexts = await DatabaseService.loadButtonOrder(username);
    if (buttonTexts.isNotEmpty) {
      setState(() {
        buttons.sort((a, b) => buttonTexts
            .indexOf(a['text'])
            .compareTo(buttonTexts.indexOf(b['text'])));
      });
    }
  }

  void _onReorder(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final item = buttons.removeAt(oldIndex);
      buttons.insert(newIndex, item);
      if (username != null && username != "未登录") {
        _saveButtonOrder(username!);
      }
    });
  }

  Future<void> _handleButtonPress(String buttonText) async {
    switch (buttonText) {
      case '订单下载 Descarga de pedidos':
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => OrderScreenZhuangtai1(zhuangtai: 1)));
        break;
      case '订单出库 Despacho de pedidos':
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => OrderScreenZhuangtai2(zhuangtai: 2)));
        break;
      case '订单复核 Revision de pedidos':
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => OrderScreenZhuangtai5(zhuangtai: 5)));
        break;
      case '查询功能 Funcion de consulta':
        Navigator.push(
            context, MaterialPageRoute(builder: (context) => ScanInventoryPage()));
        break;
      case '仓库盘点 Conteo de inventario':
        Navigator.push(
            context, MaterialPageRoute(builder: (context) => ScanStockingPage()));
        break;
      case '备品仓库 Almacen De Reserva':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => WarehouseStoragePage()),
        );
        break;
      case '标签打印 Impresion de Etiquetas':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PrintLabelScreen(articuloID: ''),
          ),
        );
        break;
      case '商品核对 Verificacion de Productos':
        Navigator.push(
            context, MaterialPageRoute(builder: (context) => GoodsCheckPage()));
        break;
      default:
        break;
    }
  }

  Future<void> _loadUsername() async {
    final db = await DatabaseService.database;
    final List<Map<String, dynamic>> result = await db.query(
      'empleados',
      where: 'in_use = ?',
      whereArgs: [-1],
    );

    if (result.isNotEmpty) {
      setState(() {
        username = result.first['Nombre'];
      });
    } else {
      setState(() {
        username = "未登录";
      });
    }
  }

  void _logout() async {
    if (username != null) {
      await DatabaseService.updateInUse(username!, 0);
      Navigator.pushReplacementNamed(context, '/login');
    }
  }

  void _toggleView() {
    setState(() {
      isListView = !isListView;
    });
  }

  @override
  Widget build(BuildContext context) {
    final settings = Provider.of<SettingsProvider>(context);

    // 如果试用期已过期，直接返回空的 Scaffold，以避免构建主界面
    if (trialExpired) {
      // 返回一个空的容器，或者您可以返回一个提示界面
      return Scaffold();
    }

    return Scaffold(
      // 在build方法中的AppBar部分
      appBar: AppBar(
      title: Text(appBarTitle),
      centerTitle: true,
      toolbarHeight: 80,
      actions: [
      // 添加在线状态指示器
      OnlineStatusIndicator(),
      IconButton(
      icon: Icon(isListView ? Icons.grid_view : Icons.view_list),
      onPressed: _toggleView,
      ),
      ],
      ),
      body: Center(
        child: Column(
          children: [
            SizedBox(height: 30),
            Expanded(
              child: isListView
                  ? ReorderableListView(
                onReorder: _onReorder,
                children: List.generate(buttons.length, (index) {
                  var button = buttons[index];
                  return Container(
                    key: ValueKey('button$index'),
                    margin: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Center(
                      child: SizedBox(
                        width: 350,
                        child: ElevatedButton.icon(
                          onPressed: () async {
                            await _handleButtonPress(button['text']);
                          },
                          style: ElevatedButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                          ),
                          icon: Icon(button['icon'], color: Colors.white),
                          label: Text(button['text'],
                              style: TextStyle(color: Colors.white)),
                        ),
                      ),
                    ),
                  );
                }),
              )
                  : GridView.builder(
                gridDelegate:
                SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  mainAxisSpacing: 5,
                  crossAxisSpacing: 5,
                  childAspectRatio: 1,
                ),
                itemCount: buttons.length,
                itemBuilder: (context, index) {
                  var button = buttons[index];
                  return Container(
                    key: ValueKey('button$index'),
                    margin: const EdgeInsets.all(1.0),
                    child: ElevatedButton.icon(
                      onPressed: () async {
                        await _handleButtonPress(button['text']);
                      },
                      style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        minimumSize: Size(0, 150),
                      ),
                      icon: Icon(button['icon'], color: Colors.white),
                      label: Text(button['text'],
                          style: TextStyle(color: Colors.white)),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
      drawer: Drawer(
        child: Stack(
          children: [
            Column(
              children: <Widget>[
                Container(
                  height: 150,
                  child: DrawerHeader(
                    decoration: BoxDecoration(
                      color: Colors.blue,
                    ),
                    child: Center(
                      child: Text(
                        '菜单',
                        style: TextStyle(color: Colors.white, fontSize: 24),
                      ),
                    ),
                  ),
                ),
                ListTile(
                  title: Text('IP地址配置'),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => IpInputScreen()),
                    );
                  },
                ),
                ListTile(
                  title: Text('订单管理'),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => OrderDetailsScreenPurge()),
                    );
                  },
                ),
                ListTile(
                  title: Text('平台编号'),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => PlatformNumberScreen()),
                    );
                  },
                ),
                ListTile(
                  title: Text(!settings.isDetailedMode ? '简洁模式' : '详细模式'),
                  trailing: Switch(
                    value: !settings.isDetailedMode,
                    onChanged: (value) {
                      settings.toggleDetailedMode();
                    },
                  ),
                ),

                ListTile(
                  title: Text(settings.isOrderCountMode ? '订单数模式' : '包数模式'),
                  trailing: Switch(
                    value: settings.isOrderCountMode,
                    onChanged: (value) {
                      settings.toggleOrderCountMode();
                    },
                  ),
                ),
                Spacer(),
                Padding(
                  padding: const EdgeInsets.only(bottom: 32.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (username != null)
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text('当前账号：$username',
                                style: TextStyle(fontSize: 16)),
                            SizedBox(width: 25),
                            SizedBox(
                              width: 120,
                              height: 40,
                              child: ElevatedButton(
                                onPressed: _logout,
                                child: Text('退出登录'),
                                style: ElevatedButton.styleFrom(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  backgroundColor: Colors.blue,
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      if (shopId != null) // 显示 shopid
                        Padding(
                          padding: const EdgeInsets.only(top: 16.0),
                          child: Text(
                            '店铺ID: $shopId',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// 新增的平台编号页面
class PlatformNumberScreen extends StatefulWidget {
  @override
  _PlatformNumberScreenState createState() => _PlatformNumberScreenState();
}

class _PlatformNumberScreenState extends State<PlatformNumberScreen> {
  TextEditingController _ygidController = TextEditingController();
  bool _isYGIDInputDisabled = false; // 输入框是否禁用，初始为可编辑
  int _inputFieldClickCount = 0; // 输入框点击次数

  @override
  void initState() {
    super.initState();
    _loadYGID();
  }

  // 从数据库加载平台编号 YGID
  Future<void> _loadYGID() async {
    try {
      final db = await DatabaseService.database;
      final List<Map<String, dynamic>> result = await db.query(
        'yginfo',
        where: 'id = ?',
        whereArgs: [1],
      );

      if (result.isNotEmpty) {
        setState(() {
          _ygidController.text = result.first['YGID'] ?? ''; // 获取 YGID 的值
          _isYGIDInputDisabled = result.first['YGID'] != null && result.first['YGID'] != '';
        });
      } else {
        // 如果表中没有 id=1 的记录，插入一条空记录
        await db.insert('yginfo', {'id': 1, 'YGID': ''});
      }
    } catch (e) {
      print('Error loading YGID: $e');
    }
  }

  // 保存平台编号到数据库
  Future<void> _saveYGID() async {
    try {
      final db = await DatabaseService.database;
      await db.update(
        'yginfo',
        {'YGID': _ygidController.text},
        where: 'id = ?',
        whereArgs: [1],
      );
      setState(() {
        _isYGIDInputDisabled = true; // 保存后禁用输入框
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('平台编号已保存')),
      );
    } catch (e) {
      print('Error saving YGID: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text('平台编号'),
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: GestureDetector(
            onTap: () {
              if (_isYGIDInputDisabled) {
                _inputFieldClickCount++;
                if (_inputFieldClickCount >= 6) {
                  setState(() {
                    _isYGIDInputDisabled = false;
                    _inputFieldClickCount = 0;
                  });
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('您可以编辑平台编号了')),
                  );
                }
              }
            },
            child: AbsorbPointer(
              absorbing: _isYGIDInputDisabled,
              child: Column(
                children: [
                  TextField(
                    controller: _ygidController,
                    enabled: !_isYGIDInputDisabled, // 根据状态禁用或启用输入框
                    decoration: InputDecoration(
                      labelText: '平台编号',
                      border: OutlineInputBorder(),
                    ),
                    onSubmitted: (value) {
                      _saveYGID();
                    },
                  ),
                  SizedBox(height: 20),
                  if (!_isYGIDInputDisabled)
                    ElevatedButton(
                      onPressed: _saveYGID,
                      child: Text('保存平台编号'),
                    ),
                ],
              ),
            ),
          ),
        ));
  }
}
