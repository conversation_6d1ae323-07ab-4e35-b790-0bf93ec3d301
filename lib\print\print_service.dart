import 'package:flutter/services.dart';

class PrintService {
  static const platform = MethodChannel('com.example.mistoer/print');

  static Future<Map<String, dynamic>> printLabel({
    required String productNumberLabel,
    required String productNameLabel,
    required String retailPriceLabel,
    required String memberPriceLabel,
    required String articuloID,
    required String nombreES,
    required String codigoBarra,
    required String precioDetalle,
    required String precioMayor,
  }) async {
    try {
      // 记录日志，确认每次调用
      print("Flutter: Invoking printLabel with articuloID: $articuloID");

      final result = await platform.invokeMethod('printLabel', {
        'productNumberLabel': productNumberLabel,
        'productNameLabel': productNameLabel,
        'retailPriceLabel': retailPriceLabel,
        'memberPriceLabel': memberPriceLabel,
        'articuloID': articuloID,
        'nombreES': nombreES,
        'codigoBarra': codigoBarra,
        'precioDetalle': precioDetalle,
        'precioMayor': precioMayor,
      });

      print("Flutter: Label printed successfully: $result");
      return result as Map<String, dynamic>;
    } on PlatformException catch (e) {
      print("Flutter: Failed to print label: ${e.message}");
      return {
        'success': false,
        'message': e.message ?? 'Unknown error occurred'
      };
    } catch (e) {
      print("Flutter: Unexpected error during printing: $e");
      return {
        'success': false,
        'message': e.toString()
      };
    }
  }
}
