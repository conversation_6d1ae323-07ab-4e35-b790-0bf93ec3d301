import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mistoer/models/order.dart';

class OrderItem extends StatelessWidget {
  final Order order;
  final VoidCallback onTap;

  OrderItem({required this.order, required this.onTap});

  String _formatDate(String date) {
    DateTime parsedDate = DateTime.parse(date);
    return DateFormat('yyyy年MM月dd日').format(parsedDate);
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(order.name),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('订单编号: ${order.pedidoKey}'),
          Text('日期: ${_formatDate(order.riqi)}'),
          Text('金额: ${order.amount}'),
          Text('备注: ${order.beizhu}'),
        ],
      ),
      trailing: Container(
        width: 80,
        alignment: Alignment.centerRight,
        child: Text('${(order.fulfillmentRate * 100).toStringAsFixed(2)}%'),
      ),
      onTap: onTap,
    );
  }
}
