import 'package:flutter/material.dart';

class PrintAdvancedSettingsScreen extends StatefulWidget {
  @override
  _PrintAdvancedSettingsScreenState createState() => _PrintAdvancedSettingsScreenState();
}

class _PrintAdvancedSettingsScreenState extends State<PrintAdvancedSettingsScreen> {
  final TextEditingController _fontController = TextEditingController(text: "12");
  final TextEditingController _alignmentController = TextEditingController(text: "Center");

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('高级设置'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            _buildTextField(_fontController, '字体大小'),
            SizedBox(height: 20),
            _buildTextField(_alignmentController, '对齐方式'),
            <PERSON><PERSON><PERSON><PERSON>(height: 20),
            ElevatedButton(
              onPressed: () {
                // 保存高级设置
              },
              child: Text('保存设置'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField(TextEditingController controller, String label) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(),
      ),
    );
  }
}
