import 'package:flutter/material.dart';

class CustomFadingEdgeScrollView extends StatelessWidget {
  final Widget child;
  final ScrollController? controller;

  CustomFadingEdgeScrollView({required this.child, this.controller});

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      shaderCallback: (Rect bounds) {
        return LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: <Color>[Colors.transparent, Colors.black, Colors.black, Colors.transparent],
          stops: <double>[0.0, 0.1, 0.9, 1.0],
        ).createShader(bounds);
      },
      blendMode: BlendMode.dstOut,
      child: <PERSON><PERSON><PERSON>(
        controller: controller ?? ScrollController(),
        child: SingleChildScrollView(
          controller: controller ?? ScrollController(),
          child: child,
        ),
      ),
    );
  }
}
