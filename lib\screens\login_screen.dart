import 'dart:async';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:mistoer/services/api_service.dart';
import 'package:mistoer/services/sn_service.dart'; // 使用你的 SNservice 服务
import 'package:mistoer/services/database_service.dart'; // 引入本地数据库管理服务
import 'package:mistoer/screens/download_apk_page.dart'; // 引入下载页面
import 'package:mistoer/services/offline_manager.dart';

class LoginScreen extends StatefulWidget {
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  // 控制器和焦点节点
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final FocusNode _usernameFocusNode = FocusNode();
  final FocusNode _passwordFocusNode = FocusNode();
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _passwordFieldKey = GlobalKey();

  // 状态变量
  bool _isLoading = true;
  bool _isEditingUsername = false;
  bool _isCheckingUpdate = false;
  List<String> _usernames = [];
  String? _selectedUsername;
  String _appVersion = '2.10.0'; // 初始化版本号为默认值

  bool _isOnline = false;
  bool _manualOffline = false;

  @override
  void initState() {
    super.initState();
    _checkForUpdate(); // 检查是否有更新，优先处理
    _usernameFocusNode.addListener(_onUsernameFocusChange);
    _passwordFocusNode.addListener(() {
      if (_passwordFocusNode.hasFocus && _passwordFieldKey.currentContext != null) {
        Future.delayed(const Duration(milliseconds: 50), () {
          if (!mounted) return;
          Scrollable.ensureVisible(
            _passwordFieldKey.currentContext!,
            duration: const Duration(milliseconds: 250),
            curve: Curves.easeInOut,
            alignment: 0.3,
          );
        });
      }
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _usernameFocusNode.requestFocus();
      // 同步全局在线/离线
      _isOnline = OfflineManager.instance.isOnline.value;
      _manualOffline = OfflineManager.instance.manualOffline;
      OfflineManager.instance.isOnline.addListener(() {
        if (!mounted) return;
        setState(() {
          _isOnline = OfflineManager.instance.isOnline.value;
        });
      });
    });
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    _usernameFocusNode.dispose();
    _passwordFocusNode.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // 处理网络连接错误并跳转
  void _handleNetworkError(String errorMessage) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('网络连接错误'),
        content: Text(errorMessage),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.pushReplacementNamed(context, '/ip_input');
            },
            child: Text('确定'),
          ),
        ],
      ),
    );
  }

  // 加载当前应用版本信息
  Future<void> _loadAppVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _appVersion = packageInfo.version;
      print('本地应用版本号: $_appVersion');
    });
  }

  // 加载用户名
  Future<void> _loadUsernames() async {
    print("Loading usernames from the database...");
    try {
      List<Map<String, dynamic>> usernamesFromDb = await DatabaseService.getUsernames();
      print("Usernames loaded: $usernamesFromDb");
      setState(() {
        _usernames = usernamesFromDb.map((user) => user['Nombre'].toString()).toSet().toList();
        if (_usernames.isNotEmpty) {
          _selectedUsername = _usernames.first;
          _isEditingUsername = false;
        } else {
          _selectedUsername = null;
          _isEditingUsername = true;
          _usernameFocusNode.requestFocus();
        }
        _isLoading = false;
      });
    } catch (e) {
      print("Failed to fetch user fields: $e");
    }
  }

  // 检查是否有新的版本
  Future<void> _checkForUpdate() async {
    setState(() {
      _isCheckingUpdate = true; // 开始检查更新
    });

    try {
      final versionInfo = await Future.any([
        SNservice.checkForUpdate(),
        Future.delayed(Duration(seconds: 3)).then((_) => throw TimeoutException('版本检查超时')),
      ]);

      if (versionInfo.containsKey('error')) {
        print(versionInfo['error']);
        // 不阻塞，继续离线流程
        setState(() {
          _isCheckingUpdate = false;
          _loadAppVersion();
          _loadUsernames();
        });
        return;
      }

      final latestVersion = versionInfo['latest_version'];
      final updateUrl = versionInfo['update_url'];
      final releaseNotes = versionInfo['release_notes'];

      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion = packageInfo.version;

      if (_isUpdateAvailable(currentVersion, latestVersion)) {
        _showUpdateDialog(latestVersion, updateUrl, releaseNotes);
      } else {
        print('当前已是最新版本');
        setState(() {
          _isCheckingUpdate = false;
          _loadAppVersion();
          _loadUsernames();
        });
      }
    } catch (e) {
      print('版本检查错误: $e');
      setState(() {
        _isCheckingUpdate = false;
        _loadAppVersion();
        _loadUsernames();
      });
    }
  }

  bool _isUpdateAvailable(String currentVersion, String latestVersion) {
    print('本地版本号: $currentVersion');
    print('服务器版本号: $latestVersion');
    List<String> currentParts = currentVersion.split('.');
    List<String> latestParts = latestVersion.split('.');
    int maxLength = currentParts.length > latestParts.length ? currentParts.length : latestParts.length;
    for (int i = 0; i < maxLength; i++) {
      int currentPart = i < currentParts.length ? int.parse(currentParts[i]) : 0;
      int latestPart = i < latestParts.length ? int.parse(latestParts[i]) : 0;
      if (currentPart < latestPart) {
        return true;
      } else if (currentPart > latestPart) {
        return false;
      }
    }
    return false;
  }

  void _showUpdateDialog(String latestVersion, String updateUrl, String releaseNotes) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text('新版本 $latestVersion 可用'),
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('请更新到版本 $latestVersion 以获得更好的体验。'),
            SizedBox(height: 10),
            Text('更新说明:'),
            Text(releaseNotes),
          ],
        ),
        actions: [
          TextButton(
            child: Text('稍后'),
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _isCheckingUpdate = false;
                _loadAppVersion();
                _loadUsernames();
              });
            },
          ),
          TextButton(
            child: Text('更新'),
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => DownloadApkPage(downloadUrl: updateUrl),
                ),
              );
              setState(() {
                _isCheckingUpdate = false;
                _loadAppVersion();
                _loadUsernames();
              });
            },
          ),
        ],
      ),
    );
  }

  // 登录功能（接入离线管理）
  void _login() async {
    String username = _selectedUsername ?? _usernameController.text.trim();
    if (username.isEmpty) {
      _showErrorDialog('用户名不能为空');
      return;
    }
    String password = _passwordController.text;

    // 如果处于手动离线或检测为离线，则直接走本地验证
    if (_manualOffline || !_isOnline) {
      await _validateLocalUser(username, password);
      return;
    }

    try {
      String? ipOrDomain = await DatabaseService.getIp();
      if (ipOrDomain == null) {
        _showErrorDialog('无法获取服务器地址，请检查设置', navigateToIpInput: true);
        return;
      }

      // 先 ping 一下，快速失败
      final reachable = await ApiService.checkIpOrDomain(ipOrDomain);
      if (!reachable) {
        _handleNetworkError('无法连接到服务器，请检查服务器是否开启');
        return;
      }

      bool success = await ApiService.login(username, password);
      if (success) {
        final existingUser = await DatabaseService.getUserByName(username);
        if (existingUser == null) {
          await DatabaseService.insertNewUser(username, password);
        } else {
          await DatabaseService.updateInUse(username, -1);
        }
        Navigator.pushReplacementNamed(context, '/home');
      } else {
        _showErrorDialog('用户名或密码错误或账号没有权限');
      }
    } catch (e) {
      // 网络失败回退本地验证
      await _validateLocalUser(username, password);
    }
  }

  Future<void> _validateLocalUser(String username, String password) async {
    try {
      final user = await DatabaseService.getUserByName(username);
      if (user == null) {
        _showErrorDialog('用户名不存在，请先在在线模式下登录', navigateToIpInput: true);
        return;
      }
      final ok = await DatabaseService.verifyUserPassword(username, password);
      if (!ok) {
        _showErrorDialog('密码错误');
        return;
      }
      await DatabaseService.updateInUse(username, -1);
      Navigator.pushReplacementNamed(context, '/home');
    } catch (e) {
      _showErrorDialog('离线验证失败: $e');
    }
  }

  void _enableUsernameEdit() {
    setState(() {
      _isEditingUsername = true;
      _usernameFocusNode.requestFocus();
    });
  }

  void _onUsernameFocusChange() {
    if (!_usernameFocusNode.hasFocus) {
      setState(() {
        _isEditingUsername = false;
        _selectedUsername = _usernameController.text.isNotEmpty ? _usernameController.text : _selectedUsername;
        if (_usernameController.text.isNotEmpty && !_usernames.contains(_usernameController.text)) {
          _usernames.add(_usernameController.text);
        }
      });
    }
  }

  void _deleteUsername(String username) async {
    await DatabaseService.deleteUsername(username);
    _loadUsernames();
  }

  // 添加缺失的 _showErrorDialog 方法
  void _showErrorDialog(String message, {bool navigateToIpInput = false}) {
    if (!mounted) return; // 如果组件已经被销毁，不显示对话框
    showDialog(
      context: context,
      barrierDismissible: false, // 防止点击对话框外部关闭
      builder: (context) => WillPopScope(
        onWillPop: () async => false, // 禁止返回键关闭对话框
        child: AlertDialog(
          title: Text('错误'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                if (navigateToIpInput) {
                  Navigator.pushReplacementNamed(context, '/ip_input');
                }
              },
              child: Text('确定'),
            ),
          ],
        ),
      ),
    );
  }

  InputDecoration _inputDecoration(String label) {
    return InputDecoration(
      labelText: label,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        shadowColor: Colors.transparent,
        surfaceTintColor: Colors.white,
        title: null,
        automaticallyImplyLeading: false,
        actions: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Icon(
              _isOnline ? Icons.cloud_done : Icons.cloud_off,
              color: _isOnline ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: _isLoading || _isCheckingUpdate
            ? Center(child: CircularProgressIndicator())
            : LayoutBuilder(
                builder: (context, constraints) {
                  final bottomInset = MediaQuery.of(context).viewInsets.bottom;
                  return SingleChildScrollView(
                    controller: _scrollController,
                    keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
                    padding: EdgeInsets.fromLTRB(16, 16, 16, 16 + bottomInset),
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        minHeight: constraints.maxHeight - 32,
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Align(
                            alignment: Alignment.center,
                            child: Image.asset('assets/logo.png', width: 180, height: 180),
                          ),
                          SizedBox(height: 24.0),
                          GestureDetector(
                            onDoubleTap: _enableUsernameEdit,
                            child: _isEditingUsername
                                ? TextField(
                                    controller: _usernameController,
                                    focusNode: _usernameFocusNode,
                                    decoration: _inputDecoration('用户名'),
                                    scrollPadding: EdgeInsets.only(
                                      bottom: MediaQuery.of(context).viewInsets.bottom + 120,
                                    ),
                                  )
                                : Container(
                                    decoration: BoxDecoration(
                                      border: Border.all(color: Colors.grey),
                                      borderRadius: BorderRadius.circular(8.0),
                                    ),
                                    padding: const EdgeInsets.symmetric(horizontal: 12.0),
                                    child: DropdownButton<String>(
                                      isExpanded: true,
                                      hint: Text('请选择用户名Nombre'),
                                      value: _selectedUsername,
                                      onChanged: (String? newValue) {
                                        setState(() {
                                          _selectedUsername = newValue;
                                        });
                                      },
                                      items: _usernames.map<DropdownMenuItem<String>>((String value) {
                                        return DropdownMenuItem<String>(
                                          value: value,
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(value),
                                              IconButton(
                                                icon: Icon(Icons.close, color: Colors.red),
                                                onPressed: () => _deleteUsername(value),
                                              ),
                                            ],
                                          ),
                                        );
                                      }).toList(),
                                      underline: SizedBox(),
                                    ),
                                  ),
                          ),
                          SizedBox(height: 16.0),
                          KeyedSubtree(
                            key: _passwordFieldKey,
                            child: TextField(
                              controller: _passwordController,
                              focusNode: _passwordFocusNode,
                              decoration: _inputDecoration('密码Contraseña'),
                              obscureText: true,
                              textInputAction: TextInputAction.done,
                              onSubmitted: (_) => _login(),
                              scrollPadding: EdgeInsets.only(
                                bottom: MediaQuery.of(context).viewInsets.bottom + 120,
                              ),
                            ),
                          ),
                          SizedBox(height: 24.0),
                          SizedBox(
                            width: 200,
                            height: 50,
                            child: ElevatedButton(
                              onPressed: _login,
                              child: Text('登录Acceder'),
                              style: ElevatedButton.styleFrom(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20),
                                ),
                              ),
                            ),
                          ),
                          SizedBox(height: 20),
                          Column(
                            children: [
                              Text('App Version: $_appVersion', style: TextStyle(fontSize: 12)),
                              SizedBox(height: 8),
                              Text(' 2024 悟空科技 版权所有', style: TextStyle(fontSize: 12)),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
      ),
    );
  }
}
