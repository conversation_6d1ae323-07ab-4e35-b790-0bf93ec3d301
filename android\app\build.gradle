plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

import java.util.Properties
import java.io.FileInputStream

def keystorePropertiesFile = rootProject.file("key.properties")
def keystoreProperties = new Properties()
keystoreProperties.load(new FileInputStream(keystorePropertiesFile))


android {
    namespace = "com.example.mistoer"
    compileSdk = 34 // 将其替换为实际的 SDK 版本号
    ndkVersion = "23.1.7779620" // 使用实际的 NDK 版本号，如果需要

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    defaultConfig {
        applicationId = "com.example.mistoer"
        minSdk = 21 // 使用实际的最低 SDK 版本号
        targetSdk = 34 // 使用实际的目标 SDK 版本号
        versionCode = 1 // 设置实际的版本号
        versionName = "1.10.0" // 设置实际的版本名称
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.aar'])
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'androidx.appcompat:appcompat-resources:1.7.0'
    // 应用内更新
    implementation 'com.google.android.play:app-update:2.0.0'
    implementation 'com.google.android.play:app-update-ktx:2.0.0'

    // 动态功能交付
    implementation 'com.google.android.play:feature-delivery:2.0.0'
    implementation 'com.google.android.play:feature-delivery-ktx:2.0.0'
}

repositories {
    flatDir {
        dirs 'libs'
    }
}

flutter {
    source = "../.."
}
