import 'package:flutter/material.dart';
import 'package:mistoer/services/scan_api_service.dart';
import 'package:mistoer/services/database_service.dart';
import 'package:flutter/scheduler.dart';
import 'dart:async';
import 'package:audioplayers/audioplayers.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:math';
import 'dart:io';
import 'dart:ui';
import 'package:mistoer/widgets/custom_keyboard.dart';
import 'package:mistoer/services/database_service.dart' as localdb;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:mistoer/services/offline_manager.dart';
import 'package:mistoer/screens/local_db_inspector_page.dart';
import 'package:shared_preferences/shared_preferences.dart';

// 磨玻璃弹窗组件
class GlassDialog extends StatelessWidget {
  final String? title;
  final Widget? content;
  final List<Widget>? actions;
  final bool barrierDismissible;
  final EdgeInsetsGeometry? contentPadding;
  final EdgeInsetsGeometry? titlePadding;
  final Widget? titleWidget;

  const GlassDialog({
    Key? key,
    this.title,
    this.content,
    this.actions,
    this.barrierDismissible = true,
    this.contentPadding,
    this.titlePadding,
    this.titleWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
      child: AlertDialog(
        backgroundColor: Colors.white.withOpacity(0.85),
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: BorderSide(color: Colors.white.withOpacity(0.3), width: 1.5),
        ),
        title: titleWidget ?? (title != null ? Text(
          title!,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ) : null),
        titlePadding: titlePadding ?? EdgeInsets.fromLTRB(24, 20, 24, 16),
        content: content,
        contentPadding: contentPadding ?? EdgeInsets.fromLTRB(24, 0, 24, 20),
        actions: actions?.map((action) {
          if (action is TextButton) {
            return Container(
              margin: EdgeInsets.symmetric(horizontal: 4),
              child: TextButton(
                onPressed: (action as TextButton).onPressed,
                style: TextButton.styleFrom(
                  backgroundColor: Colors.white.withOpacity(0.7),
                  foregroundColor: Colors.black87,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                ),
                child: (action as TextButton).child!,
              ),
            );
          }
          return action;
        }).toList(),
        actionsPadding: EdgeInsets.fromLTRB(16, 0, 16, 16),
      ),
    );
  }
}

// 磨玻璃进度弹窗组件
class GlassProgressDialog extends StatelessWidget {
  final String title;
  final String message;
  final Widget? progressWidget;

  const GlassProgressDialog({
    Key? key,
    required this.title,
    required this.message,
    this.progressWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
      child: AlertDialog(
        backgroundColor: Colors.white.withOpacity(0.85),
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: BorderSide(color: Colors.white.withOpacity(0.3), width: 1.5),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (progressWidget != null) progressWidget!,
            SizedBox(height: 16),
            Row(
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.withOpacity(0.8)),
                ),
                SizedBox(width: 20),
                Expanded(
                  child: Text(
                    message,
                    style: TextStyle(color: Colors.black87),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class ScanStockingPage extends StatefulWidget {
  @override
  _ScanStockingPageState createState() => _ScanStockingPageState();
}

class _ScanStockingPageState extends State<ScanStockingPage> with WidgetsBindingObserver {
  TextEditingController warehouseController = TextEditingController();
  TextEditingController barcodeController = TextEditingController();
  FocusNode warehouseFocusNode = FocusNode();
  FocusNode barcodeFocusNode = FocusNode();

  List<Map<String, dynamic>> scanResults = [];
  String lastScannedWarehouse = '';
  String username = '';
  int inUseStatus = 0;
  bool isDuplicateCheckEnabled = true;
  bool isManualModeEnabled = false; // 添加手动模式开关状态

  // 列可见性控制
  bool hideIdColumn = false;
  bool hideWarehouseColumn = false;
  bool hideBarcodeColumn = false;
  bool hideArticleColumn = false;
  bool hideQuantityColumn = false;
  bool hideActionColumn = false;

  ScrollController _horizontalScrollController = ScrollController();
  ScrollController _dataHorizontalScrollController = ScrollController();
  ScrollController _verticalScrollController = ScrollController();

  bool isScrolled = false;
  late AudioPlayer _audioPlayer;

  Set<int> synchronizedIds = {}; // 本地存储已同步的记录ID

  bool _isWarehouseKeyboardVisible = false;
  bool _isBarcodeKeyboardVisible = false;
  bool _isSearchKeyboardVisible = false;
  bool _isDialogKeyboardVisible = false;
  TextEditingController? _dialogQtyController;
  FocusNode? _dialogQtyFocusNode;
    void Function(String value)? _dialogOnSubmit;
 
   // 扫描输入挂起（弹窗期间暂停扫描处理）
   bool _suspendScanning = false;
 
   // 自定义键盘高度（用于弹窗上移避免遮挡）
   static const double _customKeyboardHeight = 280.0;
 
   // 覆盖层键盘（确保在弹窗之上）
  OverlayEntry? _dialogKeyboardOverlay;

  void _showDialogKeyboardOverlay({required TextEditingController controller, required FocusNode focusNode}) {
    // 若已存在则不重复插入
    if (_dialogKeyboardOverlay != null) return;
    final OverlayState? overlay = Navigator.of(context).overlay;
    if (overlay == null) return;
    _dialogKeyboardOverlay = OverlayEntry(
      builder: (ctx) => Positioned(
        left: 0,
        right: 0,
        bottom: 0,
        child: SafeArea(
          top: false,
          child: Material(
            color: Colors.transparent,
            child: Container(
              color: Colors.grey[200],
              child: CustomKeyboard(
                controller: controller,
                onSubmit: (value) {
                  controller.text = value;
                  // 回车键等于确定按钮，触发提交逻辑
                  if (_dialogOnSubmit != null) {
                    _dialogOnSubmit!(value);
                  }
                  // 安全地保持焦点在弹窗输入框
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (mounted && focusNode.canRequestFocus) {
                      try {
                        FocusScope.of(context).requestFocus(focusNode);
                      } catch (e) {
                        // 忽略焦点请求错误
                      }
                    }
                  });
                },
                showKeyboard: true,
                onKeyboardVisibilityChanged: () {},
              ),
            ),
          ),
        ),
      ),
    );
    overlay.insert(_dialogKeyboardOverlay!);
  }

  void _removeDialogKeyboardOverlay() {
    try {
      _dialogKeyboardOverlay?.remove();
    } catch (_) {}
    _dialogKeyboardOverlay = null;
  }

  // 处理数量提交的通用方法
  void _handleQuantitySubmit(TextEditingController controller, String input, bool isBarcode, bool isSetMode) {
    String quantityStr = controller.text.trim();
    int quantity = 1;

    try {
      quantity = int.parse(quantityStr);
      if (quantity <= 0) throw FormatException('数量必须大于0');
    } catch (e) {
      if (mounted) {
        _showSnackBar('请输入有效的数量');
      }
      return;
    }

    Navigator.pop(context);
    _uploadStocktakingData(input, quantity, updateMode: isSetMode ? 'set' : 'add', isBarcode: isBarcode);
  }

  // 处理新记录数量提交
  void _handleQuantitySubmitForNewRecord(TextEditingController controller, String barcode, bool isSetMode) {
    String quantityStr = controller.text.trim();
    int quantity = 1;

    try {
      quantity = int.parse(quantityStr);
      if (quantity <= 0) throw FormatException('数量必须大于0');
    } catch (e) {
      if (mounted) {
        _showSnackBar('请输入有效的数量');
      }
      return;
    }

    Navigator.pop(context);
    _uploadStocktakingData(barcode, quantity, updateMode: isSetMode ? 'set' : 'add', forceNewRecord: true);
  }

  // 处理更新数量提交
  void _handleQuantitySubmitForUpdate(TextEditingController controller, String barcode, String warehouse, int existingQuantity, String articleId, int? recordId, bool isSetMode) {
    String quantityStr = controller.text.trim();
    int quantity = 1;

    try {
      quantity = int.parse(quantityStr);
      if (quantity <= 0) throw FormatException('数量必须大于0');
    } catch (e) {
      if (mounted) {
        _showSnackBar('请输入有效的数量');
      }
      return;
    }

    Navigator.pop(context);
    _updateStocktakingRecord(barcode, warehouse, quantity, articleId, recordId: recordId, updateMode: isSetMode ? 'set' : 'add');
  }

  // 显示SnackBar消息
  void _showSnackBar(String message, {Color? backgroundColor, Duration? duration}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          duration: duration ?? Duration(seconds: 2),
        ),
      );
    }
  }

  // 更新盘点记录
  Future<void> _updateStocktakingRecord(String barcode, String warehouse, int quantity, String articleId, {int? recordId, String updateMode = 'add'}) async {
    // 这里应该实现更新记录的逻辑
    // 暂时使用 _uploadStocktakingData 作为替代
    await _uploadStocktakingData(barcode, quantity, updateMode: updateMode);
  }







  // 添加一个全局键来安全地访问ScaffoldMessenger
  final GlobalKey<ScaffoldMessengerState> _scaffoldMessengerKey = GlobalKey<ScaffoldMessengerState>();

  // ===== 网络检测相关 =====
  bool isOnline = false; // 当前是否在线（从 OfflineManager 同步）
  bool manualOffline = false; // 手动离线状态
  Timer? _networkTimer; // 周期性检测定时器
  // =======================

  // ===== 下载进度相关 =====
  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  String _downloadStatus = '';
  ValueNotifier<double> _downloadProgressVN = ValueNotifier(0.0);
  ValueNotifier<String> _downloadStatusVN = ValueNotifier('');
  // =======================

  // ===== UI相关 =====
  // =================

  // ===== 搜索模式 =====
  bool _isSearchMode = false;
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  String _searchQuery = '';
  List<Map<String, dynamic>> _filteredResults = [];
  // =====================

  // 批量下载产品数据
  Future<void> _downloadProductData({bool incremental = false, bool ignoreOnlineCheck = false}) async {
    if (!isOnline && !ignoreOnlineCheck) {
      _showSnackBar('需要网络连接才能下载产品数据', backgroundColor: Colors.red);
      return;
    }
    
    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
      _downloadStatus = '准备下载...';
      _downloadProgressVN.value = 0.0;
      _downloadStatusVN.value = '准备下载...';
    });
    
    _showDownloadProgressDialog();
    
    try {
      if (incremental) {
        await _performIncrementalSync();
      } else {
        await _performFullDownload();
      }

      _showSnackBar('产品数据下载完成', backgroundColor: Colors.green);
      _audioPlayer.play(AssetSource('true.mp3'));
    } catch (e) {
      _showSnackBar('下载失败: $e', backgroundColor: Colors.red);
    } finally {
      setState(() {
        _isDownloading = false;
        _downloadProgress = 0.0;
        _downloadStatus = '';
        _downloadProgressVN.value = 0.0;
        _downloadStatusVN.value = '';
      });
      if (mounted && Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
    }
  }
  
  // 执行完整下载
  Future<void> _performFullDownload() async {
    setState(() {
      _downloadStatus = '获取产品总数...';
      _downloadStatusVN.value = '获取产品总数...';
    });
    
    final totalCount = await ScanApiService.getArticulosCount();
    if (totalCount == 0) {
      throw Exception('无法获取产品总数');
    }
    
    const batchSize = 1000;
    int downloaded = 0;
    
    for (int offset = 0; offset < totalCount; offset += batchSize) {
      setState(() {
        _downloadStatus = '下载中... ($downloaded/$totalCount)';
        _downloadProgress = downloaded / totalCount;
        _downloadStatusVN.value = _downloadStatus;
        _downloadProgressVN.value = _downloadProgress;
      });
      
      final batch = await ScanApiService.getArticulosBatch(
        offset: offset,
        limit: batchSize,
      );
      
      if (batch.isNotEmpty) {
        await DatabaseService.batchUpsertArticulos(batch);
        downloaded += batch.length as int;
      }
    }
    
    // 更新同步时间
    await DatabaseService.setLastSyncTime('articulos', DateTime.now().toIso8601String());
    
    setState(() {
      _downloadProgress = 1.0;
      _downloadStatus = '下载完成';
      _downloadProgressVN.value = 1.0;
      _downloadStatusVN.value = '下载完成';
    });
  }
  
  // 执行增量同步
  Future<void> _performIncrementalSync() async {
    setState(() {
      _downloadStatus = '检查更新...';
      _downloadStatusVN.value = '检查更新...';
    });
    
    final lastSyncTime = await DatabaseService.getLastSyncTime('articulos');
    if (lastSyncTime == null) {
      // 如果没有同步记录，执行完整下载
      await _performFullDownload();
      return;
    }
    
    final updatedArticulos = await ScanApiService.getUpdatedArticulos(lastSyncTime);
    
    if (updatedArticulos.isEmpty) {
      setState(() {
        _downloadStatus = '数据已是最新';
        _downloadStatusVN.value = '数据已是最新';
      });
      return;
    }
    
    setState(() {
      _downloadStatus = '同步更新数据...';
      _downloadProgress = 0.5;
      _downloadStatusVN.value = '同步更新数据...';
      _downloadProgressVN.value = 0.5;
    });
    
    await DatabaseService.batchUpsertArticulos(updatedArticulos);
    await DatabaseService.setLastSyncTime('articulos', DateTime.now().toIso8601String());
    
    setState(() {
      _downloadProgress = 1.0;
      _downloadStatus = '同步完成';
      _downloadProgressVN.value = 1.0;
      _downloadStatusVN.value = '同步完成';
    });
  }
  
  // 检查数据完整性
  Future<void> _checkDataIntegrity() async {
    if (!isOnline) {
      _showSnackBar('需要网络连接才能检查数据完整性', backgroundColor: Colors.red);
      return;
    }
    
    try {
      final result = await ScanApiService.checkDataIntegrity();
      final localStats = await DatabaseService.getArticulosStats();
      
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('数据完整性检查'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('本地产品总数: ${localStats['total']}'),
              Text('本地有条码产品: ${localStats['withBarcode']}'),
              if (result['success'] == true) ...[
                Text('服务器产品总数: ${result['serverCount'] ?? 'N/A'}'),
                Text('数据一致性: ${result['consistent'] == true ? '正常' : '异常'}'),
              ] else
                Text('服务器检查失败: ${result['message']}'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('关闭'),
            ),
          ],
        ),
      );
    } catch (e) {
      _showSnackBar('检查失败: $e', backgroundColor: Colors.red);
    }
  }
  
  // 显示产品数据状态
  Future<void> _showProductDataStatus() async {
    final stats = await DatabaseService.getArticulosStats();
    final lastSyncTime = await DatabaseService.getLastSyncTime('articulos');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('产品数据状态'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('本地产品总数: ${stats['total']}'),
            Text('有条码产品: ${stats['withBarcode']}'),
            Text('最后同步时间: ${lastSyncTime ?? '从未同步'}'),
            SizedBox(height: 16),
            if (_isDownloading) ...[
              LinearProgressIndicator(value: _downloadProgress),
              SizedBox(height: 8),
              Text(_downloadStatus),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('关闭'),
          ),
        ],
      ),
    );
  }

  // ===== 产品数据下载优化 =====
  bool _isAutoSyncing = false;
  bool _prevOnline = false;

  void _showDownloadProgressDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (ctx) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.cloud_download,
                  color: Colors.blue,
                  size: 24,
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  '正在下载商品数据',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          content: Container(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 进度条容器
                Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Column(
                    children: [
                      // 进度百分比显示
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '下载进度',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          ValueListenableBuilder<double>(
                            valueListenable: _downloadProgressVN,
                            builder: (context, v, _) {
                              final percentage = (v * 100).clamp(0.0, 100.0);
                              return Text(
                                '${percentage.toStringAsFixed(1)}%',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.blue,
                                  fontWeight: FontWeight.w600,
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                      SizedBox(height: 12),
                      // 进度条
                      ValueListenableBuilder<double>(
                        valueListenable: _downloadProgressVN,
                        builder: (context, v, _) {
                          return Container(
                            height: 8,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(6),
                              color: Colors.grey.shade200,
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(6),
                              child: LinearProgressIndicator(
                                value: (v <= 0 || v.isNaN) ? null : v.clamp(0.0, 1.0),
                                backgroundColor: Colors.transparent,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  v >= 1.0 ? Colors.green : Colors.blue,
                                ),
                                minHeight: 8,
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 16),
                // 状态信息
                Row(
                  children: [
                    ValueListenableBuilder<String>(
                      valueListenable: _downloadStatusVN,
                      builder: (context, status, _) {
                        IconData statusIcon;
                        Color statusColor;
                        
                        if (status.contains('完成')) {
                          statusIcon = Icons.check_circle;
                          statusColor = Colors.green;
                        } else if (status.contains('检查') || status.contains('准备')) {
                          statusIcon = Icons.search;
                          statusColor = Colors.orange;
                        } else if (status.contains('下载') || status.contains('同步')) {
                          statusIcon = Icons.download;
                          statusColor = Colors.blue;
                        } else {
                          statusIcon = Icons.info;
                          statusColor = Colors.grey;
                        }
                        
                        return Container(
                          padding: EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: statusColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Icon(
                            statusIcon,
                            color: statusColor,
                            size: 16,
                          ),
                        );
                      },
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: ValueListenableBuilder<String>(
                        valueListenable: _downloadStatusVN,
                        builder: (context, s, _) {
                          return Text(
                            s.isEmpty ? '请稍候...' : s,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade700,
                              height: 1.4,
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16),
                // 提示信息
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.withOpacity(0.2)),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.blue,
                        size: 16,
                      ),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '下载过程中请保持网络连接，不要关闭应用',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue.shade700,
                            height: 1.3,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            // 完成后显示关闭按钮
            ValueListenableBuilder<String>(
              valueListenable: _downloadStatusVN,
              builder: (context, status, _) {
                if (status.contains('完成') || status.contains('已是最新')) {
                  return TextButton.icon(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(Icons.check, size: 16),
                    label: Text(
                      '完成',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.green,
                      minimumSize: const Size(0, 44),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                    ),
                  );
                }
                return SizedBox.shrink();
              },
            ),
          ],
        );
      },
    );
  }

  // 手动离线开关（优先级高于自动检测）

  StreamSubscription<List<ConnectivityResult>>? _connSub; // 监听系统网络变化（v6返回列表）

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _audioPlayer = AudioPlayer();

    SchedulerBinding.instance.addPostFrameCallback((_) {
      loadUsername();
      // 不使用持久化存储，仅在应用运行期间记录已同步的ID
      _loadLastWarehouse(); // 加载上次保存的库位值
      _setInitialFocus();
      setState(() {
        // 初始化时键盘默认隐藏
        _isWarehouseKeyboardVisible = false;
        _isBarcodeKeyboardVisible = false;
      });
      // 同步全局在线状态
      isOnline = OfflineManager.instance.isOnline.value;
      _prevOnline = isOnline;
      OfflineManager.instance.isOnline.addListener(() {
        if (!mounted) return;
        setState(() {
          isOnline = OfflineManager.instance.isOnline.value;
        });
        // 自动重连后同步离线记录
        if (!_prevOnline && isOnline) {
          _autoSyncOfflineRecordsSilent();
        }
        _prevOnline = isOnline;
      });
      manualOffline = OfflineManager.instance.manualOffline;

      // 启动原有定时 ping（保留，避免接口变更影响）
      _startNetworkMonitor();
      _refreshNetworkStatus();

      // 订阅系统网络变化（WiFi/移动网络）
      _connSub = Connectivity().onConnectivityChanged.listen((List<ConnectivityResult> results) async {
        if (manualOffline) return; // 手动离线时不改变 isOnline
        if (!results.any((r) => r != ConnectivityResult.none)) {
          if (!mounted) return;
          setState(() => isOnline = false);
          return;
        }
        await _refreshNetworkStatus();
      });
    });

    _horizontalScrollController.addListener(() {
      _dataHorizontalScrollController.jumpTo(_horizontalScrollController.offset);
    });
    _dataHorizontalScrollController.addListener(() {
      _horizontalScrollController.jumpTo(_dataHorizontalScrollController.offset);
    });

    _verticalScrollController.addListener(() {
      if (_verticalScrollController.offset > 0 && !isScrolled) {
        setState(() {
          isScrolled = true;
        });
      } else if (_verticalScrollController.offset <= 0 && isScrolled) {
        setState(() {
          isScrolled = false;
        });
      }
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      fetchOnlineData();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _horizontalScrollController.dispose();
    _dataHorizontalScrollController.dispose();
    _verticalScrollController.dispose();
    _audioPlayer.dispose();
    _downloadProgressVN.dispose();
    _downloadStatusVN.dispose();
    warehouseController.dispose();
    barcodeController.dispose();

    // 保存库位值到本地存储，下次进入时可以恢复
    _saveLastWarehouse();

    super.dispose();
  }

  Future<void> loadUsername() async {
    final db = await DatabaseService.database;
    final List<Map<String, dynamic>> result = await db.query('empleados', where: 'in_use = ?', whereArgs: [-1]);
    if (result.isNotEmpty) {
      setState(() {
        username = result.first['Nombre'];
        inUseStatus = result.first['in_use'];
      });
      fetchOnlineData();
    }
  }

  // 保存最后扫描的库位值到本地存储
  Future<void> _saveLastWarehouse() async {
    if (lastScannedWarehouse.isNotEmpty) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('lastScannedWarehouse', lastScannedWarehouse);
      print('库位值已保存: $lastScannedWarehouse');
    }
  }

  // 从本地存储加载最后扫描的库位值
  Future<void> _loadLastWarehouse() async {
    final prefs = await SharedPreferences.getInstance();
    final savedWarehouse = prefs.getString('lastScannedWarehouse');
    if (savedWarehouse != null && savedWarehouse.isNotEmpty) {
      setState(() {
        lastScannedWarehouse = savedWarehouse;
        // 不在库位输入框中显示值，保持输入框清洁
        // warehouseController.text = savedWarehouse;
      });
      print('库位值已恢复: $savedWarehouse');
    }
  }

  // ===== 网络检测实现 =====
  Future<void> _refreshNetworkStatus({bool showSnack = false}) async {
    final online = manualOffline ? false : await _checkOnline();
    if (!mounted) return;
    setState(() {
      isOnline = online;
    });
    if (showSnack) {
      _showSnackBar(online ? '已连接服务器' : '当前离线',
          backgroundColor: online ? Colors.green : Colors.orange);
    }
  }

  Future<bool> _checkOnline() async {
    try {
      final baseUrl = await ScanApiService.getBaseUrl();
      final url = Uri.parse('$baseUrl/ping');
      final res = await http.get(url).timeout(const Duration(seconds: 3));
      return res.statusCode == 200;
    } catch (_) {
      return false;
    }
  }

  void _startNetworkMonitor() {
    _networkTimer?.cancel();
    _networkTimer = Timer.periodic(Duration(seconds: 5), (timer) async {
      await _refreshNetworkStatus();
      if (isOnline && !_prevOnline) {
        // 网络从离线恢复到在线
        // _autoSyncOfflineRecordsSilent();
      }
      _prevOnline = isOnline;
    });
  }
  // =======================

  Future<void> fetchOnlineData() async {
    if (!isOnline) {
      // 离线：读取本地 stocktaking（服务器字段名）
      final local = await localdb.DatabaseService.getLocalStocktakingDataForUser(username);
      setState(() {
        scanResults = local
            .map((row) => {
                  'id': row['id'],
                  'warehouse': row['Weizhi'] ?? '',
                  'barcode': row['CodigoBarra'] ?? '',
                  'articleId': row['ArticuloID'] ?? '',
                  'quantity': int.tryParse((row['Stock'] ?? '0').toString()) ?? 0,
                  'repeat': false,
                  'isSynced': (row['isSynced'] ?? 0) == 1,
                  'isUnknown': (row['isUnknown'] ?? 0) == 1,
                  'oldStock': row['oldstock'] ?? '',
                  'hasDuplicateLocation': (row['oldstock'] ?? '').toString().startsWith('DUP:'),
                })
            .toList();
      });
      scanResults.sort((a, b) => (b['id'] ?? 0).compareTo(a['id'] ?? 0));
      _setInitialFocus();
      return;
    }
    final data = await ScanApiService.getStocktakingDataForUser(username);
    setState(() {
      scanResults = data.map((item) => {
        'id': item['id'],
        'warehouse': item['Weizhi'],
        'barcode': item['CodigoBarra'],
        'articleId': item['ArticuloID'],
        'quantity': item['Stock'] is String ? int.tryParse(item['Stock']) ?? 0 : item['Stock'],
        'repeat': false,
        'isSynced': synchronizedIds.contains(item['id']),
        'isUnknown': item['isUnknown'] ?? false,
        'oldStock': item['oldstock'] ?? '',
        'hasDuplicateLocation': (item['oldstock'] ?? '').toString().startsWith('DUP:'),
      }).toList();
    });
    scanResults.sort((a, b) => b['id'].compareTo(a['id']));
    _setInitialFocus();
  }

  // 智能设置初始焦点：如果没有库位数据，聚焦到库位输入框；否则聚焦到条码输入框
  void _setInitialFocus() {
    if (!mounted) return;

    // 检查是否有库位数据
    bool hasWarehouseData = scanResults.isNotEmpty &&
        scanResults.any((item) =>
            (item['warehouse']?.toString() ?? '').trim().isNotEmpty);

    if (hasWarehouseData) {
      // 有库位数据，聚焦到条码输入框
      FocusScope.of(context).requestFocus(barcodeFocusNode);
      print('有库位数据，聚焦到条码输入框');
    } else {
      // 没有库位数据，聚焦到库位输入框
      FocusScope.of(context).requestFocus(warehouseFocusNode);
      print('没有库位数据，聚焦到库位输入框');
    }
  }

  // 构建统计行的辅助方法
  Widget _buildStatRow(String label, String value, IconData icon, [Color? iconColor]) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: iconColor ?? Colors.grey[600],
          ),
          SizedBox(width: 8),
          Text(
            '$label: ',
            style: TextStyle(
              color: Colors.grey[700],
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.black87,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }



  void handleScan(String type, String value) async {
    if (_suspendScanning) {
      // 弹窗期间暂停任何扫描输入，避免误判为新扫描记录
      return;
    }
    if (type == 'warehouse') {
      if (value != lastScannedWarehouse) {
        setState(() {
          lastScannedWarehouse = value;
        });
        _saveLastWarehouse(); // 立即保存库位值
        if (mounted) {
          _showSnackBar('扫描库位成功: $value');
          _audioPlayer.play(AssetSource('wz.mp3'));
        }
      } else {
        if (mounted) {
          _showSnackBar('该库位已经存在.');
        }
      }
      setState(() {
        warehouseController.clear();
        // 库位扫描完成后，自动切换焦点到条码输入框
        FocusScope.of(context).requestFocus(barcodeFocusNode);
        
        // 如果键盘已显示，则切换到条码键盘
        if (_isWarehouseKeyboardVisible || _isBarcodeKeyboardVisible) {
          _isWarehouseKeyboardVisible = false;
          _isBarcodeKeyboardVisible = true;
        }
      });
    } else if (type == 'barcode') {
      if (value.isEmpty) {
        _showSnackBar('条码输入不能为空');
        return;
      }

      // 检查是否有库位值（优先检查已扫描的库位，其次检查输入框中的值）
      String currentWarehouse = lastScannedWarehouse.isNotEmpty
          ? lastScannedWarehouse
          : warehouseController.text.trim();

      if (currentWarehouse.isEmpty) {
        // 显示磨玻璃提示弹窗
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return GlassDialog(
              title: '请先输入库位',
              titleWidget: Row(
                children: [
                  Icon(
                    Icons.warning,
                    color: Colors.orange[600],
                    size: 24,
                  ),
                  SizedBox(width: 8),
                  Text(
                    '请先输入库位',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                      fontSize: 18,
                    ),
                  ),
                ],
              ),
              content: Text(
                '在扫描或输入商品条码之前，请先在库位输入框中输入库位信息。',
                style: TextStyle(color: Colors.black87),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    // 使用 Future.delayed 确保弹窗完全关闭后再设置焦点
                    Future.delayed(Duration(milliseconds: 200), () {
                      if (mounted) {
                        // 清空条码输入框
                        barcodeController.clear();

                        // 先更新键盘状态
                        setState(() {
                          _isBarcodeKeyboardVisible = false;
                          _isWarehouseKeyboardVisible = true;
                        });

                        // 再设置焦点到库位输入框
                        Future.delayed(Duration(milliseconds: 100), () {
                          if (mounted) {
                            warehouseFocusNode.requestFocus();
                            print('焦点已切换到库位输入框');
                          }
                        });
                      }
                    });
                  },
                  style: TextButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                  child: Text('确定'),
                ),
              ],
            );
          },
        );
        return;
      }

      // 如果使用的是输入框中的值（而不是已扫描的值），则更新 lastScannedWarehouse
      if (lastScannedWarehouse.isEmpty && currentWarehouse.isNotEmpty) {
        lastScannedWarehouse = currentWarehouse;
        _saveLastWarehouse(); // 保存手动输入的库位值
        print('库位已更新为: $currentWarehouse');
      }

      // 判断输入是条码还是货号（优先使用本地表判定）
      bool isBarcode = await _isInputBarcode(value);

      // 检查各种重复情况
      bool barcodeExistsInSameWarehouse = false; // 相同库位相同条码
      bool barcodeExistsInAnotherWarehouse = false; // 相同条码不同库位
      bool articleIdExistsInSameWarehouse = false; // 相同库位相同货号
      bool articleIdExistsInAnotherWarehouse = false; // 不同库位相同货号
      
      String existingWarehouse = '';
      List<String> existingWarehouses = []; // 存储所有存在此条码的库位
      String articleId = '';
      int existingQuantity = 0;

      if (isDuplicateCheckEnabled) {
        // 先通过 本地或API 获取当前输入对应的货号(如果有)
        String currentArticleId = '';
        if (isBarcode) {
          try {
            if (isOnline) {
              final response = await ScanApiService.getArticleIdByBarcode(value);
              currentArticleId = response['articleId'] ?? '';
            } else {
              final localRow = await localdb.DatabaseService.getArticuloByBarcode(value);
              currentArticleId = (localRow != null ? (localRow['ArticuloID']?.toString() ?? '') : '');
            }
          } catch (e) {
            // 如果获取失败，继续处理
            print('获取货号失败(本地/在线): $e');
          }
        } else {
          // 输入本身是货号
          currentArticleId = value;
        }

        // 检查所有可能的重复情况
        for (var result in scanResults) {
          // 相同库位相同条码
          if (result['warehouse'] == lastScannedWarehouse && result['barcode'] == value) {
            barcodeExistsInSameWarehouse = true;
            existingQuantity = result['quantity'] is int ? result['quantity'] : 0;
            articleId = result['articleId']?.toString() ?? '';
            break;
          }
          
          // 相同条码不同库位
          if (result['barcode'] == value && result['warehouse'] != lastScannedWarehouse) {
            barcodeExistsInAnotherWarehouse = true;
            String warehouse = result['warehouse']?.toString() ?? '';
            if (warehouse.isNotEmpty && warehouse != result['barcode']) {
              existingWarehouses.add(warehouse);
              existingWarehouse = warehouse; // 保留最后一个找到的库位
            }
            articleId = result['articleId']?.toString() ?? '';
          }
          
          // 如果已经知道当前条码的货号，检查相同货号的情况
          if (currentArticleId.isNotEmpty) {
            if (result['warehouse'] == lastScannedWarehouse && 
                result['articleId']?.toString() == currentArticleId && 
                result['barcode'] != value) {
              if (!barcodeExistsInSameWarehouse) {
                articleIdExistsInSameWarehouse = true;
              }
            }
            
            if (result['warehouse'] != lastScannedWarehouse && 
                result['articleId']?.toString() == currentArticleId) {
              articleIdExistsInAnotherWarehouse = true;
              existingWarehouse = result['warehouse']?.toString() ?? '';
            }
          }
        }
      }

      // 简化处理逻辑，根据重复情况处理：
      if (isManualModeEnabled) {
        // 手动模式下，不论是否重复，都弹出数量输入框
        if (barcodeExistsInSameWarehouse) {
          // 相同库位相同条码，显示更新数量对话框
          _showUpdateQuantityDialog(value, lastScannedWarehouse, existingQuantity, articleId, recordId: (scanResults.firstWhere((e) => e['warehouse'] == lastScannedWarehouse && e['barcode'] == value, orElse: () => {})['id'] as int?) );
        } else if (barcodeExistsInAnotherWarehouse && isDuplicateCheckEnabled) {
          // 相同条码不同库位，显示条码在其他库位对话框
          _showBarcodeInOtherWarehouseDialog(value, existingWarehouse, articleId, existingWarehouses);
        } else if (articleIdExistsInSameWarehouse && isDuplicateCheckEnabled) {
          // 相同库位相同货号，不再显示提示，直接提示输入数量
          _promptQuantityInput(value, isBarcode: isBarcode);
        } else if (articleIdExistsInAnotherWarehouse && isDuplicateCheckEnabled) {
          // 不同库位相同货号，显示货号在其他库位对话框
          _showArticleInOtherWarehouseDialog(value, existingWarehouse);
        } else {
          // 没有重复情况，显示数量输入框
          _promptQuantityInput(value, isBarcode: isBarcode);
        }
      } else {
        // 非手动模式下，根据情况直接处理，并自动设置数量为1
        if (barcodeExistsInSameWarehouse) {
          // 相同库位相同条码，直接更新数量（增加1）
          _uploadStocktakingData(value, 1, updateMode: 'add', isBarcode: isBarcode);
        } else if (barcodeExistsInAnotherWarehouse && isDuplicateCheckEnabled) {
          // 相同条码不同库位，显示对话框
          _showBarcodeInOtherWarehouseDialog(value, existingWarehouse, articleId, existingWarehouses);
        } else if (articleIdExistsInSameWarehouse && isDuplicateCheckEnabled) {
          // 相同库位相同货号，不再显示提示，直接累加数量
          _uploadStocktakingData(value, 1, updateMode: 'add', isBarcode: isBarcode);
        } else if (articleIdExistsInAnotherWarehouse && isDuplicateCheckEnabled) {
          // 不同库位相同货号，显示对话框
          _showArticleInOtherWarehouseDialog(value, existingWarehouse);
        } else {
          // 没有重复情况，直接添加新记录
          _uploadStocktakingData(value, 1, updateMode: 'add', isBarcode: isBarcode);
        }
      }
    }
  }

  // 显示相同库位相同条码的对话框，询问是否更新数量
  void _showUpdateQuantityDialog(String barcode, String warehouse, int existingQuantity, String articleId, {int? recordId}) {
    TextEditingController quantityController = TextEditingController();
    // 输入框默认为空
    quantityController.text = '';

    // 添加更新模式选择
    bool isSetMode = false; // 默认为叠加数量模式

    // 打开弹窗时切换到对话框自定义键盘
    setState(() {
      _suspendScanning = true;
      _dialogQtyController = quantityController;
      _dialogQtyFocusNode = FocusNode();
      // 设置回车提交逻辑
      _dialogOnSubmit = (String value) {
        // 执行确定按钮的逻辑
        _handleQuantitySubmitForUpdate(quantityController, barcode, warehouse, existingQuantity, articleId, recordId, isSetMode);
      };
      _isWarehouseKeyboardVisible = false;
      _isBarcodeKeyboardVisible = false;
      _isSearchKeyboardVisible = false;
      _isDialogKeyboardVisible = true;
    });
    // 在弹窗之上显示覆盖层键盘
    _showDialogKeyboardOverlay(controller: quantityController, focusNode: _dialogQtyFocusNode!);
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, dialogSetState) {
            final bottomInset = _isDialogKeyboardVisible ? _customKeyboardHeight : 0.0;
            return Padding(
              padding: EdgeInsets.only(bottom: bottomInset),
              child: GlassDialog(
              title: '条码: $barcode',
              contentPadding: EdgeInsets.all(16.0),
              content: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(color: Colors.blue.withOpacity(0.5)),
                      ),
                      child: Text(
                        '现有数量: $existingQuantity',
                        style: TextStyle(fontSize: 14, color: Colors.blue, fontWeight: FontWeight.w500),
                      ),
                    ),
                    SizedBox(height: 16),
                    Container(
                      height: 40,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(4.0),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                dialogSetState(() {
                                  isSetMode = false;
                                  // 不再自动设置默认值
                                  // quantityController.text = '1';
                                });
                              },
                              child: Container(
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  color: !isSetMode ? Colors.blue.withOpacity(0.1) : null,
                                  borderRadius: BorderRadius.horizontal(left: Radius.circular(3)),
                                ),
                                child: Text('叠加',
                                  style: TextStyle(
                                    fontWeight: !isSetMode ? FontWeight.bold : FontWeight.normal,
                                    color: !isSetMode ? Colors.blue : Colors.black,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Container(width: 1, color: Colors.grey.shade300),
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                dialogSetState(() {
                                  isSetMode = true;
                                  // 不再自动设置默认值
                                  // quantityController.text = (existingQuantity + 1).toString();
                                });
                              },
                              child: Container(
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  color: isSetMode ? Colors.blue.withOpacity(0.1) : null,
                                  borderRadius: BorderRadius.horizontal(right: Radius.circular(3)),
                                ),
                                child: Text('替换',
                                  style: TextStyle(
                                    fontWeight: isSetMode ? FontWeight.bold : FontWeight.normal,
                                    color: isSetMode ? Colors.blue : Colors.black,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 12),
                    TextField(
                      controller: quantityController,
                      focusNode: _dialogQtyFocusNode,
                      readOnly: false,
                      showCursor: true,
                      enableInteractiveSelection: false,
                      keyboardType: TextInputType.none,
                      decoration: InputDecoration(
                        labelText: isSetMode ? '设置总数量' : '叠加数量',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(4.0),
                        ),
                        contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                        hintText: isSetMode ? '输入新总数量' : '输入叠加数量',
                      ),
                      autofocus: true,
                    ),
                    SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            barcodeController.clear();
                            FocusScope.of(context).requestFocus(barcodeFocusNode);
                          },
                          child: Text('取消'),
                        ),
                        SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: () async {
                            int newQuantity;
                            try {
                              newQuantity = int.parse(quantityController.text);
                              if (newQuantity <= 0) throw FormatException('数量必须大于0');
                            } catch (e) {
                              _showSnackBar('请输入有效的数量');
                              return;
                            }
                            
                            Navigator.of(context).pop();
                            
                            try {
                              await ScanApiService.updateStocktakingQuantity(
                                warehouse, 
                                barcode, 
                                newQuantity, 
                                username, 
                                inUseStatus,
                                updateMode: isSetMode ? 'set' : 'add',
                                forceInsert: false,
                                allowUnknown: false,
                                recordId: recordId, // 从函数参数传入的当前记录id
                              );
                              
                              String modeText = isSetMode ? '设置为' : '增加了';
                              _showSnackBar('数量已$modeText: $newQuantity');
                              _audioPlayer.play(AssetSource('scan.mp3'));
                              fetchOnlineData();
                              barcodeController.clear();
                              FocusScope.of(context).requestFocus(barcodeFocusNode);
                            } catch (e) {
                              _showSnackBar('更新数量失败: $e');
                            }
                          },
                          child: Text('确认'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4.0),
                            ),
                            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          }
        );
      },
    ).whenComplete(() {
      // 关闭弹窗时关闭对话框键盘（覆盖层）
      _removeDialogKeyboardOverlay();
      setState(() {
        _suspendScanning = false;
        _isDialogKeyboardVisible = false;
        _dialogQtyController = null;
        _dialogQtyFocusNode = null;
        _dialogOnSubmit = null;
      });
    });
  }

  // 显示相同库位相同货号的对话框
  void _showSameArticleInWarehouseDialog(String barcode) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('提示'),
          content: Text('该货号已存在于当前库位中，但条码不同。是否继续添加？'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                barcodeController.clear();
                FocusScope.of(context).requestFocus(barcodeFocusNode);
              },
              child: Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _processBarcodeScan(barcode);
              },
              child: Text('继续添加'),
            ),
          ],
        );
      },
    );
  }

  // 处理条码扫描（根据是否手动模式决定是否弹出数量输入框）
  void _processBarcodeScan(String barcode) async {
    bool isBarcode = await _isInputBarcode(barcode);
    if (isManualModeEnabled) {
      _promptQuantityInput(barcode, isBarcode: isBarcode);
    } else {
      _uploadStocktakingData(barcode, 1, updateMode: 'add', isBarcode: isBarcode); // 默认数量1，使用叠加模式
    }
  }

  // 处理不同库位相同条码的情况（不检查货号）
  void _processDifferentLocationScan(String barcode) async {
    bool isBarcode = await _isInputBarcode(barcode);
    if (isManualModeEnabled) {
      _promptQuantityInputForNewRecord(barcode);
    } else {
      _uploadStocktakingData(barcode, 1, updateMode: 'add', forceNewRecord: true, isBarcode: isBarcode);
    }
  }
  
  // 显示相同条码不同库位的对话框
  void _showBarcodeInOtherWarehouseDialog(String barcode, String existingWarehouse, String articleId, List<String> allWarehouses) {
    // 验证库位格式
    String displayWarehouse = "其他库位";
    
    // 处理多个库位的情况
    if (allWarehouses.isNotEmpty) {
      // 最多显示3个库位，避免文本过长
      if (allWarehouses.length <= 3) {
        displayWarehouse = allWarehouses.join(", ");
      } else {
        displayWarehouse = "${allWarehouses.take(3).join(", ")}等${allWarehouses.length}个库位";
      }
    } else if (existingWarehouse.isNotEmpty && existingWarehouse != barcode) {
      displayWarehouse = existingWarehouse;
    }
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('提示'),
          content: Text('该条码已经存在于库位 $displayWarehouse 中。\n对应的货号: $articleId'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                barcodeController.clear();
                FocusScope.of(context).requestFocus(barcodeFocusNode);
              },
              child: Text('不记入'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // 确保使用add模式，在新库位中添加新记录
                _processDifferentLocationScan(barcode);
              },
              child: Text('记入数据'),
            ),
          ],
        );
      },
    );
  }

  // 专门用于添加新记录的数量输入对话框
  void _promptQuantityInputForNewRecord(String barcode) {
    TextEditingController quantityController = TextEditingController();
    // 输入框默认为空
    quantityController.text = '';

    // 添加更新模式选择
    bool isSetMode = false; // 默认为叠加模式

    // 打开弹窗时切换到对话框自定义键盘
    setState(() {
      _suspendScanning = true;
      _dialogQtyController = quantityController;
      _dialogQtyFocusNode = FocusNode();
      _dialogOnSubmit = null;
      _isWarehouseKeyboardVisible = false;
      _isBarcodeKeyboardVisible = false;
      _isSearchKeyboardVisible = false;
      _isDialogKeyboardVisible = true;
    });
    // 在弹窗之上显示覆盖层键盘
    _showDialogKeyboardOverlay(controller: quantityController, focusNode: _dialogQtyFocusNode!);

    // 保存当前上下文
    final BuildContext currentContext = context;
    
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, dialogSetState) {
            final bottomInset = _isDialogKeyboardVisible ? _customKeyboardHeight : 0.0;
            return Padding(
              padding: EdgeInsets.only(bottom: bottomInset),
              child: GlassDialog(
              title: barcode,
              titlePadding: EdgeInsets.fromLTRB(16, 16, 16, 0),
              contentPadding: EdgeInsets.all(16),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 40,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () {
                              dialogSetState(() {
                                isSetMode = false;
                                // 不再自动设置默认值
                                // quantityController.text = '1';
                              });
                            },
                            child: Container(
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: !isSetMode ? Colors.blue.withOpacity(0.1) : null,
                                borderRadius: BorderRadius.horizontal(left: Radius.circular(3)),
                              ),
                              child: Text('叠加',
                                style: TextStyle(
                                  fontWeight: !isSetMode ? FontWeight.bold : FontWeight.normal,
                                  color: !isSetMode ? Colors.blue : Colors.black,
                                ),
                              ),
                            ),
                          ),
                        ),
                        Container(width: 1, color: Colors.grey.shade300),
                        Expanded(
                          child: InkWell(
                            onTap: () {
                              setState(() {
                                isSetMode = true;
                                // 不再自动设置默认值
                              });
                            },
                            child: Container(
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: isSetMode ? Colors.blue.withOpacity(0.1) : null,
                                borderRadius: BorderRadius.horizontal(right: Radius.circular(3)),
                              ),
                              child: Text('替换',
                                style: TextStyle(
                                  fontWeight: isSetMode ? FontWeight.bold : FontWeight.normal,
                                  color: isSetMode ? Colors.blue : Colors.black,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 12),
                  TextField(
                    controller: quantityController,
                    focusNode: _dialogQtyFocusNode,
                    readOnly: false,
                    showCursor: true,
                    enableInteractiveSelection: false,
                    keyboardType: TextInputType.none,
                    decoration: InputDecoration(
                      labelText: '数量',
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(4.0)),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                      hintText: '请输入数量',
                    ),
                    autofocus: true,
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    barcodeController.clear();
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        FocusScope.of(currentContext).requestFocus(barcodeFocusNode);
                      }
                    });
                  },
                  child: Text('取消'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    String quantityStr = quantityController.text.trim();
                    int quantity = 1;

                    try {
                      quantity = int.parse(quantityStr);
                      if (quantity <= 0) throw FormatException('数量必须大于0');
                    } catch (e) {
                      if (mounted) {
                        _showSnackBar('请输入有效的数量');
                      }
                      return;
                    }

                    Navigator.pop(context);
                    _uploadStocktakingData(barcode, quantity, updateMode: isSetMode ? 'set' : 'add', forceNewRecord: true);
                  },
                  child: Text('确定'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ),
              ],
            ),
            );
          },
        );
      },
    ).whenComplete(() {
      // 关闭弹窗时关闭对话框键盘（覆盖层）
      _removeDialogKeyboardOverlay();
      setState(() {
        _suspendScanning = false;
        _isDialogKeyboardVisible = false;
        _dialogQtyController = null;
        _dialogQtyFocusNode = null;
        _dialogOnSubmit = null;
      });
    });
  }

  // 显示不同库位相同货号的对话框
  void _showArticleInOtherWarehouseDialog(String barcode, String existingWarehouse) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('提示'),
          content: Text('该货号已存在于库位 $existingWarehouse 中。是否继续添加？'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                barcodeController.clear();
                FocusScope.of(context).requestFocus(barcodeFocusNode);
              },
              child: Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _processBarcodeScan(barcode);
              },
              child: Text('继续添加'),
            ),
          ],
        );
      },
    );
  }

  // 提示用户输入数量
  void _promptQuantityInput(String input, {bool isBarcode = true}) {
    TextEditingController quantityController = TextEditingController();
    // 输入框默认为空
    quantityController.text = '';

    // 添加更新模式选择
    bool isSetMode = false; // 默认为叠加模式

    // 打开弹窗时切换到对话框自定义键盘
    setState(() {
      _suspendScanning = true;
      _dialogQtyController = quantityController;
      _dialogQtyFocusNode = FocusNode();
      // 设置回车提交逻辑
      _dialogOnSubmit = (String value) {
        // 执行确定按钮的逻辑
        _handleQuantitySubmit(quantityController, input, isBarcode, isSetMode);
      };
      _isWarehouseKeyboardVisible = false;
      _isBarcodeKeyboardVisible = false;
      _isSearchKeyboardVisible = false;
      _isDialogKeyboardVisible = true;
    });
    // 在弹窗之上显示覆盖层键盘
    _showDialogKeyboardOverlay(controller: quantityController, focusNode: _dialogQtyFocusNode!);

    // 保存当前上下文
    final BuildContext currentContext = context;
    
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, dialogSetState) {
            final bottomInset = _isDialogKeyboardVisible ? _customKeyboardHeight : 0.0;
            return Padding(
              padding: EdgeInsets.only(bottom: bottomInset),
              child: GlassDialog(
              title: '${isBarcode ? "条码" : "货号"}: $input',
              titlePadding: EdgeInsets.fromLTRB(16, 16, 16, 0),
              contentPadding: EdgeInsets.all(16),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 40,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () {
                              dialogSetState(() {
                                isSetMode = false;
                                // 不再自动设置默认值
                                // quantityController.text = '1';
                              });
                            },
                            child: Container(
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: !isSetMode ? Colors.blue.withOpacity(0.1) : null,
                                borderRadius: BorderRadius.horizontal(left: Radius.circular(3)),
                              ),
                              child: Text('叠加',
                                style: TextStyle(
                                  fontWeight: !isSetMode ? FontWeight.bold : FontWeight.normal,
                                  color: !isSetMode ? Colors.blue : Colors.black,
                                ),
                              ),
                            ),
                          ),
                        ),
                        Container(width: 1, color: Colors.grey.shade300),
                        Expanded(
                          child: InkWell(
                            onTap: () {
                              setState(() {
                                isSetMode = true;
                                // 不再自动设置默认值
                              });
                            },
                            child: Container(
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: isSetMode ? Colors.blue.withOpacity(0.1) : null,
                                borderRadius: BorderRadius.horizontal(right: Radius.circular(3)),
                              ),
                              child: Text('替换',
                                style: TextStyle(
                                  fontWeight: isSetMode ? FontWeight.bold : FontWeight.normal,
                                  color: isSetMode ? Colors.blue : Colors.black,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 12),
                  TextField(
                    controller: quantityController,
                    focusNode: _dialogQtyFocusNode,
                    readOnly: false,
                    showCursor: true,
                    enableInteractiveSelection: false,
                    keyboardType: TextInputType.none,
                    decoration: InputDecoration(
                      labelText: '数量',
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(4.0)),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                      hintText: '请输入数量',
                    ),
                    autofocus: true,
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    barcodeController.clear();
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        FocusScope.of(currentContext).requestFocus(barcodeFocusNode);
                      }
                    });
                  },
                  child: Text('取消'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    _handleQuantitySubmit(quantityController, input, isBarcode, isSetMode);
                  },
                  child: Text('确定'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ),
              ],
            ),
            );
          }
        );
      },
    ).whenComplete(() {
      // 关闭弹窗时关闭对话框键盘（覆盖层）
      _removeDialogKeyboardOverlay();
      setState(() {
        _suspendScanning = false;
        _isDialogKeyboardVisible = false;
        _dialogQtyController = null;
        _dialogQtyFocusNode = null;
        _dialogOnSubmit = null;
      });
    });
  }

  // 上传库存盘点数据
  Future<void> _uploadStocktakingData(String input, int quantity, {String updateMode = 'add', bool forceNewRecord = false, bool isBarcode = true}) async {
    String barcode = '';
    String articleId = '';
    
    if (isBarcode) {
      // 输入是条形码
      barcode = input;
      // 通过条形码获取货号（含互换策略）
      try {
        if (isOnline) {
          final response = await ScanApiService.getArticleIdByBarcode(input);
          articleId = response['articleId'] ?? '';
          if ((articleId.isEmpty || articleId == 'NO') && input.length < 10) {
            // 如果条码查询失败且长度像货号，尝试互换当作货号查本地
            final localRowSwap = await localdb.DatabaseService.getArticuloByArticuloId(input);
            if (localRowSwap != null) {
              articleId = input;
              barcode = (localRowSwap['CodigoBarra']?.toString() ?? 'NO');
            }
          }
        } else {
          // 离线优先本地：条码->货号；失败则尝试互换
          final localRow = await localdb.DatabaseService.getArticuloByBarcode(input);
          if (localRow != null) {
            articleId = (localRow['ArticuloID']?.toString() ?? '');
          }
          if (articleId.isEmpty) {
            // 将条码当作货号尝试
            final localRowSwap = await localdb.DatabaseService.getArticuloByArticuloId(input);
            if (localRowSwap != null) {
              articleId = input;
              barcode = (localRowSwap['CodigoBarra']?.toString() ?? 'NO');
            }
          }
        }
      } catch (e) {
        print('通过条形码获取货号失败: $e');
      }
    } else {
      // 输入是货号
      articleId = input;
      // 通过货号查找条形码（含互换策略）
      try {
        if (isOnline) {
          final response = await ScanApiService.getBarcodeByArticleId(input);
          barcode = response['barcode'] ?? 'NO';
        } else {
          final localRow = await localdb.DatabaseService.getArticuloByArticuloId(input);
          barcode = (localRow != null ? (localRow['CodigoBarra']?.toString() ?? 'NO') : 'NO');
          if (barcode.isEmpty || barcode == 'NO') {
            // 若货号查不到，尝试互换条码->货号（不限制长度）
            final swapRow = await localdb.DatabaseService.getArticuloByBarcode(input);
            if (swapRow != null) {
              articleId = (swapRow['ArticuloID']?.toString() ?? articleId);
              barcode = (swapRow['CodigoBarra']?.toString() ?? 'NO');
            }
          }
        }
      } catch (e) {
        print('通过货号获取条形码失败: $e');
        barcode = 'NO';
      }
      
      // 如果找不到条形码，设置为NO
      if (barcode.isEmpty) {
        barcode = 'NO';
      }
    }
    
    if (!isOnline) {
      // 离线：写本地 stocktaking（服务器字段名）
      try {
        // 重复库位检查，拼装附加信息
        List<String> duplicateWarehouses = [];
        for (var result in scanResults) {
          String resultBarcode = result['barcode']?.toString() ?? '';
          String resultArticleId = result['articleId']?.toString() ?? '';
          
          // 根据输入类型进行比较
          bool isDuplicate = false;
          if (isBarcode) {
            isDuplicate = (resultBarcode == input && result['warehouse'] != lastScannedWarehouse);
          } else {
            isDuplicate = (resultArticleId == input && result['warehouse'] != lastScannedWarehouse);
          }
          
        	if (isDuplicate) {
            String warehouse = result['warehouse']?.toString() ?? '';
            if (warehouse.isNotEmpty && warehouse != input) {
              duplicateWarehouses.add(warehouse);
            }
          }
        }

        String? oldStock;
        if (duplicateWarehouses.isNotEmpty) {
          oldStock = 'DUP:${duplicateWarehouses.join('|')}';
        }

        await localdb.DatabaseService.upsertStocktakingRecord(
          username: username,
          warehouse: lastScannedWarehouse,
          barcode: barcode,
          quantity: quantity,
          updateMode: updateMode,
          forceNewRecord: forceNewRecord,
          articleId: articleId,
          isUnknown: (articleId.isEmpty || articleId == 'NO' || barcode.isEmpty || barcode == 'NO'),
          oldStock: oldStock,
          inUse: inUseStatus,
        );

        _audioPlayer.play(AssetSource('scan.mp3'));
        String modeText = updateMode == 'set' ? '设置为' : '添加了';
        String locationText = forceNewRecord ? '（新库位）' : '';
        String duplicateText = duplicateWarehouses.isNotEmpty ? '（已在其他库位存在）' : '';
        String inputTypeText = isBarcode ? '条码' : '货号';
        String displayText = isBarcode ? input : '$input (条码: ${barcode.isEmpty ? 'NO' : barcode})';
        _showSnackBar('[离线] 成功${modeText}${inputTypeText}：$displayText，数量：$quantity $locationText$duplicateText');

        await fetchOnlineData();
        barcodeController.clear();
        FocusScope.of(context).requestFocus(barcodeFocusNode);
      } catch (e) {
        _showSnackBar('本地保存失败: $e', backgroundColor: Colors.red);
      }
      return;
    }

    try {
      // 在线模式处理
      final Map<String, dynamic> extraParams = {};

      // 检查是否存在于其他库位
      List<String> duplicateWarehouses = [];
      for (var result in scanResults) {
        String resultBarcode = result['barcode']?.toString() ?? '';
        String resultArticleId = result['articleId']?.toString() ?? '';
        
        // 根据输入类型进行比较
        bool isDuplicate = false;
        if (isBarcode) {
          isDuplicate = (resultBarcode == input && result['warehouse'] != lastScannedWarehouse);
        } else {
          isDuplicate = (resultArticleId == input && result['warehouse'] != lastScannedWarehouse);
        }
        
        if (isDuplicate) {
          String warehouse = result['warehouse']?.toString() ?? '';
          if (warehouse.isNotEmpty && warehouse != input) {
            duplicateWarehouses.add(warehouse);
          }
        }
      }

      if (duplicateWarehouses.isNotEmpty) {
        extraParams['oldstock'] = 'DUP:${duplicateWarehouses.join('|')}';
      }

      bool isUnknownProduct = (articleId.isEmpty && (barcode.isEmpty || barcode == 'NO'));

      final now = DateTime.now();
      String two(int v) => v.toString().padLeft(2, '0');
      final stdata = '${now.year}-${two(now.month)}-${two(now.day)} ${two(now.hour)}:${two(now.minute)}:${two(now.second)}';

      final payload = {
        'Weizhi': lastScannedWarehouse,
        'CodigoBarra': barcode,
        'ArticuloID': isUnknownProduct ? 'UNKNOWN' : articleId,
        'Stock': quantity.toString(),
        'user': username,
        'oldstock': extraParams['oldstock'],
        'isUnknown': isUnknownProduct ? 1 : 0,
        'stdata': stdata,
        'inUse': inUseStatus,
        'updateMode': updateMode,
        'forceNewRecord': forceNewRecord ? 1 : 0,
        // 兼容旧字段名
        'warehouse': lastScannedWarehouse,
        'barcode': barcode,
        'quantity': quantity.toString(),
        'oldStock': extraParams['oldstock'],
      };

      final baseUrl = await ScanApiService.getBaseUrl();
      final url = Uri.parse('$baseUrl/stocktaking');
      final response = await http
          .post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(payload),
      )
          .timeout(ScanApiService.timeoutDuration);

      if (response.statusCode == 200) {
        if (isUnknownProduct) {
          _audioPlayer.play(AssetSource('err.mp3'));
          String inputTypeText = isBarcode ? '条码' : '货号';
          String displayText = isBarcode ? input : '$input (条码: $barcode)';
          _showSnackBar(
            '未知商品已记录（${inputTypeText}）：$displayText，数量：$quantity',
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 2)
          );
        } else {
          _audioPlayer.play(AssetSource('scan.mp3'));
          String modeText = updateMode == 'set' ? '设置为' : '添加了';
          String locationText = forceNewRecord ? '（新库位）' : '';
          String duplicateText = duplicateWarehouses.isNotEmpty ? '（已在其他库位存在）' : '';
          String inputTypeText = isBarcode ? '条码' : '货号';
          String displayText = isBarcode ? input : '$input (条码: ${barcode.isEmpty ? 'NO' : barcode})';
          _showSnackBar('成功${modeText}${inputTypeText}：$displayText，数量：$quantity $locationText$duplicateText');
        }

        await fetchOnlineData();
        barcodeController.clear();
        FocusScope.of(context).requestFocus(barcodeFocusNode);
      } else {
        throw Exception('API响应错误: ${response.statusCode}');
      }
    } catch (e) {
      // 回退：保存到本地待同步队列
      try {
        await localdb.DatabaseService.upsertStocktakingRecord(
          username: username,
          warehouse: lastScannedWarehouse,
          barcode: barcode,
          quantity: quantity,
          updateMode: updateMode,
          forceNewRecord: forceNewRecord,
          articleId: articleId,
          isUnknown: true,
          oldStock: null,
          inUse: inUseStatus,
        );
        if (mounted) {
          _showSnackBar('网络不稳定，已暂存本地待同步', backgroundColor: Colors.orange);
        }
      } catch (_) {}
      if (mounted) _showSnackBar('上传数据失败: $e', backgroundColor: Colors.red);
      barcodeController.clear();
      FocusScope.of(context).requestFocus(barcodeFocusNode);
    }
  }

  // 一键同步本地离线记录
  Future<void> _syncOfflineRecords() async {
    if (!isOnline) {
      _showSnackBar('当前离线，无法同步', backgroundColor: Colors.orange);
      return;
    }
    try {
      final unsynced = await localdb.DatabaseService.getUnsyncedStocktakingForUser(username);
      if (unsynced.isEmpty) {
        _showSnackBar('没有未同步记录');
        return;
      }

      final controller = StreamController<int>();
      int total = unsynced.length;
      int done = 0;
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) => SyncProgressDialog(total: total, syncedCountStream: controller.stream),
      );

      for (final row in unsynced) {
        try {
          // 确保 stdata 存在
          String? stdata = row['stdata']?.toString();
          if (stdata == null || stdata.isEmpty) {
            final now = DateTime.now();
            String two(int v) => v.toString().padLeft(2, '0');
            stdata = '${now.year}-${two(now.month)}-${two(now.day)} ${two(now.hour)}:${two(now.minute)}:${two(now.second)}';
          }
          await ScanApiService.uploadSingleStocktaking({
            'Weizhi': row['Weizhi'],
            'CodigoBarra': row['CodigoBarra'],
            'ArticuloID': row['ArticuloID'],
            'Stock': row['Stock'],
            'user': row['user'],
            'oldstock': row['oldstock'],
            'isUnknown': row['isUnknown'],
            'stdata': stdata,
            'inUse': inUseStatus,
            'updateMode': 'add',
          });
          await localdb.DatabaseService.markStocktakingSynced(row['id'] as int);
          done++;
          controller.add(done);
        } catch (e) {
          print('同步失败 id=${row['id']}: $e');
        }
      }

      controller.close();
      Navigator.of(context).pop();
      _showSnackBar('同步完成：$done/$total');
      if (done > 0) {
        _audioPlayer.play(AssetSource('true.mp3'));
      }
      await fetchOnlineData();
    } catch (e) {
      _showSnackBar('同步失败: $e', backgroundColor: Colors.red);
    }
  }

  // 网络恢复时自动同步离线记录（带进度提示）
  Future<void> _autoSyncOfflineRecordsSilent() async {
    if (!isOnline) return;
    
    try {
      final unsynced = await localdb.DatabaseService.getUnsyncedStocktakingForUser(username);
      if (unsynced.isEmpty) return; // 没有未同步记录，直接返回

      // 显示同步确认对话框
      bool? shouldSync = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Row(
              children: [
                Icon(Icons.cloud_upload, color: Colors.blue, size: 28),
                SizedBox(width: 12),
                Text('检测到网络连接', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('发现 ${unsynced.length} 条本地数据需要上传到服务器'),
                SizedBox(height: 8),
                Text('是否立即开始同步？', style: TextStyle(color: Colors.grey[600])),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text('稍后同步', style: TextStyle(color: Colors.grey)),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text('立即同步', style: TextStyle(color: Colors.white)),
              ),
            ],
          );
        },
      );

      if (shouldSync != true) return;

      // 创建进度流控制器
      final controller = StreamController<int>();
      int total = unsynced.length;
      int syncedCount = 0;

      // 显示进度对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) => _buildSyncProgressDialog(total, controller.stream),
      );

      // 执行同步
      for (final row in unsynced) {
        try {
          // 确保 stdata 存在
          String? stdata = row['stdata']?.toString();
          if (stdata == null || stdata.isEmpty) {
            final now = DateTime.now();
            String two(int v) => v.toString().padLeft(2, '0');
            stdata = '${now.year}-${two(now.month)}-${two(now.day)} ${two(now.hour)}:${two(now.minute)}:${two(now.second)}';
          }
          
          await ScanApiService.uploadSingleStocktaking({
            'Weizhi': row['Weizhi'],
            'CodigoBarra': row['CodigoBarra'],
            'ArticuloID': row['ArticuloID'],
            'Stock': row['Stock'],
            'user': row['user'],
            'oldstock': row['oldstock'],
            'isUnknown': row['isUnknown'],
            'stdata': stdata,
            'inUse': inUseStatus,
            'updateMode': 'add',
          });
          
          await localdb.DatabaseService.markStocktakingSynced(row['id'] as int);
          syncedCount++;
          controller.add(syncedCount);
        } catch (e) {
          print('同步失败 id=${row['id']}: $e');
        }
      }
      
      controller.close();
      Navigator.of(context).pop(); // 关闭进度对话框
      
      // 显示完成对话框，询问是否删除本地数据
      await _showSyncCompletionDialog(syncedCount, total);
      
    } catch (e) {
      print('自动同步失败: $e');
      _showSnackBar('同步失败: $e', backgroundColor: Colors.red);
    }
  }

  // 构建同步进度对话框
  Widget _buildSyncProgressDialog(int total, Stream<int> syncedCountStream) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          SizedBox(width: 12),
          Text('正在上传数据', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
        ],
      ),
      content: StreamBuilder<int>(
        stream: syncedCountStream,
        initialData: 0,
        builder: (context, snapshot) {
          final synced = snapshot.data ?? 0;
          final progress = total > 0 ? synced / total : 0.0;
          
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              LinearProgressIndicator(
                value: progress,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
              ),
              SizedBox(height: 16),
              Text(
                '进度: $synced / $total',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
              SizedBox(height: 8),
              Text(
                '${(progress * 100).toStringAsFixed(1)}%',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ],
          );
        },
      ),
    );
  }

  // 显示同步完成对话框
  Future<void> _showSyncCompletionDialog(int syncedCount, int total) async {
    final bool? shouldDeleteLocal = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 28),
              SizedBox(width: 12),
              Text('同步完成', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('成功上传 $syncedCount / $total 条数据到服务器'),
              SizedBox(height: 16),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '是否删除本地数据？',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                    SizedBox(height: 4),
                    Text(
                      '删除：释放存储空间，数据已安全保存到服务器\n保留：本地备份，可离线查看',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('保留本地数据', style: TextStyle(color: Colors.grey[700])),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[400],
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text('删除本地数据', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );

    if (shouldDeleteLocal == true) {
      try {
        // 删除已同步的本地数据
        await localdb.DatabaseService.deleteSyncedStocktakingForUser(username);
        _showSnackBar('本地数据已清理', backgroundColor: Colors.green);
      } catch (e) {
        _showSnackBar('清理本地数据失败: $e', backgroundColor: Colors.red);
      }
    }
    
    // 刷新数据显示
    await fetchOnlineData();
    _audioPlayer.play(AssetSource('true.mp3'));
  }

  void refreshPage() {
    fetchOnlineData();
  }

  // ===== 搜索模式逻辑 =====
  void _enterSearchMode() {
    setState(() {
      _isSearchMode = true;
      _searchQuery = '';
      _searchController.clear();
      _filteredResults = scanResults;
      // 若已有任一键盘显示，则切换到搜索键盘
      if (_isWarehouseKeyboardVisible || _isBarcodeKeyboardVisible || _isSearchKeyboardVisible || _isDialogKeyboardVisible) {
        _isWarehouseKeyboardVisible = false;
        _isBarcodeKeyboardVisible = false;
        _isSearchKeyboardVisible = true;
        _isDialogKeyboardVisible = false;
      }
    });
    // 聚焦搜索框
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) FocusScope.of(context).requestFocus(_searchFocusNode);
    });
  }

  void _exitSearchMode() {
    setState(() {
      _isSearchMode = false;
      _searchQuery = '';
      _searchController.clear();
      _filteredResults = [];
      _isSearchKeyboardVisible = false;
      _isDialogKeyboardVisible = false;
    });
    // 恢复条码输入焦点
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) FocusScope.of(context).requestFocus(barcodeFocusNode);
    });
  }

  void _applySearch(String query) {
    final q = query.trim();
    setState(() {
      _searchQuery = q;
      if (q.isEmpty) {
        _filteredResults = scanResults;
      } else {
        final lower = q.toLowerCase();
        _filteredResults = scanResults.where((row) {
          final w = (row['warehouse'] ?? '').toString().toLowerCase();
          final b = (row['barcode'] ?? '').toString().toLowerCase();
          final a = (row['articleId'] ?? '').toString().toLowerCase();
          // 模糊匹配：包含或部分匹配
          return w.contains(lower) || b.contains(lower) || a.contains(lower);
        }).toList();
      }
    });
  }
  // =====================

  Future<void> synchronizeLocationData() async {
    if (!isOnline) {
      _showSnackBar('当前离线，无法同步', backgroundColor: Colors.orange);
      return;
    }

    // 显示确认对话框
    bool? confirmed = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return GlassDialog(
          title: '确认同步库位',
          content: Text(
            '此操作将把盘点记录中的库位信息同步到商品主数据中。\n\n这会更新商品的存储位置信息，确定要继续吗？',
            style: TextStyle(color: Colors.black87),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              child: Text('确认同步'),
            ),
          ],
        );
      },
    );

    if (confirmed != true) {
      return;
    }

    // 显示同步进度对话框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return GlassProgressDialog(
          title: '同步库位',
          message: '正在同步库位数据...',
        );
      },
    );

    try {
      // 调用服务器端的库位同步API
      final baseUrl = await ScanApiService.getBaseUrl();
      final url = Uri.parse('$baseUrl/sync_locations');

      print('开始调用库位同步API...');

      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
      ).timeout(Duration(seconds: 60)); // 增加超时时间，因为同步可能需要较长时间

      Navigator.of(context).pop(); // 关闭进度对话框

      print('库位同步API响应: ${response.statusCode} - ${response.body}');

      if (response.statusCode == 200) {
        try {
          final responseData = jsonDecode(response.body);
          print('库位同步API完整响应: $responseData');

          if (responseData['success'] == true) {
            final stats = responseData['statistics'];
            final updateResults = responseData['updateResults'] as List?;
            print('统计信息: $stats');
            print('更新结果: $updateResults');

            String resultMessage = '库位同步完成！\n\n';
            resultMessage += '总记录数: ${stats['totalRecords'] ?? stats['total']}\n';
            resultMessage += '有效记录: ${stats['validRecords'] ?? stats['total']}\n';
            resultMessage += '成功更新: ${stats['updated']}\n';

            // 显示跳过的记录详情
            int skippedNotFound = stats['skippedNotFound'] ?? stats['skipped'] ?? 0;
            int skippedNoRecords = stats['skippedNoRecords'] ?? 0;

            if (skippedNoRecords > 0) {
              resultMessage += '跳过NO记录: $skippedNoRecords 条\n';
            }
            if (skippedNotFound > 0) {
              resultMessage += '跳过不存在商品: $skippedNotFound 条\n';
            }
            if (stats['errors'] > 0) {
              resultMessage += '错误记录: ${stats['errors']}\n';
            }

            // 更新示例不再在这里显示，已移到弹窗中

            // 显示同步结果弹窗
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) {
                return GlassDialog(
                  title: '同步完成',
                  titleWidget: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: stats['updated'] > 0 ? Colors.green[600] : Colors.blue[600],
                        size: 24,
                      ),
                      SizedBox(width: 8),
                      Text(
                        '库位同步完成',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                          fontSize: 18,
                        ),
                      ),
                    ],
                  ),
                  content: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 统计信息
                        Container(
                          padding: EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.6),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.white.withOpacity(0.4)),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '同步统计',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black87,
                                  fontSize: 16,
                                ),
                              ),
                              SizedBox(height: 12),
                              _buildStatRow('总记录数', '${stats['totalRecords'] ?? stats['total']}', Icons.inventory),
                              _buildStatRow('有效记录', '${stats['validRecords'] ?? stats['total']}', Icons.verified),
                              _buildStatRow('成功更新', '${stats['updated']}', Icons.check_circle, Colors.green[600]),
                              if (skippedNoRecords > 0)
                                _buildStatRow('跳过NO记录', '$skippedNoRecords', Icons.skip_next, Colors.orange[600]),
                              if (skippedNotFound > 0)
                                _buildStatRow('跳过不存在商品', '$skippedNotFound', Icons.error_outline, Colors.orange[600]),
                              if (stats['errors'] > 0)
                                _buildStatRow('错误记录', '${stats['errors']}', Icons.error, Colors.red[600]),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      child: Text('确定'),
                    ),
                  ],
                );
              },
            );

            if (stats['updated'] > 0) {
              _audioPlayer.play(AssetSource('true.mp3'));
            }

          } else {
            _showSnackBar(
              '库位同步失败: ${responseData['message']}',
              backgroundColor: Colors.red,
            );
          }
        } catch (e) {
          _showSnackBar(
            '解析同步结果失败: $e',
            backgroundColor: Colors.red,
          );
        }
      } else {
        _showSnackBar(
          '库位同步失败: HTTP ${response.statusCode}',
          backgroundColor: Colors.red,
        );
      }
    } catch (e) {
      Navigator.of(context).pop(); // 确保关闭进度对话框
      _showSnackBar(
        '库位同步失败: $e',
        backgroundColor: Colors.red,
      );
    }
  }

  // 保留原来的验证功能，重命名为验证库位数据
  Future<void> _validateLocationData() async {
    if (!isOnline) {
      _showSnackBar('当前离线，无法验证', backgroundColor: Colors.orange);
      return;
    }
    try {
      // 获取未同步的数据
      var unsyncedData = scanResults.where((item) => !(item['isSynced'] ?? false)).toList();

      if (unsyncedData.isEmpty) {
        _showSnackBar('所有库位都已验证');
        return;
      }

      // 创建新的 StreamController
      final syncedCountStreamController = StreamController<int>();

      int total = unsyncedData.length;
      int syncedCount = 0;

      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return SyncProgressDialog(
            total: total,
            syncedCountStream: syncedCountStreamController.stream,
          );
        },
      );

      int failedCount = 0;
      List<String> failedItems = [];

      int skippedCount = 0;
      List<String> skippedItems = [];

      for (var item in unsyncedData) {
        try {
          // 获取货号或条码
          String articleId = item['articleId']?.toString() ?? '';
          String barcode = item['barcode']?.toString() ?? '';
          String identifier = articleId.isNotEmpty ? articleId : barcode;

          if (identifier.isEmpty) {
            print('跳过空的货号/条码记录: ${item['id']}');
            skippedCount++;
            skippedItems.add('记录ID: ${item['id']} (缺少货号/条码)');
            continue;
          }

          // 跳过货号和条码为"NO"的商品
          if (articleId.toUpperCase() == 'NO' && barcode.toUpperCase() == 'NO') {
            print('跳过货号和条码都为NO的记录: ${item['id']}');
            skippedCount++;
            skippedItems.add('记录ID: ${item['id']} (货号和条码都为NO)');
            continue;
          }

          // 如果主要标识符为"NO"，跳过同步
          if (identifier.toUpperCase() == 'NO') {
            print('跳过标识符为NO的记录: ${item['id']} (标识符: $identifier)');
            skippedCount++;
            skippedItems.add('$identifier (标识符为NO，无需同步)');
            continue;
          }

          // 验证货号是否存在于系统中
          String queryType = identifier.length > 10 ? 'CodigoBarra' : 'ArticuloID';
          final results = await ScanApiService.bigstockqueryDatabase(identifier, queryType);

          if (results.isNotEmpty) {
            // 货号存在，标记为同步成功
            setState(() {
              item['isSynced'] = true;
              synchronizedIds.add(item['id']);
            });
            syncedCount++;
            print('同步成功: $identifier');
          } else {
            // 货号不存在，记录失败
            failedCount++;
            failedItems.add('$identifier (货号不存在)');
            print('同步失败: $identifier - 货号不存在');
          }
        } catch (e) {
          // 网络或其他错误
          String identifier = item['articleId']?.toString() ?? item['barcode']?.toString() ?? '未知';
          failedCount++;
          failedItems.add('$identifier (网络错误: $e)');
          print('同步失败: $identifier - 错误: $e');
        }

        // 更新进度
        syncedCountStreamController.add(syncedCount + failedCount + skippedCount);
        await Future.delayed(Duration(milliseconds: 300)); // 避免请求过快
      }

      // 同步完成后关闭 StreamController
      syncedCountStreamController.close();

      Navigator.of(context).pop(); // 关闭进度对话框

      // 显示详细的同步结果
      String resultMessage = '同步完成！\n';
      resultMessage += '成功: $syncedCount 条\n';

      if (skippedCount > 0) {
        resultMessage += '跳过: $skippedCount 条 (货号/条码为NO)\n';
      }

      if (failedCount > 0) {
        resultMessage += '失败: $failedCount 条\n';
        if (failedItems.length <= 2) {
          resultMessage += '失败项目: ${failedItems.join(', ')}';
        } else {
          resultMessage += '失败项目: ${failedItems.take(2).join(', ')}... (共$failedCount项)';
        }
      }

      // 根据结果选择背景色
      Color backgroundColor = Colors.green;
      if (failedCount > 0 && syncedCount == 0) {
        backgroundColor = Colors.red; // 全部失败
      } else if (failedCount > 0) {
        backgroundColor = Colors.orange; // 部分失败
      } else if (syncedCount == 0 && skippedCount > 0) {
        backgroundColor = Colors.blue; // 全部跳过
      }

      _showSnackBar(
        resultMessage,
        backgroundColor: backgroundColor,
      );

      if (syncedCount > 0) {
        _audioPlayer.play(AssetSource('true.mp3'));
      }
    } catch (e) {
      Navigator.of(context).pop(); // 发生错误时关闭进度对话框
      _showSnackBar('验证库位数据失败: $e', backgroundColor: Colors.red);
    }
  }

  // 简化删除记录确认对话框
  void confirmDeleteRecord(int id, String barcode, String warehouse, bool isSynced) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return GlassDialog(
          title: '确认删除',
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '确定要删除此条盘点记录吗？',
                style: TextStyle(color: Colors.black87),
              ),
              SizedBox(height: 16),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.6),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.white.withOpacity(0.4)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.qr_code, size: 16, color: Colors.blue[700]),
                        SizedBox(width: 8),
                        Text('条码: ', style: TextStyle(fontSize: 14, color: Colors.grey[700])),
                        Expanded(
                          child: Text(
                            barcode,
                            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14, color: Colors.black87),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(Icons.location_on, size: 16, color: Colors.green[700]),
                        SizedBox(width: 8),
                        Text('库位: ', style: TextStyle(fontSize: 14, color: Colors.grey[700])),
                        Expanded(
                          child: Text(
                            warehouse,
                            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14, color: Colors.black87),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              SizedBox(height: 12),
              Text(
                '⚠️ 删除后无法恢复，请确认操作',
                style: TextStyle(fontSize: 13, color: Colors.orange[700]),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                deleteUserRecord(id);
              },
              style: TextButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              child: Text('确认删除', style: TextStyle(color: Colors.red[700], fontWeight: FontWeight.bold)),
            ),
          ],
        );
      },
    );
  }

  Future<void> deleteUserRecord(int id) async {
    // 查找要删除的记录
    final recordToDelete = scanResults.firstWhere(
      (record) => record['id'] == id,
      orElse: () => <String, dynamic>{},
    );

    if (recordToDelete.isEmpty) {
      _showSnackBar('未找到要删除的记录', backgroundColor: Colors.red);
      return;
    }

    final bool isSynced = recordToDelete['isSynced'] ?? false;
    final String barcode = recordToDelete['barcode']?.toString() ?? '未知';
    final String warehouse = recordToDelete['warehouse']?.toString() ?? '未知';

    // 显示删除进度指示器
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return GlassProgressDialog(
          title: '删除记录',
          message: '正在删除记录...',
        );
      },
    );

    try {
      if (!isOnline) {
        // 离线模式：只能删除本地记录
        await localdb.DatabaseService.deleteLocalStocktakingRecord(id);
        setState(() {
          scanResults.removeWhere((e) => (e['id'] == id));
        });
        Navigator.of(context).pop(); // 关闭进度对话框
        _showSnackBar('已删除本地记录 (条码: $barcode)', backgroundColor: Colors.green);
        return;
      }

      // 在线模式：尝试删除服务器记录（无论同步状态如何）
      bool serverDeleteSuccess = false;

      // 总是尝试从服务器删除记录
      try {
        final baseUrl = await ScanApiService.getBaseUrl();
        final url = Uri.parse('$baseUrl/delete_record');
        print('尝试删除服务器记录: ID=$id, isSynced=$isSynced');

        final response = await http.post(
          url,
          body: jsonEncode({'id': id}),
          headers: {'Content-Type': 'application/json'},
        ).timeout(ScanApiService.timeoutDuration);

        print('服务器删除响应: ${response.statusCode} - ${response.body}');

        if (response.statusCode == 200) {
          try {
            final responseData = jsonDecode(response.body);
            if (responseData['success'] == true) {
              serverDeleteSuccess = true;
              print('服务器记录删除成功: ID=$id');
              if (responseData['deletedRecord'] != null) {
                final deletedRecord = responseData['deletedRecord'];
                print('删除的记录详情: 条码=${deletedRecord['barcode']}, 库位=${deletedRecord['warehouse']}');
              }
            } else {
              print('服务器删除失败: ${responseData['message']}');
              if (isSynced) {
                throw Exception('服务器删除失败: ${responseData['message']}');
              }
            }
          } catch (e) {
            // 如果响应不是JSON格式，按旧格式处理
            serverDeleteSuccess = true;
            print('服务器记录删除成功 (旧格式): ID=$id');
          }
        } else if (response.statusCode == 404) {
          print('服务器上未找到记录: ID=$id (可能是未同步记录)');
          // 404错误对于未同步记录是正常的
          if (isSynced) {
            throw Exception('服务器上未找到要删除的记录');
          }
        } else {
          print('服务器删除失败: ${response.statusCode} - ${response.body}');
          // 对于未同步记录，服务器删除失败是正常的，不要抛出异常
          if (isSynced) {
            throw Exception('服务器删除失败: ${response.body}');
          } else {
            print('未同步记录，服务器删除失败是正常的');
          }
        }
      } catch (e) {
        print('服务器删除异常: $e');
        // 对于已同步记录，服务器删除失败应该报错
        if (isSynced) {
          Navigator.of(context).pop(); // 关闭进度对话框
          _showSnackBar('删除服务器记录失败: $e', backgroundColor: Colors.red);
          return;
        } else {
          print('未同步记录，服务器删除异常是正常的');
        }
      }

      // 删除本地记录
      try {
        await localdb.DatabaseService.deleteLocalStocktakingRecord(id);
        print('本地记录删除成功: ID=$id');
      } catch (e) {
        print('删除本地记录失败: $e');
        // 即使本地删除失败，如果服务器删除成功，也要继续
      }

      // 更新UI状态
      setState(() {
        scanResults.removeWhere((e) => (e['id'] == id));
      });
      synchronizedIds.remove(id);

      Navigator.of(context).pop(); // 关闭进度对话框

      // 显示成功消息
      if (isSynced && serverDeleteSuccess) {
        _showSnackBar('已删除服务器和本地记录\n条码: $barcode, 库位: $warehouse', backgroundColor: Colors.green);
      } else {
        _showSnackBar('已删除本地记录\n条码: $barcode, 库位: $warehouse', backgroundColor: Colors.green);
      }

      // 刷新在线数据以确保同步
      if (isOnline) {
        await fetchOnlineData();
      }

    } catch (e) {
      Navigator.of(context).pop(); // 关闭进度对话框
      _showSnackBar('删除记录失败: $e', backgroundColor: Colors.red);
    }
  }

  Future<void> clearUserData() async {
    if (!isOnline) {
      try {
        await localdb.DatabaseService.clearLocalStocktakingForUser(username);
        setState(() {
          scanResults.clear();
        });
        _showSnackBar('已清除本地盘点数据');
        _audioPlayer.play(AssetSource('true.mp3'));
      } catch (e) {
        _showSnackBar('清除本地数据失败: $e', backgroundColor: Colors.red);
      }
      return;
    }
    try {
      if (username.isEmpty) {
        _showSnackBar('无法获取当前用户信息', backgroundColor: Colors.red);
        return;
      }
      
      final baseUrl = await ScanApiService.getBaseUrl();
      final url = Uri.parse('$baseUrl/clear_my_stocktaking');
      
      print('正在清除用户[$username]的数据...');
      
      // 显示加载指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 20),
                Text("正在清除数据..."),
              ],
            ),
          );
        },
      );
      
      final response = await http.post(
        url, 
        body: jsonEncode({'user': username}), 
        headers: {'Content-Type': 'application/json'}
      ).timeout(Duration(seconds: 10));  // 设置10秒超时

      // 关闭加载指示器
      Navigator.of(context).pop();

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final deletedCount = responseData['deletedCount'] ?? 0;
        _showSnackBar('清除数据成功，共删除了 $deletedCount 条记录');
        synchronizedIds.clear(); // 清空已同步的ID集合
        await fetchOnlineData(); // 使用await确保数据刷新完成
        _audioPlayer.play(AssetSource('true.mp3'));
      } else {
        _showSnackBar('清除数据失败: ${response.reasonPhrase ?? "未知错误"}', backgroundColor: Colors.red);
        print('清除数据API返回错误: ${response.statusCode}, ${response.body}');
      }
    } on TimeoutException {
      Navigator.of(context).pop(); // 确保加载指示器关闭
      _showSnackBar('清除数据请求超时，请检查网络连接', backgroundColor: Colors.orange);
    } catch (e) {
      // 确保加载指示器关闭
      try {
        Navigator.of(context).pop();
      } catch (_) {}
      _showSnackBar('清除数据失败: $e', backgroundColor: Colors.red);
      print('清除数据时发生异常: $e');
    }
  }
  
  void _confirmClearAllData() {
    // 产生随机4位数字
    final randomNumber = Random().nextInt(9000) + 1000;
    TextEditingController verifyController = TextEditingController();

    // 保存当前上下文
    final BuildContext currentContext = context;
    
    showDialog(
      context: context,
      barrierDismissible: false, // 强制用户响应对话框
      builder: (BuildContext dialogContext) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: AlertDialog(
            backgroundColor: Colors.white.withOpacity(0.8),
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
              side: BorderSide(color: Colors.white.withOpacity(0.2), width: 1),
            ),
            title: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.12),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.red.withOpacity(0.25), width: 1),
                  ),
                  child: Icon(Icons.delete_forever, color: Colors.red.shade700, size: 24),
                ),
                SizedBox(width: 14),
                Expanded(
                  child: Text(
                    '清除全部数据',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      color: Colors.grey.shade800,
                    ),
                  ),
                ),
                SizedBox(width: 8),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    color: (isOnline ? Colors.green : Colors.orange).withOpacity(0.12),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: (isOnline ? Colors.green : Colors.orange).withOpacity(0.3), width: 1),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(isOnline ? Icons.cloud_done : Icons.cloud_off, size: 14, color: isOnline ? Colors.green : Colors.orange),
                      SizedBox(width: 6),
                      Text(isOnline ? '在线' : '离线', style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600, color: isOnline ? Colors.green : Colors.orange)),
                    ],
                  ),
                ),
              ],
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 确认提示 + 输入（同一行，降低弹窗高度）
                  Container(
                    width: double.maxFinite,
                    padding: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.white.withOpacity(0.4), width: 1),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: RichText(
                            text: TextSpan(
                              text: '请输入数字确认: ',
                              style: TextStyle(color: Colors.black87, fontSize: 16),
                              children: [
                                TextSpan(
                                  text: '$randomNumber',
                                  style: TextStyle(color: Colors.red, fontSize: 18, fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(width: 8),
                        SizedBox(
                          width: 120,
                          child: TextField(
                            controller: verifyController,
                            keyboardType: TextInputType.number,
                            autofocus: true,
                            decoration: InputDecoration(
                              hintText: '输入',
                              filled: true,
                              fillColor: Colors.white.withOpacity(0.7),
                              border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
                              contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 12),
                  Container(
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.06),
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Colors.red.withOpacity(0.2), width: 1),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(Icons.warning_amber_rounded, color: Colors.red, size: 18),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            '此操作将删除您的所有盘点数据，无法恢复！',
                            style: TextStyle(color: Colors.red.shade700),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            actionsPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                },
                style: TextButton.styleFrom(
                  foregroundColor: Colors.grey.shade700,
                  backgroundColor: Colors.grey.withOpacity(0.15),
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                ),
                child: Text('取消'),
              ),
              ElevatedButton(
                onPressed: () async {
                  String input = verifyController.text.trim();
                  if (input == randomNumber.toString()) {
                    Navigator.of(dialogContext).pop();
                    await clearUserData();
                  } else {
                    ScaffoldMessenger.of(currentContext).showSnackBar(
                      SnackBar(content: Text('输入错误，请重新尝试')),
                    );
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red.shade500,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
                ),
                child: Text('确定清除'),
              ),
            ],
          ),
        );
      },
    );
  }

  // 修改键盘图标点击方法，根据当前焦点决定显示哪个键盘
  void _toggleKeyboard() {
    // 检查是否有键盘正在显示
    bool isAnyKeyboardVisible = _isWarehouseKeyboardVisible || _isBarcodeKeyboardVisible;
    
    setState(() {
      if (isAnyKeyboardVisible) {
        // 如果已有键盘显示，则隐藏所有键盘
        _isWarehouseKeyboardVisible = false;
        _isBarcodeKeyboardVisible = false;
      } else {
        // 根据当前焦点决定显示哪个键盘
        if (warehouseFocusNode.hasFocus) {
          _isWarehouseKeyboardVisible = true;
          _isBarcodeKeyboardVisible = false;
        } else if (barcodeFocusNode.hasFocus) {
          _isBarcodeKeyboardVisible = true;
          _isWarehouseKeyboardVisible = false;
        } else {
          // 如果没有焦点，默认激活条码输入框的焦点和键盘
          FocusScope.of(context).requestFocus(barcodeFocusNode);
          _isBarcodeKeyboardVisible = true;
          _isWarehouseKeyboardVisible = false;
        }
      }
    });
  }

  void _focusWarehouseInput() {
    FocusScope.of(context).requestFocus(warehouseFocusNode);
    // 点击输入框不自动显示键盘，只设置焦点
    setState(() {
      // 如果已经有键盘显示，则切换到对应的键盘
      if (_isWarehouseKeyboardVisible || _isBarcodeKeyboardVisible) {
        _isWarehouseKeyboardVisible = true;
        _isBarcodeKeyboardVisible = false;
      }
    });
  }

  void _focusBarcodeInput() {
    FocusScope.of(context).requestFocus(barcodeFocusNode);
    // 点击输入框不自动显示键盘，只设置焦点
    setState(() {
      // 如果已经有键盘显示，则切换到对应的键盘
      if (_isWarehouseKeyboardVisible || _isBarcodeKeyboardVisible) {
        _isBarcodeKeyboardVisible = true;
        _isWarehouseKeyboardVisible = false;
      }
    });
  }

  // 显示重复库位信息的对话框
  void _showDuplicateLocationsInfo(Map<String, dynamic> record) {
    String oldStock = record['oldStock'] ?? '';
    List<String> duplicateLocations = [];
    
    // 解析oldStock中的重复库位信息
    if (oldStock.startsWith('DUP:')) {
      duplicateLocations = oldStock.substring(4).split('|').where((s) => s.isNotEmpty).toList();
    }
    
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('重复库位信息'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('条码: ${record['barcode']}', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('货号: ${record['articleId']}'),
              Text('当前库位: ${record['warehouse']}'),
              Divider(),
              Text('此条码也存在于以下库位:', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              if (duplicateLocations.isEmpty)
                Text('无法解析库位信息', style: TextStyle(color: Colors.red)),
              ...duplicateLocations.map((loc) => Padding(
                padding: const EdgeInsets.only(bottom: 4.0),
                child: Row(
                  children: [
                    Icon(Icons.location_on, size: 14, color: Colors.orange),
                    SizedBox(width: 4),
                    Text(loc),
                  ],
                ),
              )),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('关闭'),
            ),
          ],
        );
      },
    );
  }

  // 调整某一行数量（delta 可为 +1 或 -1），自动区分在线/离线逻辑
  Future<void> _adjustQuantity(Map<String, dynamic> row, int delta) async {
    final String rowWarehouse = (row['warehouse'] ?? '').toString();
    final String rowBarcode = (row['barcode'] ?? '').toString();
    final String rowArticle = (row['articleId'] ?? '').toString();

    int current = 0;
    final q = row['quantity'];
    if (q is int) {
      current = q;
    } else {
      current = int.tryParse(q?.toString() ?? '0') ?? 0;
    }

    int newQuantity = current + delta;
    if (delta < 0) {
      if (current <= 0) {
        _showSnackBar('当前数量为 0，无法减少');
        return;
      }
      if (newQuantity < 1) {
        newQuantity = 1; // 至少保留 1 件
        _showSnackBar('已自动调整为 1（至少保留 1 件）');
      }
    }

    final int? recordId = (row['id'] is int) ? row['id'] as int : int.tryParse('${row['id'] ?? ''}');

    try {
      if (!isOnline) {
        // 离线：直接更新/插入本地记录（使用 set 模式）
        await localdb.DatabaseService.upsertStocktakingRecord(
          username: username,
          warehouse: rowWarehouse.isNotEmpty ? rowWarehouse : lastScannedWarehouse,
          barcode: rowBarcode.isNotEmpty ? rowBarcode : 'NO',
          quantity: newQuantity,
          updateMode: 'set',
          forceNewRecord: false,
          articleId: rowArticle,
          isUnknown: (rowArticle.isEmpty || rowBarcode.isEmpty || rowBarcode == 'NO'),
          oldStock: row['oldStock'],
          inUse: inUseStatus,
        );
        _showSnackBar('[离线] 数量已更新为: $newQuantity');
      } else {
        // 在线：使用 set 模式精确更新
        await ScanApiService.updateStocktakingQuantity(
          rowWarehouse.isNotEmpty ? rowWarehouse : lastScannedWarehouse,
          rowBarcode.isNotEmpty ? rowBarcode : 'NO',
          newQuantity,
          username,
          inUseStatus,
          updateMode: 'set',
          forceInsert: false,
          allowUnknown: false,
          recordId: recordId,  // 传递了recordId
        );
        _showSnackBar('数量已更新为: $newQuantity');
      }

      // 刷新数据与焦点
      await fetchOnlineData();
      // 去重：若后端插入了新记录且旧记录仍在，则删除旧记录
      if (recordId != null) {
        try {
          final same = scanResults.where((item) =>
            (item['warehouse']?.toString() ?? '') == (rowWarehouse)
            && (item['barcode']?.toString() ?? '') == (rowBarcode.isNotEmpty ? rowBarcode : 'NO')
            && (item['articleId']?.toString() ?? '') == (rowArticle)
          ).toList();
          if (same.length > 1) {
            // 如果存在数量等于新值的记录且其 id != 原 id，则删除原 id
            final newer = same.firstWhere(
              (e) => (int.tryParse('${e['quantity']}') ?? e['quantity'] ?? 0) == newQuantity && e['id'] != recordId,
              orElse: () => {},
            );
            if (newer is Map && newer.isNotEmpty) {
              await deleteUserRecord(recordId);
            }
          }
        } catch (_) {}
      }
      barcodeController.clear();
      if (mounted) FocusScope.of(context).requestFocus(barcodeFocusNode);
      _audioPlayer.play(AssetSource('scan.mp3'));
    } catch (e) {
      _showSnackBar('更新失败: $e', backgroundColor: Colors.red);
    }
  }

  // 弹出对话框，输入要叠加的数量（累加）
  Future<void> _showAddQuantityDialog(Map<String, dynamic> row) async {
    final int? recordId = (row['id'] is int) ? row['id'] as int : int.tryParse('${row['id'] ?? ''}');
    final TextEditingController qtyController = TextEditingController();
    int current = 0;
    final q = row['quantity'];
    if (q is int) {
      current = q;
    } else {
      current = int.tryParse(q?.toString() ?? '0') ?? 0;
    }

    // 打开弹窗时切换到对话框自定义键盘
    setState(() {
      _suspendScanning = true;
      _dialogQtyController = qtyController;
      _dialogQtyFocusNode = FocusNode();
      _dialogOnSubmit = null;
      _isWarehouseKeyboardVisible = false;
      _isBarcodeKeyboardVisible = false;
      _isSearchKeyboardVisible = false;
      _isDialogKeyboardVisible = true;
    });
    // 在弹窗之上显示覆盖层键盘
    _showDialogKeyboardOverlay(controller: qtyController, focusNode: _dialogQtyFocusNode!);

    await showDialog(
      context: context,
      builder: (ctx) {
        final bottomInset = _isDialogKeyboardVisible ? _customKeyboardHeight : 0.0;
        return Padding(
          padding: EdgeInsets.only(bottom: bottomInset),
          child: AlertDialog(
          backgroundColor: Colors.white.withOpacity(0.78),
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(color: Colors.white.withOpacity(0.25)),
          ),
          title: Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.12),
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Colors.blue.withOpacity(0.25)),
                ),
                child: Icon(Icons.edit, color: Colors.blue, size: 20),
              ),
              SizedBox(width: 10),
              Expanded(
                child: Text(
                  '当前数量: $current',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w700),
                ),
              ),
            ],
          ),
          content: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 14, sigmaY: 14),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.55),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.white.withOpacity(0.3)),
                ),
                padding: EdgeInsets.all(12),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    TextField(
                      controller: qtyController,
                      focusNode: _dialogQtyFocusNode,
                      readOnly: false,
                      showCursor: true,
                      enableInteractiveSelection: false,
                      keyboardType: TextInputType.none,
                      decoration: InputDecoration(
                        labelText: '输入增减数量（可为负数，如 -3）',
                        border: OutlineInputBorder(),
                      ),
                      onTap: () {
                        setState(() {
                          _isWarehouseKeyboardVisible = false;
                          _isBarcodeKeyboardVisible = false;
                          _isSearchKeyboardVisible = false;
                          _isDialogKeyboardVisible = true;
                          _dialogQtyController = qtyController;
                        });
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          if (mounted && _dialogQtyFocusNode != null) {
                            FocusScope.of(context).requestFocus(_dialogQtyFocusNode);
                          }
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(ctx).pop(),
              child: Text('取消'),
            ),
            ElevatedButton(
              onPressed: () async {
                int add = 0;
                try {
                  add = int.parse(qtyController.text.trim());
                } catch (_) {
                  _showSnackBar('请输入有效的整数（可为负数）');
                  return;
                }
                if (add == 0) {
                  _showSnackBar('输入为 0，数量未变');
                  return;
                }
                if (add < 0) {
                  int currentQty = 0;
                  final cq = row['quantity'];
                  if (cq is int) currentQty = cq; else currentQty = int.tryParse(cq?.toString() ?? '0') ?? 0;
                  if (currentQty <= 0) {
                    _showSnackBar('当前数量为 0，无法减少');
                    return;
                  }
                  if (currentQty + add < 1) {
                    final adjusted = 1 - currentQty; // 例如当前56，输入-56 -> 调整为-55
                    add = adjusted;
                    _showSnackBar('已自动调整为 $adjusted（至少保留 1 件）');
                  }
                }
                                 Navigator.of(ctx).pop();
                 // 恢复扫描挂起状态在 whenComplete 中处理
                 await _adjustQuantity(row, add);
              },
              child: Text('确定'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
            ),
                    ],
           ),
         );
       },
          ).whenComplete(() {
       // 关闭弹窗时关闭对话框键盘（覆盖层）
       _removeDialogKeyboardOverlay();
       setState(() {
         _suspendScanning = false;
         _isDialogKeyboardVisible = false;
         _dialogQtyController = null;
         _dialogQtyFocusNode = null;
         _dialogOnSubmit = null;
       });
     });
  }

  @override
  Widget build(BuildContext context) {
    return ScaffoldMessenger(
      key: _scaffoldMessengerKey,
      child: GestureDetector(
        onTap: () {
          setState(() {
            _isWarehouseKeyboardVisible = false;
            _isBarcodeKeyboardVisible = false;
          });
          FocusScope.of(context).unfocus();
        },
        child: Scaffold(
          appBar: AppBar(
            titleSpacing: 0, // 移除标题左侧间距
            title: lastScannedWarehouse.isNotEmpty
              ? Align(
                  alignment: Alignment.centerLeft,
                  child: Container(
                    margin: EdgeInsets.only(left: 2), // 更靠近返回箭头
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.blue[400]!, Colors.blue[600]!],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.blue.withOpacity(0.3),
                          blurRadius: 4,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Text(
                      lastScannedWarehouse,
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                )
              : null,
            actions: [
              // 网络状态指示与手动刷新
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Icon(
                  isOnline ? Icons.cloud_done : Icons.cloud_off,
                  color: isOnline ? Colors.green : Colors.red,
                ),
              ),
              // 手动离线切换
              IconButton(
                tooltip: manualOffline ? '切到自动' : '手动离线',
                icon: Icon(manualOffline ? Icons.toggle_on : Icons.toggle_off),
                onPressed: () async {
                  final willEnable = !manualOffline;
                  final tip = await OfflineManager.instance.setManualOffline(willEnable);
                  setState(() {
                    manualOffline = OfflineManager.instance.manualOffline;
                    isOnline = OfflineManager.instance.isOnline.value;
                  });
                  // 进入离线时，提示是否更新商品
                  if (willEnable && manualOffline) {
                    if (!mounted) return;
                    final extra = (tip != null && tip.isNotEmpty) ? '\n\n$tip' : '';
                    showDialog(
                      context: context,
                      barrierColor: Colors.black.withOpacity(0.2),
                      builder: (ctx) => BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
                        child: AlertDialog(
                          backgroundColor: Colors.white.withOpacity(0.8),
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                            side: BorderSide(
                              color: Colors.white.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          title: Container(
                            padding: EdgeInsets.only(bottom: 8),
                            child: Row(
                              children: [
                                Container(
                                  padding: EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: Colors.orange.withOpacity(0.15),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: Colors.orange.withOpacity(0.3),
                                      width: 1,
                                    ),
                                  ),
                                  child: Icon(
                                    Icons.cloud_off_outlined,
                                    color: Colors.orange.shade700,
                                    size: 26,
                                  ),
                                ),
                                SizedBox(width: 16),
                                Expanded(
                                  child: Text(
                                    '离线前更新商品？',
                                    style: TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.w700,
                                      color: Colors.grey.shade800,
                                      letterSpacing: 0.5,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          content: Container(
                            width: double.maxFinite,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // 主要提示信息
                                Container(
                                  padding: EdgeInsets.all(20),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.6),
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: Colors.white.withOpacity(0.4),
                                      width: 1,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.05),
                                        blurRadius: 10,
                                        offset: Offset(0, 4),
                                      ),
                                    ],
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.info_outline,
                                        color: Colors.blue.shade600,
                                        size: 24,
                                      ),
                                      SizedBox(width: 16),
                                      Expanded(
                                        child: Text(
                                          '建议在离线前更新商品数据，以保证离线扫描体验。',
                                          style: TextStyle(
                                            fontSize: 15,
                                            color: Colors.grey.shade700,
                                            fontWeight: FontWeight.w500,
                                            height: 1.5,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                if (extra.isNotEmpty) ...[
                                  SizedBox(height: 16),
                                  // 额外提示信息
                                  Container(
                                    padding: EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.amber.shade50.withOpacity(0.7),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: Colors.amber.withOpacity(0.3),
                                        width: 1,
                                      ),
                                    ),
                                    child: Row(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Icon(
                                          Icons.warning_amber_outlined,
                                          color: Colors.amber.shade700,
                                          size: 20,
                                        ),
                                        SizedBox(width: 12),
                                        Expanded(
                                          child: Text(
                                            extra.trim(),
                                            style: TextStyle(
                                              fontSize: 13,
                                              color: Colors.amber.shade800,
                                              fontWeight: FontWeight.w500,
                                              height: 1.4,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                                SizedBox(height: 20),
                                // 选项说明
                                Container(
                                  padding: EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.grey.shade50.withOpacity(0.8),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: Colors.grey.shade200.withOpacity(0.8),
                                      width: 1,
                                    ),
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        '更新选项：',
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600,
                                          color: Colors.grey.shade700,
                                        ),
                                      ),
                                      SizedBox(height: 8),
                                      Row(
                                        children: [
                                          Icon(Icons.sync, size: 16, color: Colors.blue.shade600),
                                          SizedBox(width: 8),
                                          Text(
                                            '增量同步：只下载更新的商品数据',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey.shade600,
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 4),
                                      Row(
                                        children: [
                                          Icon(Icons.download, size: 16, color: Colors.green.shade600),
                                          SizedBox(width: 8),
                                          Text(
                                            '完整下载：下载全部商品数据',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey.shade600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          actions: [
                            // 按钮容器
                            Container(
                              width: double.maxFinite,
                              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                              child: Row(
                                children: [
                                  // 跳过按钮
                                  Expanded(
                                    child: TextButton(
                                      onPressed: () => Navigator.of(ctx).pop(),
                                      child: Text('跳过', maxLines: 1, overflow: TextOverflow.ellipsis, style: TextStyle(fontWeight: FontWeight.w600)),
                                      style: TextButton.styleFrom(
                                        foregroundColor: Colors.grey.shade600,
                                        backgroundColor: Colors.grey.withOpacity(0.1),
                                        minimumSize: const Size(0, 44),
                                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                        padding: EdgeInsets.symmetric(vertical: 12),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(10),
                                          side: BorderSide(
                                            color: Colors.grey.withOpacity(0.3),
                                            width: 1,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  // 增量同步按钮
                                  Expanded(
                                    child: TextButton(
                                      onPressed: () async {
                                        Navigator.of(ctx).pop();
                                        await _downloadProductData(incremental: true, ignoreOnlineCheck: true);
                                      },
                                      child: Text('增量同步', maxLines: 1, overflow: TextOverflow.ellipsis, style: TextStyle(fontWeight: FontWeight.w600)),
                                      style: TextButton.styleFrom(
                                        foregroundColor: Colors.blue.shade700,
                                        backgroundColor: Colors.blue.withOpacity(0.1),
                                        minimumSize: const Size(0, 44),
                                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                        padding: EdgeInsets.symmetric(vertical: 12),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(10),
                                          side: BorderSide(
                                            color: Colors.blue.withOpacity(0.3),
                                            width: 1,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  // 完整下载按钮
                                  Expanded(
                                    child: TextButton(
                                      onPressed: () async {
                                        Navigator.of(ctx).pop();
                                        await _downloadProductData(incremental: false, ignoreOnlineCheck: true);
                                      },
                                      child: Text('完整下载', maxLines: 1, overflow: TextOverflow.ellipsis, style: TextStyle(fontWeight: FontWeight.w600)),
                                      style: TextButton.styleFrom(
                                        foregroundColor: Colors.green.shade700,
                                        backgroundColor: Colors.green.withOpacity(0.1),
                                        minimumSize: const Size(0, 44),
                                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                        padding: EdgeInsets.symmetric(vertical: 12),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(10),
                                          side: BorderSide(
                                            color: Colors.green.withOpacity(0.3),
                                            width: 1,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  } else {
                    await _refreshNetworkStatus(showSnack: true);
                  }
                  await fetchOnlineData();
                },
              ),
              IconButton(
                icon: Icon(_isWarehouseKeyboardVisible || _isBarcodeKeyboardVisible ? Icons.keyboard_hide : Icons.keyboard),
                onPressed: _toggleKeyboard, // 使用新的方法处理键盘显示
              ),
              IconButton(
                icon: Icon(Icons.settings),
                onPressed: () {
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    backgroundColor: Colors.transparent,
                    barrierColor: Colors.black.withOpacity(0.2),
                    builder: (context) {
                      return BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
                        child: Container(
                          margin: EdgeInsets.fromLTRB(12, 0, 12, 12),
                          padding: EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.8),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
                          ),
                          child: SafeArea(
                            top: false,
                            child: SingleChildScrollView(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  SwitchListTile(
                                    title: Text('检测重复库位'),
                                    value: isDuplicateCheckEnabled,
                                    onChanged: (bool value) {
                                      setState(() {
                                        isDuplicateCheckEnabled = value;
                                      });
                                      Navigator.pop(context);
                                    },
                                    secondary: Icon(isDuplicateCheckEnabled ? Icons.check_box : Icons.check_box_outline_blank),
                                  ),
                                  SwitchListTile(
                                    title: Text('手动输入数量模式'),
                                    subtitle: Text('启用后扫描条码会提示输入数量'),
                                    value: isManualModeEnabled,
                                    onChanged: (bool value) {
                                      setState(() {
                                        isManualModeEnabled = value;
                                      });
                                      Navigator.pop(context);
                                    },
                                    secondary: Icon(Icons.dialpad),
                                  ),
                                                                     ListTile(
                                      leading: Icon(Icons.sync),
                                      title: Text('同步库位'),
                                      onTap: () {
                                        Navigator.pop(context);
                                        synchronizeLocationData();
                                      },
                                    ),
                                  ListTile(
                                    leading: Icon(Icons.refresh),
                                    title: Text('刷新页面'),
                                    onTap: () {
                                      Navigator.pop(context);
                                      refreshPage();
                                    },
                                  ),
                                  ListTile(
                                    leading: Icon(Icons.view_column),
                                    title: Text('重置列显示'),
                                    onTap: () {
                                      setState(() {
                                        hideIdColumn = false;
                                        hideWarehouseColumn = false;
                                        hideBarcodeColumn = false;
                                        hideArticleColumn = false;
                                        hideQuantityColumn = false;
                                        hideActionColumn = false;
                                      });
                                      Navigator.pop(context);
                                    },
                                  ),
                                  ListTile(
                                    leading: Icon(Icons.delete_forever),
                                    title: Text('清除全部数据'),
                                    onTap: () {
                                      Navigator.pop(context);
                                      _confirmClearAllData();
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ],
          ),
          body: Column(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(0.0, 10.0, 10.0, 10.0),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: warehouseController,
                              focusNode: warehouseFocusNode,
                              readOnly: false,
                              showCursor: true,
                              enableInteractiveSelection: false,
                              keyboardType: TextInputType.none,
                              decoration: InputDecoration(
                                labelText: '扫描库位...',
                                border: OutlineInputBorder(),
                              ),
                              onTap: () {
                                _focusWarehouseInput();
                              },
                              onChanged: (value) {
                                if (value.contains('\n') || value.contains('\r')) {
                                  String query = value.replaceAll('\n', '').replaceAll('\r', '').trim();
                                  if (query.isNotEmpty) {
                                    handleScan('warehouse', query);
                                  }
                                }
                              },
                              onSubmitted: (value) {
                                if (value.isNotEmpty) {
                                  handleScan('warehouse', value);
                                }
                              },
                            ),
                          ),
                          SizedBox(width: 8),
                          Tooltip(
                            message: _isSearchMode ? '关闭查找' : '查找（模糊匹配）',
                            child: Container(
                              decoration: BoxDecoration(
                                color: (_isSearchMode ? Colors.red.withOpacity(0.08) : Colors.blue.withOpacity(0.08)),
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(color: _isSearchMode ? Colors.red.shade200 : Colors.blue.shade200),
                              ),
                              child: IconButton(
                                onPressed: () {
                                  if (_isSearchMode) {
                                    _exitSearchMode();
                                  } else {
                                    _enterSearchMode();
                                  }
                                },
                                icon: Icon(_isSearchMode ? Icons.close : Icons.search,
                                  size: 20,
                                  color: _isSearchMode ? Colors.red : Colors.blue,
                                ),
                                splashRadius: 20,
                                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                                tooltip: _isSearchMode ? '关闭查找' : '查找（模糊匹配）',
                              ),
                            ),
                          ),
                        ],
                      ),
                                             if (_isSearchMode) ...[
                         // 搜索模式下隐藏扫描库位和扫描条码输入框
                         // 仅显示搜索输入框

                        SizedBox(height: 8),
                        TextField(
                          controller: _searchController,
                          focusNode: _searchFocusNode,
                          readOnly: false,
                          showCursor: true,
                          enableInteractiveSelection: false,
                          keyboardType: TextInputType.none,
                          decoration: InputDecoration(
                            labelText: '输入关键字（库位/条码/货号）',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.search),
                            suffixIcon: (_searchQuery.isNotEmpty)
                                ? IconButton(
                                    icon: Icon(Icons.clear),
                                    onPressed: () {
                                      _applySearch('');
                                      _searchController.clear();
                                      FocusScope.of(context).requestFocus(_searchFocusNode);
                                    },
                                  )
                                : null,
                          ),
                          onChanged: _applySearch,
                          onTap: () {
                            // 点击搜索输入框时，切换到搜索键盘并保持搜索焦点
                            setState(() {
                              _isWarehouseKeyboardVisible = false;
                              _isBarcodeKeyboardVisible = false;
                              _isSearchKeyboardVisible = true;
                            });
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              if (mounted) FocusScope.of(context).requestFocus(_searchFocusNode);
                            });
                          },
                        ),
                      ],
                                             if (!_isSearchMode) ...[
                         SizedBox(height: 10),
                         TextField(
                           controller: barcodeController,
                           focusNode: barcodeFocusNode,
                           readOnly: false, 
                        showCursor: true,
                        enableInteractiveSelection: false,
                        keyboardType: TextInputType.none,
                        decoration: InputDecoration(
                          labelText: '扫描条码...',
                          border: OutlineInputBorder(),
                        ),
                        onTap: () {
                          _focusBarcodeInput();
                        },
                        onChanged: (value) {
                          if (value.contains('\n') || value.contains('\r')) {
                            String query = value.replaceAll('\n', '').replaceAll('\r', '').trim();
                            if (query.isNotEmpty) {
                              handleScan('barcode', query);
                            }
                          }
                        },
                        onSubmitted: (value) {
                          if (value.isNotEmpty) {
                            handleScan('barcode', value);
                          }
                                                 },
                       ),
                       ],
                       SizedBox(height: 10),
                       Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 0.0, right: 0.0, top: 5.0, bottom: 5.0),
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            controller: _dataHorizontalScrollController,
                            child: SingleChildScrollView(
                              scrollDirection: Axis.vertical,
                              controller: _verticalScrollController,
                              child: LayoutBuilder(
                                builder: (context, constraints) {
                                  // 获取屏幕宽度
                                  double screenWidth = MediaQuery.of(context).size.width;

                                  // 动态分配列宽度（比例分配）
                                  double idColumnWidth = hideIdColumn ? 0 : screenWidth * 0.1; // 序号
                                  double warehouseColumnWidth = hideWarehouseColumn ? 0 : screenWidth * 0.1; // 库位
                                  double barcodeColumnWidth = hideBarcodeColumn ? 0 : screenWidth * 0.2; // 条形码
                                  double articleColumnWidth = hideArticleColumn ? 0 : screenWidth * 0.1; // 货号
                                  double quantityColumnWidth = hideQuantityColumn ? 0 : screenWidth * 0.1; // 数量
                                  double actionColumnWidth = hideActionColumn ? 0 : max(screenWidth * 0.2, 60.0); // 操作

                                  return SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    controller: _dataHorizontalScrollController,
                                    child: SingleChildScrollView(
                                      scrollDirection: Axis.vertical,
                                      controller: _verticalScrollController,
                                      child: DataTable(
                                        columnSpacing: 10, // 列距
                                        headingRowHeight: 40, // 表头高度
                                        dataRowMinHeight: 40, // 数据行最小高度
                                        dataRowMaxHeight: 60, // 数据行最大高度
                                        horizontalMargin: 0, // 将表格左侧边距设为0，使序号靠近屏幕边缘
                                        headingTextStyle: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 14, // 自适应字体大小
                                          color: Colors.blueGrey,
                                        ),
                                        columns: [
                                          if (!hideIdColumn)
                                          DataColumn(
                                            label: GestureDetector(
                                              onDoubleTap: () => setState(() => hideIdColumn = true),
                                              child: Container(
                                                width: idColumnWidth,
                                                alignment: Alignment.centerLeft,
                                                padding: EdgeInsets.only(left: 5.0), // 为序号单元格添加左内边距
                                                child: Text('序号', textAlign: TextAlign.left),
                                              ),
                                            ),
                                          ),
                                          if (!hideWarehouseColumn)
                                          DataColumn(
                                            label: GestureDetector(
                                              onDoubleTap: () => setState(() => hideWarehouseColumn = true),
                                              child: Container(
                                                width: warehouseColumnWidth,
                                                alignment: Alignment.centerLeft,
                                                child: Text('库位'),
                                              ),
                                            ),
                                          ),
                                          if (!hideBarcodeColumn)
                                          DataColumn(
                                            label: GestureDetector(
                                              onDoubleTap: () => setState(() => hideBarcodeColumn = true),
                                              child: Container(
                                                width: barcodeColumnWidth,
                                                alignment: Alignment.centerLeft,
                                                child: Text('条形码'),
                                              ),
                                            ),
                                          ),
                                          if (!hideArticleColumn)
                                          DataColumn(
                                            label: GestureDetector(
                                              onDoubleTap: () => setState(() => hideArticleColumn = true),
                                              child: Container(
                                                width: articleColumnWidth,
                                                alignment: Alignment.centerLeft,
                                                child: Text('货号'),
                                              ),
                                            ),
                                          ),
                                          if (!hideQuantityColumn)
                                          DataColumn(
                                            label: GestureDetector(
                                              onDoubleTap: () => setState(() => hideQuantityColumn = true),
                                              child: Container(
                                                width: quantityColumnWidth,
                                                alignment: Alignment.centerRight,
                                                padding: EdgeInsets.zero,
                                                child: Text('数量'),
                                              ),
                                            ),
                                          ),
                                          if (!hideActionColumn)
                                          DataColumn(
                                            label: GestureDetector(
                                              onDoubleTap: () => setState(() => hideActionColumn = true),
                                              child: Container(
                                                width: actionColumnWidth,
                                                alignment: Alignment.centerRight,
                                                padding: EdgeInsets.zero,
                                                child: Text('操作'),
                                              ),
                                            ),
                                          ),
                                        ],
                                                                                 rows: (_isSearchMode ? _filteredResults : scanResults).map((result) {
                                          return DataRow(
                                            color: MaterialStateProperty.resolveWith((states) {
                                              // 根据记录状态设置颜色
                                              if (result['isUnknown'] == true) {
                                                return Colors.red.shade100; // 未知商品显示为红色
                                              } else if (result['isSynced'] ?? false) {
                                                return Colors.green.shade100; // 已同步显示为绿色
                                              } else if (result['hasDuplicateLocation'] == true) {
                                                return Colors.orange.shade50; // 有重复库位显示为橙色
                                              }
                                              return Colors.transparent; // 默认透明
                                            }),
                                            cells: [
                                              if (!hideIdColumn)
                                              DataCell(Container(
                                                alignment: Alignment.centerLeft,
                                                padding: EdgeInsets.only(left: 5.0), // 为序号单元格添加左内边距
                                                child: Text(result['id'].toString(), style: TextStyle(fontSize: 12)),
                                              )),
                                              if (!hideWarehouseColumn)
                                              DataCell(Container(
                                                alignment: Alignment.centerLeft,
                                                child: Row(
                                                  mainAxisSize: MainAxisSize.min,
                                                  children: [
                                                    Text(result['warehouse'], style: TextStyle(fontSize: 12)),
                                                    if (result['hasDuplicateLocation'] == true)
                                                      Icon(Icons.warning_amber_rounded, color: Colors.orange, size: 14),
                                                  ],
                                                ),
                                              )),
                                              if (!hideBarcodeColumn)
                                              DataCell(Container(
                                                alignment: Alignment.center,
                                                child: Text(result['barcode'], 
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: result['isUnknown'] == true ? FontWeight.bold : FontWeight.normal, // 未知商品加粗
                                                    color: result['hasDuplicateLocation'] == true ? Colors.deepOrange : null, // 重复库位条码颜色
                                                  ),
                                                ),
                                              )),
                                              if (!hideArticleColumn)
                                              DataCell(Container(
                                                alignment: Alignment.center,
                                                child: Text(
                                                  result['articleId'] ?? '', 
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: result['isUnknown'] == true ? FontWeight.bold : FontWeight.normal,
                                                    color: result['hasDuplicateLocation'] == true ? Colors.deepOrange : null,
                                                  ),
                                                ),
                                              )),
                                              if (!hideQuantityColumn)
                                                                                            DataCell(Container(
                                                 alignment: Alignment.centerRight,
                                                 padding: EdgeInsets.only(right: 24.0),
                                                 child: InkWell(
                                                   onTap: () => _showAddQuantityDialog(result),
                                                   borderRadius: BorderRadius.circular(6),
                                                   child: Padding(
                                                     padding: EdgeInsets.symmetric(horizontal: 4, vertical: 6),
                                                     child: Builder(
                                                       builder: (_) {
                                                         final qtyStr = (result['quantity'] ?? 0).toString();
                                                         final len = qtyStr.length;
                                                         final size = len <= 2 ? 16.0 : (len <= 4 ? 14.0 : 12.0);
                                                         return Text(
                                                           qtyStr,
                                                           style: TextStyle(
                                                             fontSize: size,
                                                             fontWeight: FontWeight.w600,
                                                             color: (result['isUnknown'] == true)
                                                                 ? Colors.red
                                                                 : (result['hasDuplicateLocation'] == true
                                                                     ? Colors.deepOrange
                                                                     : Colors.blue),
                                                           ),
                                                         );
                                                       },
                                                     ),
                                                   ),
                                                 ),
                                               )),
                                              if (!hideActionColumn)
                                                                                             DataCell(
                                                 Container(
                                                   padding: EdgeInsets.only(left: 28.0),
                                                   child: Row(
                                                     mainAxisSize: MainAxisSize.min,
                                                    children: [
                                                      IconButton(
                                                        icon: Icon(Icons.delete, size: 16),
                                                        padding: EdgeInsets.zero,
                                                        constraints: BoxConstraints(),
                                                        onPressed: () {
                                                          confirmDeleteRecord(result['id'], result['barcode'], result['warehouse'], result['isSynced'] ?? false);
                                                        },
                                                      ),
                                                      if (result['hasDuplicateLocation'] == true)
                                                        IconButton(
                                                          icon: Icon(Icons.visibility, size: 16),
                                                          padding: EdgeInsets.zero,
                                                          constraints: BoxConstraints(),
                                                          onPressed: () {
                                                            _showDuplicateLocationsInfo(result);
                                                          },
                                                        ),
                                                    ],
                                                  ),
                                                )
                                              ),
                                            ],
                                          );
                                        }).toList(),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              if (_isWarehouseKeyboardVisible || _isBarcodeKeyboardVisible || _isSearchKeyboardVisible || _isDialogKeyboardVisible)
                Container(
                  color: Colors.grey[200],
                  child: _isWarehouseKeyboardVisible
                    ? CustomKeyboard(
                        controller: warehouseController,
                        onSubmit: (value) {
                          handleScan('warehouse', value);
                          // 库位扫描提交后，延迟50毫秒确保handleScan已处理完毕，然后切换到条码输入框
                          Future.delayed(Duration(milliseconds: 50), () {
                            setState(() {
                              _isWarehouseKeyboardVisible = false;
                              _isBarcodeKeyboardVisible = true;
                            });
                            FocusScope.of(context).requestFocus(barcodeFocusNode);
                          });
                        },
                        showKeyboard: _isWarehouseKeyboardVisible,
                        onKeyboardVisibilityChanged: () {
                          setState(() {
                            _isWarehouseKeyboardVisible = !_isWarehouseKeyboardVisible;
                          });
                        },
                      )
                    : (_isBarcodeKeyboardVisible
                        ? CustomKeyboard(
                            controller: barcodeController,
                            onSubmit: (value) {
                              handleScan('barcode', value);
                              setState(() {
                                _isBarcodeKeyboardVisible = true;
                              });
                              FocusScope.of(context).requestFocus(barcodeFocusNode);
                            },
                            showKeyboard: _isBarcodeKeyboardVisible,
                            onKeyboardVisibilityChanged: () {
                              setState(() {
                                _isBarcodeKeyboardVisible = !_isBarcodeKeyboardVisible;
                              });
                            },
                          )
                        : (_isSearchKeyboardVisible
                            ? CustomKeyboard(
                                controller: _searchController,
                                onSubmit: (value) {
                                  _applySearch(value);
                                  setState(() {
                                    _isSearchKeyboardVisible = true;
                                  });
                                  FocusScope.of(context).requestFocus(_searchFocusNode);
                                },
                                showKeyboard: _isSearchKeyboardVisible,
                                onKeyboardVisibilityChanged: () {
                                  setState(() {
                                    _isSearchKeyboardVisible = !_isSearchKeyboardVisible;
                                  });
                                },
                              )
                            : CustomKeyboard(
                                controller: _dialogQtyController ?? TextEditingController(),
                                onSubmit: (value) {
                                  // 提交后写入对话框输入并不自动关闭
                                  if (_dialogQtyController != null) {
                                    _dialogQtyController!.text = value;
                                  }
                                  setState(() {
                                    _isDialogKeyboardVisible = true;
                                  });
                                  if (_dialogQtyFocusNode != null) {
                                    FocusScope.of(context).requestFocus(_dialogQtyFocusNode);
                                  }
                                },
                                showKeyboard: _isDialogKeyboardVisible,
                                onKeyboardVisibilityChanged: () {
                                  setState(() {
                                    _isDialogKeyboardVisible = !_isDialogKeyboardVisible;
                                  });
                                },
                              )
                          )
                      ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

// 同步进度指示器对话框
class SyncProgressDialog extends StatefulWidget {
  final int total;
  final Stream<int> syncedCountStream;

  SyncProgressDialog({required this.total, required this.syncedCountStream});

  @override
  _SyncProgressDialogState createState() => _SyncProgressDialogState();
}

class _SyncProgressDialogState extends State<SyncProgressDialog> {
  late StreamSubscription<int> _subscription;
  int _syncedCount = 0;

  @override
  void initState() {
    super.initState();
    _subscription = widget.syncedCountStream.listen((count) {
      setState(() {
        _syncedCount = count;
      });
    });
  }

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double progress = _syncedCount / widget.total;

    return AlertDialog(
      title: Text('同步中...'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          LinearProgressIndicator(value: progress),
          SizedBox(height: 20),
          Text('已同步 $_syncedCount / ${widget.total} 条记录'),
        ],
      ),
    );
  }
}

// 已移除：全局 _autoSyncOfflineRecordsSilent，使用类内方法

  Future<bool> _isInputBarcode(String input) async {
    final trimmed = input.trim();
    if (trimmed.isEmpty) return false;
    try {
      // 如果本地表存在同值的条码，则判定为条码
      final rowByBarcode = await localdb.DatabaseService.getArticuloByBarcode(trimmed);
      if (rowByBarcode != null) return true;
      // 如果本地表存在同值的货号，则判定为货号
      final rowByArticulo = await localdb.DatabaseService.getArticuloByArticuloId(trimmed);
      if (rowByArticulo != null) return false;
    } catch (_) {}
    // 兜底规则：纯数字且长度在常见条码长度范围 → 条码，否则货号
    final isNumeric = RegExp(r'^\d+$').hasMatch(trimmed);
    if (isNumeric) {
      final len = trimmed.length;
      if (len == 12 || len == 13 || len == 14 || len == 8) {
        return true;
      }
    }
    return false;
  }

