import 'package:flutter/material.dart';
import 'package:mistoer/models/order.dart';
import 'package:mistoer/models/order_detail.dart';
import 'package:mistoer/services/api_service.dart';
import 'package:mistoer/services/database_service.dart';
import 'package:mistoer/services/database_service_sn.dart';

class OrderConfigScreen extends StatefulWidget {
  final Order order;

  OrderConfigScreen({required this.order});

  @override
  _OrderConfigScreenState createState() => _OrderConfigScreenState();
}

class _OrderConfigScreenState extends State<OrderConfigScreen> {
  final TextEditingController _companyNameController = TextEditingController();
  final TextEditingController _contactPersonController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();
  double _uploadProgress = 0;

  @override
  void initState() {
    super.initState();
    _companyNameController.text = widget.order.name;
    _amountController.text = widget.order.amount.toString();
    _fetchContactPerson();
    // Removed _fetchBohaoValue() since it wasn't used
  }

  // Removed _fetchBohaoValue() method

  Future<void> _fetchContactPerson() async {
    try {
      final contactPerson = await ApiService.fetchContactPerson(widget.order.pedidoKey);
      setState(() {
        _contactPersonController.text = contactPerson;
      });
    } catch (e) {
      print('Error fetching contact person: $e');
      _showErrorDialog('无法加载联系人信息。');
    }
  }

  Future<void> _submit() async {
    bool shouldProceed = await _showConfirmationDialog();

    if (shouldProceed) {
      try {
        int totalItems = await DatabaseService.getOutOfStockItemCount(widget.order.pedidoKey);
        int uploadedItems = 0;

        // Upload out-of-stock items in batches
        List<OrderDetail> outOfStockItems = await DatabaseService.getOutOfStockItems(widget.order.pedidoKey);
        const int batchSize = 50;
        for (int i = 0; i < outOfStockItems.length; i += batchSize) {
          List<Map<String, dynamic>> batch = outOfStockItems.sublist(
            i,
            i + batchSize > outOfStockItems.length ? outOfStockItems.length : i + batchSize,
          ).map((detail) => {
            'pedidoKey': detail.pedidoKey,
            'ArticuloID': detail.codigo,
            'CodigoBarra': detail.bianhao,
            'Cantidad': detail.zongshuliang,
          }).toList();

          await ApiService.uploadOutOfStockItems(batch);

          // Update upload progress
          uploadedItems += batch.length;
          setState(() {
            _uploadProgress = (uploadedItems / totalItems) * 100;
          });
        }

        int ticketColgandoNo = await getNewNumberBasedOnBohao();
        await _uploadOrderDetails(ticketColgandoNo);
        await _updateOrderStatusToComplete();
        // await _updateTicketColgandoTable(ticketColgandoNo, _contactPersonController.text, widget.order.pedidoKey);
        await _markOrderAsFinished();
        Navigator.of(context).pop();
      } catch (e) {
        print('Error submitting order: $e');
        _showErrorDialog('提交订单时出错。');
      }
    }
  }

  Future<void> _uploadOrderDetails(int ticketColgandoNo) async {
    try {
      int? bohao = await DatabaseServiceSN.getBohaoValue();

      String colgandoFieldName = (bohao == 1) ? 'DocumentoNo' : 'TicketColgandoNo';

      List<OrderDetail> orderDetails = await DatabaseService.getOrderDetails(widget.order.pedidoKey);
      List<Map<String, dynamic>> dataToUpload = [];

      for (int i = 0; i < orderDetails.length; i++) {
        OrderDetail detail = orderDetails[i];
        if (detail.scan > 0) {
          dataToUpload.add({
            'ArticuloID': detail.codigo,
            'CodigoBarra': detail.bianhao,
            'Cantidad': detail.scan,
            colgandoFieldName: ticketColgandoNo,
            'OrdenNo': i,
            'pedidoKey': widget.order.pedidoKey,
          });
        }
      }

      if (dataToUpload.isNotEmpty) {
        bool isSuccess = await ApiService.uploadOrderDetails(widget.order.pedidoKey, dataToUpload);
        if (!isSuccess) {
          throw Exception('Failed to upload order details to server.');
        }
      } else {
        throw Exception('No items to upload.');
      }
    } catch (e) {
      print('Error uploading order details: $e');
      throw Exception('无法上传订单详情。');
    }
  }

  Future<int> getNewNumberBasedOnBohao() async {
    int? bohao = await DatabaseServiceSN.getBohaoValue();
    print('Bohao value: $bohao');

    if (bohao == 0) {
      return await _getNewTicketColgandoNo();
    } else if (bohao == 1) {
      return await _getNewDocumentoNo();
    } else {
      throw Exception('无效的 bohao 值: $bohao');
    }
  }

  Future<int> _getNewTicketColgandoNo() async {
    try {
      int maxTicketColgandoNo = await ApiService.getMaxTicketColgandoNo();
      return maxTicketColgandoNo + 1;
    } catch (e) {
      print('Error getting max TicketColgandoNo: $e');
      throw Exception('无法获取最大 TicketColgandoNo。');
    }
  }

  Future<int> _getNewDocumentoNo() async {
    try {
      int maxDocumentoNo = await ApiService.getMaxDocumentoNo();
      return maxDocumentoNo + 1;
    } catch (e) {
      print('Error getting max DocumentoNo: $e');
      throw Exception('无法获取最大 DocumentoNo。');
    }
  }

  Future<void> _updateOrderStatusToComplete() async {
    try {
      await ApiService.updateOrderStatusToComplete(widget.order.pedidoKey);
    } catch (e) {
      print('Error updating order status: $e');
      throw Exception('无法更新订单状态。');
    }
  }

  // Future<void> _updateTicketColgandoTable(int ticketColgandoNo, String clienteID, String colgarNota) async {
  //   try {
  //     int? bohao = await DatabaseServiceSN.getBohaoValue();
  //
  //     String colgandoFieldName = (bohao == 1) ? 'DocumentoNo' : 'TicketColgandoNo';
  //
  //     await ApiService.updateTicketColgandoTable(colgandoFieldName, ticketColgandoNo, clienteID, colgarNota, bohao);
  //   } catch (e) {
  //     print('Error updating ticket colgando table: $e');
  //     throw Exception('无法更新 ticket colgando 表。');
  //   }
  // }

  Future<bool> _showConfirmationDialog() async {
    return (await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('确认'),
        content: Text('你确定要提交此订单吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text('确认'),
          ),
        ],
      ),
    )) ??
        false;
  }

  Future<void> _markOrderAsFinished() async {
    try {
      await ApiService.markOrderAsFinished(widget.order.pedidoKey);
    } catch (e) {
      print('Error marking order as finished: $e');
      throw Exception('无法将订单标记为已完成。');
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('错误'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('确定'),
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    _companyNameController.dispose();
    _contactPersonController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('配置订单'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            LinearProgressIndicator(value: _uploadProgress / 100),
            SizedBox(height: 20),
            TextFormField(
              initialValue: widget.order.pedidoKey,
              decoration: InputDecoration(labelText: '订单编号'),
              readOnly: true,
            ),
            SizedBox(height: 10),
            TextFormField(
              controller: _companyNameController,
              decoration: InputDecoration(labelText: '公司名称'),
            ),
            SizedBox(height: 10),
            TextFormField(
              controller: _contactPersonController,
              decoration: InputDecoration(labelText: '联系人'),
              readOnly: true,
            ),
            SizedBox(height: 10),
            TextFormField(
              controller: _amountController,
              decoration: InputDecoration(labelText: '金额'),
              keyboardType: TextInputType.number,
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: _submit,
              child: Text('提交'),
            ),
          ],
        ),
      ),
    );
  }
}
