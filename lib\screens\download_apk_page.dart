import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';

class DownloadApkPage extends StatefulWidget {
  final String downloadUrl;  // 下载地址传递
  DownloadApkPage({required this.downloadUrl});

  @override
  _DownloadApkPageState createState() => _DownloadApkPageState();
}

class _DownloadApkPageState extends State<DownloadApkPage> {
  static const platform = MethodChannel('com.example.mistoer/install');  // 用于安装 APK
  int _progress = 0;

  @override
  void initState() {
    super.initState();
    _initializeDownloader();
  }

  // 初始化下载器并请求权限
  Future<void> _initializeDownloader() async {
    WidgetsFlutterBinding.ensureInitialized();
    await FlutterDownloader.initialize(debug: true);
    await requestPermissions();  // 请求存储权限
  }

  // 请求存储权限
  Future<void> requestPermissions() async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.storage,
      Permission.manageExternalStorage,  // 针对 Android 11 及以上版本
    ].request();

    if (statuses[Permission.storage]!.isGranted) {
      print("Storage permission granted");
    } else {
      print("Storage permission denied");
    }
  }

  // 下载 APK 文件并保存到本地
  Future<void> downloadApk() async {
    final downloadUrl = widget.downloadUrl;
    final segments = downloadUrl.split('/');
    final fileName = segments.last;  // 提取文件名（如 1.10.0.apk）

    // 定义保存路径
    final savedPath = '/storage/emulated/0/Download/$fileName';  // 使用提取的文件名作为本地保存名

    final taskId = await FlutterDownloader.enqueue(
      url: downloadUrl,  // 下载 URL
      savedDir: '/storage/emulated/0/Download',  // 保存目录
      fileName: fileName,  // 使用从 URL 提取的文件名
      saveInPublicStorage: true,  // 确保保存到公共存储中
      showNotification: true,  // 显示通知
      openFileFromNotification: true,  // 完成后打开通知
    );

    // 注册下载进度回调
    FlutterDownloader.registerCallback((id, status, progress) {
      setState(() {
        _progress = progress;  // 更新下载进度
      });

      if (status == DownloadTaskStatus.complete) {
        print('Download complete: $id');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('下载完成，正在安装...')),
        );
        installApk(savedPath, fileName);  // 下载完成后安装 APK，传递存储路径和文件名
      }
    });
  }

  // 调用 Android 端代码来安装 APK
  Future<void> installApk(String filePath, String fileName) async {
    try {
      await platform.invokeMethod('installApk', {
        'filePath': filePath,   // 传递存储路径
        'fileName': fileName,   // 传递文件名
      });
    } on PlatformException catch (e) {
      print("Failed to install APK: ${e.message}");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Download and Install APK'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: downloadApk,  // 点击按钮启动下载
              child: Text('Download and Install APK'),
            ),
            SizedBox(height: 20),
            if (_progress > 0)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: LinearProgressIndicator(value: _progress / 100),
              ),
            if (_progress > 0)
              Text('下载进度: $_progress%'),
          ],
        ),
      ),
    );
  }
}
