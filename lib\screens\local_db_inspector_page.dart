import 'package:flutter/material.dart';
import 'package:mistoer/services/database_service.dart' as localdb;

class LocalDbInspectorPage extends StatefulWidget {
  final String username;
  const LocalDbInspectorPage({Key? key, required this.username}) : super(key: key);

  @override
  State<LocalDbInspectorPage> createState() => _LocalDbInspectorPageState();
}

class _LocalDbInspectorPageState extends State<LocalDbInspectorPage> {
  final TextEditingController _inputController = TextEditingController();
  String _queryType = 'ArticuloID'; // or CodigoBarra
  Map<String, dynamic>? _articuloRow;
  Map<String, dynamic>? _swappedRow;
  Map<String, dynamic>? _stats;
  String? _lastSyncTime;
  List<Map<String, dynamic>> _recentStocktaking = [];
  bool _loading = false;

  @override
  void initState() {
    super.initState();
    _loadStats();
  }

  Future<void> _loadStats() async {
    final stats = await localdb.DatabaseService.getArticulosStats();
    final lastSync = await localdb.DatabaseService.getLastSyncTime('articulos');
    setState(() {
      _stats = stats;
      _lastSyncTime = lastSync;
    });
  }

  Future<void> _runQuery() async {
    final q = _inputController.text.trim();
    if (q.isEmpty) {
      setState(() {
        _articuloRow = null;
        _swappedRow = null;
      });
      return;
    }
    setState(() {
      _loading = true;
    });
    try {
      Map<String, dynamic>? row;
      Map<String, dynamic>? swapped;
      if (_queryType == 'ArticuloID') {
        row = await localdb.DatabaseService.getArticuloByArticuloId(q);
        swapped = await localdb.DatabaseService.getArticuloByBarcode(q);
      } else {
        row = await localdb.DatabaseService.getArticuloByBarcode(q);
        swapped = await localdb.DatabaseService.getArticuloByArticuloId(q);
      }
      setState(() {
        _articuloRow = row;
        _swappedRow = swapped;
      });
    } finally {
      setState(() {
        _loading = false;
      });
    }
  }

  Future<void> _loadRecentStocktaking() async {
    final rows = await localdb.DatabaseService.getLocalStocktakingDataForUser(widget.username);
    setState(() {
      _recentStocktaking = rows.take(20).toList();
    });
  }

  Widget _buildKeyValue(String key, String? value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 110,
          child: Text(key, style: const TextStyle(color: Colors.grey)),
        ),
        Expanded(
          child: Text(value ?? '', style: const TextStyle(fontWeight: FontWeight.w600)),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('本地表测试'),
        actions: [
          IconButton(
            tooltip: '刷新统计',
            onPressed: _loadStats,
            icon: const Icon(Icons.refresh),
          )
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(12.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 查询输入
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _inputController,
                      decoration: const InputDecoration(
                        labelText: '输入 ArticuloID 或 CodigoBarra',
                        border: OutlineInputBorder(),
                      ),
                      onSubmitted: (_) => _runQuery(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  DropdownButton<String>(
                    value: _queryType,
                    onChanged: (v) => setState(() => _queryType = v ?? 'ArticuloID'),
                    items: const [
                      DropdownMenuItem(value: 'ArticuloID', child: Text('按货号查')),
                      DropdownMenuItem(value: 'CodigoBarra', child: Text('按条码查')),
                    ],
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: _loading ? null : _runQuery,
                    child: _loading ? const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2)) : const Text('查询'),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // 统计
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('articulos 统计', style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      _buildKeyValue('本地产品总数', _stats != null ? (_stats!['total']?.toString() ?? '0') : ''),
                      _buildKeyValue('有条码产品', _stats != null ? (_stats!['withBarcode']?.toString() ?? '0') : ''),
                      _buildKeyValue('最后同步时间', _lastSyncTime ?? '未同步'),
                    ],
                  ),
                ),
              ),

              // 查询结果
              if (_articuloRow != null || _swappedRow != null) ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('查询结果', style: TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(height: 8),
                        _buildKeyValue('ArticuloID', (_articuloRow?['ArticuloID'] ?? '').toString()),
                        _buildKeyValue('CodigoBarra', (_articuloRow?['CodigoBarra'] ?? '').toString()),
                        _buildKeyValue('NombreES', (_articuloRow?['NombreES'] ?? '').toString()),
                        const Divider(),
                        const Text('互换查询(备用)', style: TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(height: 8),
                        _buildKeyValue('ArticuloID', (_swappedRow?['ArticuloID'] ?? '').toString()),
                        _buildKeyValue('CodigoBarra', (_swappedRow?['CodigoBarra'] ?? '').toString()),
                      ],
                    ),
                  ),
                ),
              ],

              // 本地 stocktaking 最近记录
              const SizedBox(height: 12),
              Row(
                children: [
                  const Text('本地 stocktaking 最近记录', style: TextStyle(fontWeight: FontWeight.bold)),
                  const Spacer(),
                  TextButton.icon(
                    onPressed: _loadRecentStocktaking,
                    icon: const Icon(Icons.list),
                    label: const Text('加载前20条'),
                  )
                ],
              ),
              if (_recentStocktaking.isNotEmpty)
                Card(
                  child: Column(
                    children: _recentStocktaking.map((e) {
                      return ListTile(
                        dense: true,
                        title: Text('${e['Weizhi'] ?? ''}  |  ${e['ArticuloID'] ?? ''}'),
                        subtitle: Text('条码: ${e['CodigoBarra'] ?? ''}  数量: ${e['Stock'] ?? ''}  同步: ${e['isSynced'] ?? 0}'),
                        trailing: Text('${e['stdata'] ?? ''}', style: const TextStyle(color: Colors.grey)),
                      );
                    }).toList(),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
} 