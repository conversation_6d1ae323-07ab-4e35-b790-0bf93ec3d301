import 'dart:async';
import 'package:flutter/material.dart';
import 'package:mistoer/services/database_service.dart';

class PrintSettingsScreen extends StatefulWidget {
  @override
  _PrintSettingsScreenState createState() => _PrintSettingsScreenState();
}

class _PrintSettingsScreenState extends State<PrintSettingsScreen> {
  TextEditingController storeNameController = TextEditingController();
  TextEditingController productNumberController = TextEditingController();
  TextEditingController productNameController = TextEditingController();
  TextEditingController retailPriceController = TextEditingController();
  TextEditingController memberPriceController = TextEditingController();
  TextEditingController currencySymbolController = TextEditingController();
  TextEditingController beizhuController = TextEditingController(); // 备注Controller
  TextEditingController fontSizeController = TextEditingController(); // 字体大小Controller
  TextEditingController decimalPlacesController = TextEditingController(); // 小数点后几位Controller

  bool isCurrencySymbolPositionTrailing = true;
  bool _isLoading = false;
  bool isLabelMode = false; // 打印模式，标签模式=true，热敏模式=false
  bool isSmallLabel30x20Enabled = false; // 小标签30x20mm开关
  bool isSmallLabel40x20Enabled = false; // 小标签40x20mm开关
  int labelSizeMode = 3; // 标签尺寸模式: 0=默认, 1=30*20mm, 2=40*30mm, 3=58*40mm
  int label40x30Mode = 0; // 40*30mm标签内容模式: 0=标准模式, 1=简化模式(REF+价格)
  int label58x40Mode = 0; // 58*40mm标签内容模式: 0=标准模式, 1=货架模式, 2=商品模式
  bool isCurrencySymbolEditable = false; // 控制货币符号是否可以手动输入
  int printMode = 0; // 打印模式: 0=单张, 1=多张, 2=扫描自动打印
  bool isDualPrice = false; // 单价/双价标签开关，默认为单价
  bool isSinglePrice = true; // 用于选择零售价或批发价
  Timer? _increaseTimer;
  Timer? _decreaseTimer;



  List<String> currencyOptions = ['\$', '€']; // 货币符号选项

  @override
  void initState() {
    super.initState();
    
    // 直接加载数据库中保存的设置，不预设默认值
    _loadPrintSettings();
  }

  Future<void> _loadPrintSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final settings = await DatabaseService.getPrintTitles();
      final beizhu = await DatabaseService.getBeizhu(); // 加载备注字段
      final decimal_places = await DatabaseService.getDecimalPlaces(); // 加载小数点后几位

      // 打印调试信息，查看从数据库加载的实际labe1和labe2值
      print('从数据库加载的设置 - labe1: ${settings['labe1']}, labe2: ${settings['labe2']}');
      
      // 根据数据库中的值设置UI状态
      final labe1 = settings['labe1'] ?? '2'; // 默认为 '2' (单价-零售价)
      final labe2 = settings['labe2'] ?? '1';
      
      // 根据labe1和labe2的组合来设置UI状态
      bool isDualPriceValue = false;
      bool isSinglePriceValue = true;
      
      if (labe1 == '1' && labe2 == '1') {
        // 双价模式
        isDualPriceValue = true;
        isSinglePriceValue = true; // 这个值在双价模式下不重要
        print('从数据库加载的设置 - 双价模式');
      } else if (labe1 == '2' && labe2 == '1') {
        // 单价-零售价模式
        isDualPriceValue = false;
        isSinglePriceValue = true;
        print('从数据库加载的设置 - 单价模式(零售价)');
      } else if (labe1 == '1' && labe2 == '2') {
        // 单价-批发价模式
        isDualPriceValue = false;
        isSinglePriceValue = false;
        print('从数据库加载的设置 - 单价模式(批发价)');
      } else {
        // 未知组合，默认为单价-零售价模式
        isDualPriceValue = false;
        isSinglePriceValue = true;
        print('从数据库加载的设置 - 未知组合，默认设置为单价模式(零售价)');
      }
      
      setState(() {
        storeNameController.text = settings['store_name'] ?? '';
        productNumberController.text = settings['product_number'] ?? '';
        productNameController.text = settings['product_name'] ?? '';
        retailPriceController.text = settings['retail_price'] ?? '';
        memberPriceController.text = settings['member_price'] ?? '';
        currencySymbolController.text = settings['currency_symbol'] ?? '\$';
        fontSizeController.text = settings['font'] ?? '12'; // 默认字体大小
        beizhuController.text = beizhu ?? '';
        decimalPlacesController.text = decimal_places ?? '2'; // 默认保留2位小数
        isCurrencySymbolPositionTrailing = (settings['currency_symbol_position'] == '1');
        isLabelMode = (settings['print_mode'] == '2');
        
        // 加载标签尺寸设置
        final smallLabel = settings['small_labe'] ?? '3'; // 默认为 '3' (58*40mm)
        if (smallLabel == '1') {
          labelSizeMode = 1; // 30*20mm
          isSmallLabel30x20Enabled = true;
          isSmallLabel40x20Enabled = false;
        } else if (smallLabel == '2') {
          labelSizeMode = 2; // 40*30mm
          isSmallLabel30x20Enabled = false;
          isSmallLabel40x20Enabled = true;
        } else if (smallLabel == '3') {
          labelSizeMode = 3; // 58*40mm
          isSmallLabel30x20Enabled = false;
          isSmallLabel40x20Enabled = false;
        } else {
          labelSizeMode = 3; // 默认为58*40mm
          isSmallLabel30x20Enabled = false;
          isSmallLabel40x20Enabled = false;
        }
        
        // 加载40*30mm标签内容模式
        label40x30Mode = int.tryParse(settings['label_40x30_mode'] ?? '0') ?? 0;

        // 加载58*40mm标签内容模式
        label58x40Mode = int.tryParse(settings['label_58x40_mode'] ?? '0') ?? 0;



        // 加载打印模式
        final maxpage = settings['maxpage'] ?? '1';
        if (maxpage == '1') {
          printMode = 0; // 单张
        } else if (maxpage == '-1') {
          printMode = 1; // 多张
        } else if (maxpage == '2') {
          printMode = 2; // 扫描自动打印
        }
        
        // 根据数据库的值设置UI状态
        isDualPrice = isDualPriceValue;
        isSinglePrice = isSinglePriceValue;
        
        print('设置UI状态成功 - isDualPrice: $isDualPrice, isSinglePrice: $isSinglePrice');
      });
    } catch (e) {
      // 发生错误时，使用默认值
      setState(() {
        isDualPrice = false;
        isSinglePrice = true;
      });
      
      print('加载设置发生错误: $e, 使用默认值: 单价模式(零售价)');
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载失败，请检查日志以获取更多信息')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _savePrintSettings() async {
    // 检查店铺名称是否为空
    if (storeNameController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请设置店铺名称')),
      );
      return; // 如果店铺名称为空，阻止保存操作继续
    }
    setState(() {
      _isLoading = true;
    });

    // 检查字体大小的范围
    int fontSize = int.tryParse(fontSizeController.text) ?? 40;
    if (fontSize < 40) {
      fontSize = 40;
    } else if (fontSize > 70) {
      fontSize = 100;
    }

    // 检查小数点后几位数
    int decimalPlaces = int.tryParse(decimalPlacesController.text) ?? 2;
    if (decimalPlaces < 0) {
      decimalPlaces = 0;
    } else if (decimalPlaces > 4) {
      decimalPlaces = 4;
    }

    try {
      // 根据打印模式设置maxpage值
      String maxpageValue;
      if (printMode == 0) {
        maxpageValue = '1';  // 单张
      } else if (printMode == 1) {
        maxpageValue = '-1'; // 多张
      } else {
        maxpageValue = '2';  // 扫描自动打印
      }

      // 打印当前状态以便调试
      print('保存设置前的状态: isDualPrice=$isDualPrice, isSinglePrice=$isSinglePrice');
      
      // 根据UI状态确定labe1和labe2的值
      String labe1Value, labe2Value;
      
      // 使用用户在UI上选择的价格显示模式
      if (isDualPrice) {
        // 双价模式：零售价和批发价都显示
        labe1Value = '1';
        labe2Value = '1';
        print('保存用户选择的设置: 双价模式，labe1=$labe1Value, labe2=$labe2Value');
      } else if (isSinglePrice) {
        // 单价-零售价模式
        labe1Value = '2';
        labe2Value = '1';
        print('保存用户选择的设置: 单价模式(零售价)，labe1=$labe1Value, labe2=$labe2Value');
      } else {
        // 单价-批发价模式
        labe1Value = '1';
        labe2Value = '2';
        print('保存用户选择的设置: 单价模式(批发价)，labe1=$labe1Value, labe2=$labe2Value');
      }

      // 获取标签尺寸值
      String smallLabelValue = labelSizeMode.toString();
      print('保存用户选择的标签尺寸: $smallLabelValue');
      
      // 保存40*30mm标签内容模式
      print('保存用户选择的40*30mm标签内容模式: $label40x30Mode');

      final dataToSave = {
        'id': 1,
        'store_name': storeNameController.text.isNotEmpty ? storeNameController.text : null,
        'product_number': productNumberController.text.isNotEmpty ? productNumberController.text : null,
        'product_name': productNameController.text.isNotEmpty ? productNameController.text : null,
        'retail_price': retailPriceController.text.isNotEmpty ? retailPriceController.text : null,
        'member_price': memberPriceController.text.isNotEmpty ? memberPriceController.text : null,
        'currency_symbol': currencySymbolController.text.isNotEmpty ? currencySymbolController.text : null,
        'currency_symbol_position': isCurrencySymbolPositionTrailing ? 1 : -1,
        'print_mode': isLabelMode ? 2 : 0, // 保存打印模式
        'maxpage': maxpageValue, // 保存打印选项模式
        'labe1': labe1Value, // 使用用户选择的价格显示模式
        'labe2': labe2Value, // 使用用户选择的价格显示模式
        'font': fontSize.toString(),
        'small_labe': smallLabelValue, // 保存标签尺寸模式
        'label_40x30_mode': label40x30Mode.toString(), // 保存40*30mm标签内容模式
        'label_58x40_mode': label58x40Mode.toString(), // 保存58*40mm标签内容模式
        'decimal_places': decimalPlaces.toString(), // 保存小数点后几位
      };

      print('保存用户选择的58*40mm标签内容模式: $label58x40Mode');
      print('即将保存到数据库的58*40mm模式值: ${label58x40Mode.toString()}');

      print('即将保存到数据库的值: labe1=${dataToSave['labe1']}, labe2=${dataToSave['labe2']}');
      await DatabaseService.upsertPrintsRecord(dataToSave);
      print('数据已保存到数据库');

      await DatabaseService.saveBeizhu(beizhuController.text); // 保存备注
      await DatabaseService.saveDecimalPlaces(decimalPlaces.toString()); // 保存小数点后几位

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('打印设置已保存')),
      );

      Navigator.pop(context);
    } catch (e) {
      print('保存打印设置时发生错误: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('保存失败，请检查日志以获取更多信息')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _selectPriceType() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('选择单价类型'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              ListTile(
                title: Text('零售价'),
                subtitle: Text('将只在标签上显示零售价格'),
                leading: Radio(
                  value: true,
                  groupValue: isSinglePrice,
                  onChanged: (value) {
                    setState(() {
                      isSinglePrice = value as bool;
                      isDualPrice = false;
                    });
                    Navigator.of(context).pop();
                  },
                ),
                selected: isSinglePrice,
                onTap: () {
                  setState(() {
                    isSinglePrice = true;
                    isDualPrice = false;
                  });
                  Navigator.of(context).pop();
                },
              ),
              ListTile(
                title: Text('批发价'),
                subtitle: Text('将只在标签上显示批发价格'),
                leading: Radio(
                  value: false,
                  groupValue: isSinglePrice,
                  onChanged: (value) {
                    setState(() {
                      isSinglePrice = value as bool;
                      isDualPrice = false;
                    });
                    Navigator.of(context).pop();
                  },
                ),
                selected: !isSinglePrice,
                onTap: () {
                  setState(() {
                    isSinglePrice = false;
                    isDualPrice = false;
                  });
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('取消'),
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    _stopIncreasing();
    _stopDecreasing();
    super.dispose();
  }

  // 增加字体大小
  void _increaseFontSize() {
    setState(() {
      int currentSize = int.tryParse(fontSizeController.text) ?? 40;
      if (currentSize < 70) {
        fontSizeController.text = (currentSize + 1).toString();
      }
    });
  }

  // 减少字体大小
  void _decreaseFontSize() {
    setState(() {
      int currentSize = int.tryParse(fontSizeController.text) ?? 40;
      if (currentSize > 40) {
        fontSizeController.text = (currentSize - 1).toString();
      }
    });
  }

  // 开始增加字体大小
  void _startIncreasing() {
    _increaseTimer = Timer.periodic(Duration(milliseconds: 100), (timer) {
      _increaseFontSize();
    });
  }

  // 停止增加字体大小
  void _stopIncreasing() {
    if (_increaseTimer != null) {
      _increaseTimer!.cancel();
      _increaseTimer = null;
    }
  }

  // 开始减少字体大小
  void _startDecreasing() {
    _decreaseTimer = Timer.periodic(Duration(milliseconds: 100), (timer) {
      _decreaseFontSize();
    });
  }

  // 停止减少字体大小
  void _stopDecreasing() {
    if (_decreaseTimer != null) {
      _decreaseTimer!.cancel();
      _decreaseTimer = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('打印参数设置'),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : Stack(
        children: [
          Padding(
            padding: EdgeInsets.all(16.0),
            child: SingleChildScrollView(
              child: Column(
                children: <Widget>[
                  // 店铺名称和商品名称水平排列
                  Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: TextField(
                          controller: storeNameController,
                          decoration: InputDecoration(labelText: '店铺名称'),
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        flex: 1,
                        child: TextField(
                          controller: productNameController,
                          decoration: InputDecoration(labelText: '商品名称'),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10),
                  // 货号和零售价调换位置，货号和零售价水平排列
                  Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: TextField(
                          controller: productNumberController,
                          decoration: InputDecoration(labelText: '货号'),
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        flex: 1,
                        child: TextField(
                          controller: retailPriceController,
                          decoration: InputDecoration(labelText: '零售价'),
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10),
                  // 批发价和货币符号调换位置，货币符号和批发价水平排列
                  Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: GestureDetector(
                          onDoubleTap: () {
                            setState(() {
                              isCurrencySymbolEditable = true;
                            });
                          },
                          child: isCurrencySymbolEditable
                              ? TextField(
                            controller: currencySymbolController,
                            decoration: InputDecoration(labelText: '货币符号'),
                          )
                              : DropdownButtonFormField<String>(
                            value: currencySymbolController.text.isNotEmpty
                                ? currencySymbolController.text
                                : '\$',
                            decoration: InputDecoration(labelText: '货币符号'),
                            items: currencyOptions.map((String value) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Text(value),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                currencySymbolController.text = value!;
                              });
                            },
                          ),
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        flex: 1,
                        child: TextField(
                          controller: memberPriceController,
                          decoration: InputDecoration(labelText: '批发价'),
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10),
                  // 备注和小数位水平排列
                  Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: TextField(
                          controller: beizhuController,
                          decoration: InputDecoration(labelText: '备注'),
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        flex: 1,
                        child: TextField(
                          controller: decimalPlacesController,
                          decoration: InputDecoration(labelText: '价格小数位 (0-4)'),
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20),
                  // 打印模式（提示+开关在左侧），打印选项（提示+单选在右侧）
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        flex: 1,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('打印模式'),
                            Row(
                              children: [
                                Switch(
                                  value: isLabelMode,
                                  onChanged: (value) {
                                    setState(() {
                                      isLabelMode = value;
                                    });
                                  },
                                ),
                                Text(isLabelMode ? '标签启用' : '热敏启用'),
                              ],
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('打印选项'),
                            DropdownButton<int>(
                              value: printMode,
                              onChanged: (int? newValue) {
                                if (newValue != null) {
                                  setState(() {
                                    printMode = newValue;
                                  });
                                }
                              },
                              items: [
                                DropdownMenuItem<int>(
                                  value: 0,
                                  child: Text('单张打印'),
                                ),
                                DropdownMenuItem<int>(
                                  value: 1,
                                  child: Text('多张打印'),
                                ),
                                DropdownMenuItem<int>(
                                  value: 2,
                                  child: Text('扫描自动打印'),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20),
                  // 货币符号位置和单价/双价（左右排列，提示+开关）
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        flex: 1,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('货币符号位置'),
                            Row(
                              children: [
                                Switch(
                                  value: isCurrencySymbolPositionTrailing,
                                  onChanged: (value) {
                                    setState(() {
                                      isCurrencySymbolPositionTrailing = value;
                                    });
                                  },
                                ),
                                Text(isCurrencySymbolPositionTrailing ? '前置' : '后置'),
                              ],
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('单价/双价'),
                                if (!isDualPrice)
                                  Text(
                                    isSinglePrice ? '(零售价)' : '(批发价)', 
                                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                                  ),
                              ],
                            ),
                            Row(
                              children: [
                                Switch(
                                  value: isDualPrice,
                                  onChanged: (value) {
                                    setState(() {
                                      isDualPrice = value;
                                      print('双价开关状态改变: isDualPrice=$isDualPrice');
                                      
                                      // 强制确保从双价切换到单价时使用零售价
                                      if (!value) {
                                        isSinglePrice = true;
                                        print('切换到单价模式，设置为零售价: isSinglePrice=$isSinglePrice');
                                      } else {
                                        print('切换到双价模式');
                                      }
                                    });
                                  },
                                ),
                                Text(isDualPrice ? '双价' : '单价'),
                                if (!isDualPrice)
                                  TextButton(
                                    onPressed: _selectPriceType,
                                    child: Text('选择'),
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.symmetric(horizontal: 8),
                                      minimumSize: Size(50, 30),
                                    ),
                                  ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20),
                  // 标签尺寸选择（轮播样式）
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('标签尺寸选择', style: TextStyle(fontWeight: FontWeight.bold)),
                      SizedBox(height: 8),
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: _buildLabelSizeOption(1, '30*20mm'),
                            ),
                            Expanded(
                              child: _buildLabelSizeOption(2, '40*30mm'),
                            ),
                            Expanded(
                              child: _buildLabelSizeOption(3, '58*40mm'),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 8),
                      // 仅当40*30mm标签被选中时显示内容模式选择
                      if (labelSizeMode == 2)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('40*30mm标签内容模式:', style: TextStyle(fontWeight: FontWeight.bold)),
                            SizedBox(height: 8),
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade300),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Column(
                                children: [
                                  _build40x30ModeOption(0, '标准模式', '显示店名、商品名称、价格、备注和条码'),
                                  Divider(height: 1, thickness: 1),
                                  _build40x30ModeOption(1, '简化模式', '仅显示REF:货号和价格两行'),
                                ],
                              ),
                            ),
                          ],
                        ),
                      // 仅当58*40mm标签被选中时显示内容模式选择
                      if (labelSizeMode == 3)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('58*40mm标签内容模式:', style: TextStyle(fontWeight: FontWeight.bold)),
                            SizedBox(height: 8),
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade300),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Column(
                                children: [
                                  _build58x40ModeOption(0, '标准模式', '显示店名、商品名称、价格、备注和条码'),
                                  Divider(height: 1, thickness: 1),
                                  _build58x40ModeOption(1, '货架模式', '仅显示条码和货号'),
                                  Divider(height: 1, thickness: 1),
                                  _build58x40ModeOption(2, '商品模式', '显示商品名称、货号和条码'),
                                ],
                              ),
                            ),
                          ],
                        ),
                      // 仅当30*20mm标签被选中时显示字体大小设置
                      if (labelSizeMode == 1)
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text('字体大小: '),
                            GestureDetector(
                              onLongPress: _startDecreasing,
                              onLongPressEnd: (_) => _stopDecreasing(),
                              child: IconButton(
                                icon: Icon(Icons.arrow_left),
                                onPressed: _decreaseFontSize,
                              ),
                            ),
                            SizedBox(
                              width: 50,
                              child: TextField(
                                controller: fontSizeController,
                                textAlign: TextAlign.center,
                                decoration: InputDecoration(
                                  contentPadding: EdgeInsets.symmetric(vertical: 8),
                                  isDense: true,
                                ),
                                keyboardType: TextInputType.number,
                              ),
                            ),
                            GestureDetector(
                              onLongPress: _startIncreasing,
                              onLongPressEnd: (_) => _stopIncreasing(),
                              child: IconButton(
                                icon: Icon(Icons.arrow_right),
                                onPressed: _increaseFontSize,
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),

                  SizedBox(height: 100), // 增加页面高度
                ],
              ),
            ),
          ),
          // 悬浮按钮，位于屏幕底部
          Positioned(
            bottom: 20,
            left: 20,
            right: 20,
            child: ElevatedButton(
              onPressed: _savePrintSettings,
              child: Text('保存'),
              style: ElevatedButton.styleFrom(
                minimumSize: Size(double.infinity, 50), // 使按钮占据整个宽度
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建标签尺寸选项
  Widget _buildLabelSizeOption(int mode, String label) {
    bool isSelected = labelSizeMode == mode;
    return GestureDetector(
      onTap: () {
        setState(() {
          labelSizeMode = mode;
          
          // 更新开关状态（为了兼容旧代码）
          if (mode == 1) {
            isSmallLabel30x20Enabled = true;
            isSmallLabel40x20Enabled = false;
          } else if (mode == 2) {
            isSmallLabel30x20Enabled = false;
            isSmallLabel40x20Enabled = true;
          } else {
            isSmallLabel30x20Enabled = false;
            isSmallLabel40x20Enabled = false;
          }
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue.shade100 : Colors.transparent,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? Colors.blue.shade900 : Colors.black,
              ),
            ),
            SizedBox(height: 4),
            Icon(
              isSelected ? Icons.check_circle : Icons.circle_outlined,
              color: isSelected ? Colors.blue : Colors.grey,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  // 构建40*30mm标签内容模式选项
  Widget _build40x30ModeOption(int mode, String title, String subtitle) {
    bool isSelected = label40x30Mode == mode;
    return InkWell(
      onTap: () {
        setState(() {
          label40x30Mode = mode;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        child: Row(
          children: [
            Radio<int>(
              value: mode,
              groupValue: label40x30Mode,
              onChanged: (int? value) {
                if (value != null) {
                  setState(() {
                    label40x30Mode = value;
                  });
                }
              },
              activeColor: Colors.blue,
            ),
            SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      color: isSelected ? Colors.blue.shade900 : Colors.black,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建58*40mm标签内容模式选项
  Widget _build58x40ModeOption(int mode, String title, String subtitle) {
    bool isSelected = label58x40Mode == mode;
    return InkWell(
      onTap: () {
        setState(() {
          label58x40Mode = mode;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        child: Row(
          children: [
            Radio<int>(
              value: mode,
              groupValue: label58x40Mode,
              onChanged: (int? value) {
                if (value != null) {
                  setState(() {
                    label58x40Mode = value;
                  });
                }
              },
              activeColor: Colors.blue,
            ),
            SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      color: isSelected ? Colors.blue.shade900 : Colors.black,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
