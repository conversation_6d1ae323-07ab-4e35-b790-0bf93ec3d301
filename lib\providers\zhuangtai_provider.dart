import 'package:flutter/material.dart';
import 'package:mistoer/models/order_detail.dart';
import 'package:mistoer/services/database_service.dart';

class ZhuangtaiProvider with ChangeNotifier {
  List<OrderDetail> _orderDetails = [];

  List<OrderDetail> get orderDetails => _orderDetails;

  void loadOrderDetails(List<OrderDetail> orderDetails) {
    _orderDetails = orderDetails;
    notifyListeners();
  }

  void updateScan(int id, int newScan) async {
    int index = _orderDetails.indexWhere((orderDetail) => orderDetail.id == id);
    if (index != -1) {
      _orderDetails[index] = _orderDetails[index].copyWith(scan: newScan);
      notifyListeners();
      await DatabaseService.updateOrderDetailScan(_orderDetails[index]);
    }
  }

  void updateBaozhuangshu(int id, int newBaozhuangshu) async {
    int index = _orderDetails.indexWhere((orderDetail) => orderDetail.id == id);
    if (index != -1) {
      _orderDetails[index] = _orderDetails[index].copyWith(baozhuangshu: newBaozhuangshu);
      notifyListeners();
      await DatabaseService.updateOrderDetailBaozhuangshu(id, newBaozhuangshu);
    }
  }
}
