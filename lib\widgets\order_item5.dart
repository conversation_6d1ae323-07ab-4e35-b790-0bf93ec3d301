import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mistoer/models/order.dart';

class OrderItem5 extends StatelessWidget {
  final Order order;
  final VoidCallback onTap;
  final VoidCallback onGenerateReport;
  final VoidCallback onReview;

  OrderItem5({
    required this.order,
    required this.onTap,
    required this.onGenerateReport,
    required this.onReview,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(order.name),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('订单编号: ${order.pedidoKey}'),
          Text('日期: ${order.riqi}'),
          Text('金额: ${order.amount}'),
          if (order.note.isNotEmpty) Text('备注: ${order.note}'),
          if (order.beizhu.isNotEmpty) Text('备注2: ${order.beizhu}'),
          Text('完成率: ${(order.fulfillmentRate * 100).toStringAsFixed(1)}%'),
        ],
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            icon: Icon(Icons.visibility),
            onPressed: onTap,
            tooltip: '查看详情',
          ),
          IconButton(
            icon: Icon(Icons.receipt),
            onPressed: onGenerateReport,
            tooltip: '生成出库单',
          ),
          IconButton(
            icon: Icon(Icons.check_circle),
            onPressed: onReview,
            tooltip: '复核',
          ),
        ],
      ),
    );
  }
}
