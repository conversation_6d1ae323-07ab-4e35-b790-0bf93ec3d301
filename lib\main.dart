import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'screens/home_screen.dart';
import 'screens/ip_input_screen.dart';
import 'screens/login_screen.dart';
import 'screens/order_screen_zhuangtai1.dart';
import 'screens/order_screen_zhuangtai2.dart';
import 'screens/scan_inventory_page.dart';
import 'screens/scan_stocking_page.dart';
import 'package:mistoer/print/print_label_screen.dart';
import 'screens/order_details_screen_purge.dart';
import 'services/database_service_sn.dart'; // 确保正确引入
import 'package:mistoer/services/sn_service.dart'; // 确保正确引入
import 'providers/zhuangtai_provider.dart';
import 'providers/stocking_data.dart';
import 'providers/settings_provider.dart';
import 'package:mistoer/print/goods_check_page.dart';
import 'package:mistoer/services/offline_manager.dart';

final RouteObserver<PageRoute> routeObserver = RouteObserver<PageRoute>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await OfflineManager.instance.start();
  await DatabaseServiceSN.init(); // 初始化本地数据库
  String initialRoute = await _getInitialRoute(); // 获取初始路由
  runApp(MyApp(initialRoute: initialRoute));
}

Future<String> _getInitialRoute() async {
  // 获取本地注册信息
  String? localSerial = await DatabaseServiceSN.getSerialNumber();
  String? localRegNumber = await DatabaseServiceSN.getRegNumber();

  // 如果本地没有序列号或注册码，跳转到 IP 输入界面
  if (localSerial == null || localRegNumber == null) {
    return '/ip_input';
  }

  try {
    // 向服务器检查注册信息
    Map<String, dynamic> serverData = await SNservice.checkSerial(localSerial);

    if (serverData.containsKey('error')) {
      // 如果服务器没有找到注册信息，重新注册
      return '/ip_input';
    } else {
      // 服务器返回注册信息，验证注册码
      bool isValid = SNservice.validateRegNumber(localSerial, localRegNumber);
      if (isValid) {
        // 如果验证成功，将服务器数据保存到本地并跳转到登录界面
        await SNservice.saveToDatabase(localSerial, serverData);
        return '/login';
      } else {
        // 如果验证失败，跳转到 IP 输入界面进行重新注册
        return '/ip_input';
      }
    }
  } catch (e) {
    print('注册信息检查时出现错误: $e');
    return '/ip_input';  // 遇到任何问题都跳转到 IP 输入界面
  }
}


class MyApp extends StatelessWidget {
  final String initialRoute;

  MyApp({required this.initialRoute});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ZhuangtaiProvider()),
        ChangeNotifierProvider(create: (_) => StockingData()),
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
      ],
      child: MaterialApp(
        title: 'WKTECH',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          scaffoldBackgroundColor: Colors.white,
          appBarTheme: AppBarTheme(
            color: Colors.white,
            elevation: 4.0,
            shadowColor: Colors.grey,
            iconTheme: IconThemeData(color: Colors.black),
            titleTextStyle: TextStyle(color: Colors.black, fontSize: 20),
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              minimumSize: Size(10, 60),
            ),
          ),
        ),
        navigatorObservers: [routeObserver],
        initialRoute: initialRoute,
        routes: {
          '/ip_input': (context) => IpInputScreen(),
          '/login': (context) => LoginScreen(),
          '/home': (context) => HomeScreen(),
          '/order_zhuangtai1': (context) => OrderScreenZhuangtai1(zhuangtai: 1),
          '/order_zhuangtai2': (context) => OrderScreenZhuangtai2(zhuangtai: 2),
          '/scan_inventory': (context) => ScanInventoryPage(),
          '/scan_stocking': (context) => ScanStockingPage(),
          '/print_label': (context) => PrintLabelScreen(articuloID: ''),
          '/order_details_purge': (context) => OrderDetailsScreenPurge(),
          '/goods_check_page': (context) => GoodsCheckPage(),
        },
      ),
    );
  }
}
