import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseServiceSN {
  static Database? _database;

  // 获取数据库实例
  static Future<Database> get database async {
    if (_database != null) return _database!;
    await init();
    return _database!;
  }

  // 初始化数据库
  static Future<void> init() async {
    if (_database != null) return;

    try {
      _database = await openDatabase(
        join(await getDatabasesPath(), 'servicesn.db'),
        version: 3, // 将版本号增加到 3
        onCreate: (db, version) async {
          await db.execute('''
            CREATE TABLE IF NOT EXISTS servicereg (
              id INTEGER PRIMARY KEY,
              SN TEXT,
              regnumber TEXT,
              corporatename TEXT,
              ok INTEGER DEFAULT 0,
              mayorista INTEGER DEFAULT 0,
              minorista INTEGER DEFAULT 0,
              esPrueba INTEGER DEFAULT 0,
              tiempoPrueba TEXT,
              shopid TEXT,
              bohao INTEGER DEFAULT 0 
            )
          ''');
        },
        onUpgrade: (db, oldVersion, newVersion) async {
          if (oldVersion < 2) {
            // 增加 2 版本中的新列
            await db.execute('ALTER TABLE servicereg ADD COLUMN mayorista INTEGER DEFAULT 0');
            await db.execute('ALTER TABLE servicereg ADD COLUMN minorista INTEGER DEFAULT 0');
            await db.execute('ALTER TABLE servicereg ADD COLUMN esPrueba INTEGER DEFAULT 0');
            await db.execute('ALTER TABLE servicereg ADD COLUMN tiempoPrueba TEXT');
            await db.execute('ALTER TABLE servicereg ADD COLUMN shopid INTEGER DEFAULT 0');
          }
          if (oldVersion < 3) {
            // 增加 3 版本中的 bohao 字段
            await db.execute('ALTER TABLE servicereg ADD COLUMN bohao INTEGER DEFAULT 0');
          }
        },
      );
    } catch (e) {
      print('Error initializing database: $e');
    }
  }

  // 插入或更新 servicereg 表中的记录，只保持 ID 为 1 的记录
  static Future<void> upsertServiceRegRecord({
    required String sn,
    required String regnumber,
    required String corporatename,
    required String shopid,
    required bool isOk,
    int bohao = 0,
    int mayorista = 0,
    int minorista = 0,
    int esPrueba = 0,
    String? tiempoPrueba,
  }) async {
    final db = await database;
    try {
      // 先删除所有其他记录，确保只有 ID=1 的记录存在
      await db.delete('servicereg');

      // 插入新的记录，并将其 ID 强制设为 1
      await db.insert(
        'servicereg',
        {
          'id': 1, // 强制使用 ID 为 1
          'SN': sn,
          'regnumber': regnumber,
          'corporatename': corporatename,
          'ok': isOk ? 1 : 0,
          'mayorista': mayorista,
          'minorista': minorista,
          'esPrueba': esPrueba,
          'tiempoPrueba': tiempoPrueba,
          'shopid': shopid,
          'bohao': bohao, // 添加 bohao 字段
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      print('Record upserted successfully: SN=$sn, corporatename=$corporatename, regnumber=$regnumber');

      // 查询插入的记录，检查是否插入成功
      final result = await getLocalRegistrationData(sn);
      if (result != null) {
        print('插入的数据: SN=${result['serial']}, 注册码=${result['regnumber']}, mayorista=${result['mayorista']}, minorista=${result['minorista']}, esPrueba=${result['esPrueba']}, tiempoPrueba=${result['tiempoPrueba']}, shopid=${result['shopid']}, bohao=${result['bohao']}');
      } else {
        print('未能找到插入的数据');
      }
    } catch (e) {
      print('Error upserting servicereg record: $e');
    }
  }

  // 获取 servicereg 表中的 SN 和 regnumber
  static Future<Map<String, dynamic>?> getLocalRegistrationData(String deviceSerial) async {
    final db = await database;
    try {
      // 查询 servicereg 表中的 SN 和 regnumber
      List<Map<String, dynamic>> result = await db.query(
        'servicereg',
        where: 'id = 1', // 只查询 ID 为 1 的记录
      );

      // 如果有结果，返回 serial 和 regnumber
      if (result.isNotEmpty) {
        return {
          'serial': result.first['SN'],
          'regnumber': result.first['regnumber'],
          'corporatename': result.first['corporatename'],
          'ok': result.first['ok'],
          'mayorista': result.first['mayorista'],
          'minorista': result.first['minorista'],
          'esPrueba': result.first['esPrueba'],
          'tiempoPrueba': result.first['tiempoPrueba'],
          'shopid': result.first['shopid'],
          'bohao': result.first['bohao'], // 返回 bohao 字段
        };
      } else {
        // 没有找到匹配记录，返回 null
        return null;
      }
    } catch (e) {
      print('Error getting local registration data: $e');
      return null;
    }
  }

  // 获取表中所有记录（用于调试）
  static Future<List<Map<String, dynamic>>> getAllRecords() async {
    final db = await database;
    try {
      return await db.query('servicereg');
    } catch (e) {
      print('Error getting all records: $e');
      return [];
    }
  }

  // 删除表中的所有记录（调试使用）
  static Future<void> deleteAllRecords() async {
    final db = await database;
    try {
      await db.delete('servicereg');
      print('All records deleted successfully');
    } catch (e) {
      print('Error deleting all records: $e');
    }
  }

  // 更新注册状态
  static Future<void> updateRegistrationStatus(String sn, bool status) async {
    final db = await database;
    try {
      await db.update(
        'servicereg',
        {'ok': status ? 1 : 0},
        where: 'SN = ?',
        whereArgs: [sn],
      );
      print('Registration status updated for SN: $sn');
    } catch (e) {
      print('Error updating registration status: $e');
    }
  }

  // 查询设备的注册状态
  static Future<int> getRegistrationStatus(String sn) async {
    final db = await database;
    try {
      List<Map<String, dynamic>> result = await db.query(
        'servicereg',
        where: 'SN = ?',
        whereArgs: [sn],
      );

      if (result.isNotEmpty) {
        return result.first['ok'] as int;
      } else {
        return 0; // 默认为未注册状态
      }
    } catch (e) {
      print('Error getting registration status: $e');
      return 0;
    }
  }

  // 查询表中是否有记录
  static Future<bool> hasRecords() async {
    final db = await database;
    try {
      List<Map<String, dynamic>> result = await db.query('servicereg');
      return result.isNotEmpty;
    } catch (e) {
      print('Error checking if records exist: $e');
      return false;
    }
  }

  // 获取本地序列号
  static Future<String?> getSerialNumber() async {
    final db = await database;
    final result = await db.query('servicereg', limit: 1);
    if (result.isNotEmpty) {
      return result.first['SN'] as String?;
    }
    return null;
  }

  // 获取本地注册码
  static Future<String?> getRegNumber() async {
    final db = await database;
    final result = await db.query('servicereg', limit: 1);
    if (result.isNotEmpty) {
      return result.first['regnumber'] as String?;
    }
    return null;
  }

  // 获取本地 shopid
  static Future<String?> getShopId() async {
    final db = await database;
    final result = await db.query('servicereg', limit: 1);
    if (result.isNotEmpty) {
      return result.first['shopid'] as String?;
    }
    return null;
  }
  // 获取 bohao 值的方法
  static Future<int?> getBohaoValue() async {
    final db = await database; // 假���你已经定义了 database 方法
    try {
      List<Map<String, dynamic>> result = await db.query(
        'servicereg',
        columns: ['bohao'],  // 只查询 bohao 列
        where: 'id = ?',     // 只查询 ID 为 1 的记录
        whereArgs: [1],
        limit: 1,  // 限制结果为1条，提高效率
      );

      // 检查查询结果
      if (result.isNotEmpty && result.first['bohao'] != null) {
        print('Bohao value fetched: ${result.first['bohao']}'); // 打印获取的 bohao 值
        return result.first['bohao'] as int?; // 返回 bohao 的值
      } else {
        print('Bohao value not found or is null'); // 如果没有记录或 bohao 是 null
        return null; // 返回 null
      }
    } catch (e) {
      print('Error getting bohao value: $e'); // 输出错误日志
      return null; // 处理错误时返回 null
    }
  }

  static Future<void> deleteServiceRegRecord(String serial) async {
    final db = await database;  // 获取数据库实例

    try {
      // 删除 servicereg 表中匹配指定序列号的记录
      await db.delete(
        'servicereg',
        where: 'sn = ?',
        whereArgs: [serial],
      );
      print('删除成功: SN=$serial 的记录');
    } catch (e) {
      print('删除记录时发生错误: $e');
    }
  }

  // 获取本地 corporatename
  static Future<String?> getCorporateName() async {
    final db = await database;
    final result = await db.query('servicereg', columns: ['corporatename'], limit: 1);
    if (result.isNotEmpty) {
      return result.first['corporatename'] as String?;
    }
    return null;
  }
  // 保存beizhu
  static Future<void> saveBeizhu(String beizhu) async {
    final db = await database;
    await db.update('prints', {'beizhu': beizhu}, where: 'id = ?', whereArgs: [1]);
  }

  // 清空所有注册相关数据
  static Future<void> clearRegistrationData() async {
    final db = await database;
    try {
      await db.delete('servicereg');
      print('已清空所有注册数据');
    } catch (e) {
      print('清空注册数据时出错: $e');
    }
  }

  // 只清空注册码相关信息，保留其他数据
  static Future<void> clearRegistrationCode(String sn) async {
    final db = await database;
    try {
      await db.update(
        'servicereg',
        {
          'regnumber': null,
          'ok': 0,
        },
        where: 'SN = ?',
        whereArgs: [sn],
      );
      print('已清空注册码信息');
    } catch (e) {
      print('清空注册码时出错: $e');
    }
  }
}
