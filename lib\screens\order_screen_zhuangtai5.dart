import 'package:flutter/material.dart';
import 'package:mistoer/models/order.dart';
import 'package:mistoer/models/order_detail.dart';
import 'package:mistoer/services/api_service.dart';
import 'package:mistoer/services/database_service.dart';
import 'package:mistoer/screens/order_detail_screen_zhuangtai1.dart';
import 'package:mistoer/widgets/order_item5.dart';
import 'package:mistoer/screens/order_config_screen.dart';
import 'package:mistoer/main.dart';

class OrderScreenZhuangtai5 extends StatefulWidget {
  final int zhuangtai;

  OrderScreenZhuangtai5({required this.zhuangtai});

  @override
  _OrderScreenZhuangtai5State createState() => _OrderScreenZhuangtai5State();
}

class _OrderScreenZhuangtai5State extends State<OrderScreenZhuangtai5> with RouteAware {
  List<Order> _orders = [];
  List<Order> _filteredOrders = [];
  bool _isLoading = false;
  double _progress = 0.0;
  TextEditingController _searchController = TextEditingController();
  bool _isSearchVisible = false;

  @override
  void initState() {
    super.initState();
    _fetchOrders();
    _searchController.addListener(_filterOrders);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final ModalRoute? modalRoute = ModalRoute.of(context);
    if (modalRoute is PageRoute) {
      routeObserver.subscribe(this, modalRoute);
    }
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    _searchController.dispose();
    super.dispose();
  }

  @override
  void didPopNext() {
    _fetchOrders();
  }

  Future<void> _fetchOrders() async {
    setState(() {
      _isLoading = true;
      _progress = 0.0;
    });

    try {
      List<Order> orders = await ApiService.fetchOrders(zhuangtai: widget.zhuangtai);
      for (var i = 0; i < orders.length; i++) {
        double rate = await DatabaseService.getOrderFulfillmentRate(orders[i].pedidoKey);
        orders[i] = orders[i].copyWith(fulfillmentRate: rate);
        setState(() {
          _progress = (i + 1) / orders.length;
        });
      }
      if (!mounted) return;
      setState(() {
        _orders = orders;
        _filteredOrders = orders;
        _isLoading = false;
      });
    } catch (e) {
      print("Error fetching orders: $e");
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterOrders() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredOrders = _orders.where((order) {
        return order.name.toLowerCase().contains(query) ||
            order.pedidoKey.toLowerCase().contains(query) ||
            order.riqi.toLowerCase().contains(query) ||
            order.amount.toString().contains(query) ||
            order.note.toLowerCase().contains(query) ||
            order.beizhu.toLowerCase().contains(query);
      }).toList();
    });
  }

  Future<void> _confirmAndSyncOrderDetails(Order order) async {
    setState(() {
      _isLoading = true;
      _progress = 0.0;
    });

    try {
      // 检查本地数据库是否已有订单详情
      int localLineCount = await DatabaseService.getLocalOrderLineCount(order.pedidoKey);
      if (localLineCount == 0) {
        // 本地没有数据，从服务器下载订单详情
        List<OrderDetail> orderDetails = await ApiService.fetchOrderDetails(order.pedidoKey);
        for (var i = 0; i < orderDetails.length; i++) {
          await DatabaseService.insertOrderDetail(orderDetails[i]);
          setState(() {
            _progress = (i + 1) / orderDetails.length;
          });
        }
        localLineCount = orderDetails.length; // 更新本地行数
      }

      // 获取在线订单行数
      int onlineLineCount = await ApiService.fetchOrderLineCount(order.pedidoKey);

      if (onlineLineCount == localLineCount) {
        // 行数一致，打开订单详情页面
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => OrderDetailScreenZhuangtai1(order: order),
          ),
        ).then((value) {
          if (!mounted) return;
          _fetchOrders(); // 从详情页面返回后刷新订单列表
        });
      } else {
        // 行数不一致，提示用户
        _showMismatchDialog(order.pedidoKey, onlineLineCount, localLineCount);
      }
    } catch (e) {
      print("Error syncing order details: $e");
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showMismatchDialog(String pedidoKey, int onlineCount, int localCount) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('订单行数不匹配'),
          content: Text(
              '订单 $pedidoKey 的行数不匹配。\n线上行数: $onlineCount\n本地行数: $localCount'),
          actions: <Widget>[
            TextButton(
              child: Text('确定'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  Future<void> _generateReport(Order order) async {
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(
        builder: (context) => OrderConfigScreen(order: order),
      ),
      ModalRoute.withName('/home'),
    ).then((value) {
      if (!mounted) return;
      _fetchOrders();
    });
  }

  Future<void> _reviewOrder(Order order) async {
    try {
      // 更新在线数据库中的订单状态
      await ApiService.updateOrderStatus(order.pedidoKey, 3);
      _fetchOrders();
    } catch (e) {
      print("Error updating order status: $e");
    }
  }

  void _toggleSearch() {
    setState(() {
      _isSearchVisible = !_isSearchVisible;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('订单复查'),
        actions: [
          IconButton(
            icon: Icon(Icons.search),
            onPressed: _toggleSearch,
          ),
          IconButton(
            icon: Icon(Icons.sync),
            onPressed: _fetchOrders,
          ),
          IconButton(
            icon: Icon(Icons.settings),
            onPressed: () {
              Navigator.pushNamed(context, '/settings');
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          Column(
            children: [
              if (_isSearchVisible)
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      labelText: '搜索',
                      hintText: '输入公司名称、订单编号、日期、金额或备注',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(25.0)),
                      ),
                    ),
                  ),
                ),
              Expanded(
                child: _filteredOrders.isEmpty
                    ? Center(child: Text('没有数据'))
                    : ListView.builder(
                  itemCount: _filteredOrders.length,
                  itemBuilder: (context, index) {
                    var order = _filteredOrders[index];
                    return Card(
                      color: Colors.white,
                      margin: EdgeInsets.all(8.0),
                      elevation: 2.0,
                      child: OrderItem5(
                        order: order,
                        onTap: () => _confirmAndSyncOrderDetails(order),
                        onGenerateReport: () => _generateReport(order),
                        onReview: () => _reviewOrder(order),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
          if (_isLoading)
            Center(
              child: Container(
                width: 200,
                padding: EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.8),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 20),
                    LinearProgressIndicator(value: _progress),
                    SizedBox(height: 20),
                    Text(
                      '${(_progress * 100).toStringAsFixed(0)}%',
                      style: TextStyle(color: Colors.white),
                    ),
                    SizedBox(height: 20),
                    Text(
                      '加载中，请稍等...',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}
