import 'package:flutter/material.dart';
import 'package:mistoer/models/order.dart';
import 'package:mistoer/models/order_detail.dart';
import 'package:mistoer/services/api_service.dart';
import 'package:mistoer/services/database_service.dart';
import 'package:mistoer/widgets/order_item2.dart';
import 'package:mistoer/screens/order_detail_screen_zhuangtai2.dart';
import 'package:mistoer/screens/order_config_screen.dart';
import 'package:mistoer/main.dart';

class OrderScreenZhuangtai2 extends StatefulWidget {
  final int zhuangtai;

  OrderScreenZhuangtai2({required this.zhuangtai});

  @override
  _OrderScreenZhuangtai2State createState() => _OrderScreenZhuangtai2State();
}

class _OrderScreenZhuangtai2State extends State<OrderScreenZhuangtai2> with RouteAware {
  List<Order> _orders = [];
  List<Order> _filteredOrders = [];
  TextEditingController _searchController = TextEditingController();
  bool _isSearchVisible = false;
  int _userRevision = 0; // 用户的 revision 值

  @override
  void initState() {
    super.initState();
    _fetchOrders();
    _searchController.addListener(_filterOrders);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final ModalRoute? modalRoute = ModalRoute.of(context);
    if (modalRoute is PageRoute) {
      routeObserver.subscribe(this, modalRoute);
    }
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    _searchController.dispose();
    super.dispose();
  }

  @override
  void didPopNext() {
    _fetchOrders();
  }

  Future<void> _fetchOrders() async {
    try {
      _userRevision = await DatabaseService.getCurrentUserRevision(); // 获取当前用户的 revision 值
      List<Order> orders = await ApiService.fetchOrders(zhuangtai: widget.zhuangtai);
      List<Order> validOrders = [];

      for (var order in orders) {
        List<OrderDetail> localDetails = await DatabaseService.getOrderDetails(order.pedidoKey);
        if (localDetails.isNotEmpty) {
          double rate = await DatabaseService.getOrderFulfillmentRate(order.pedidoKey);
          order = order.copyWith(fulfillmentRate: rate);
          validOrders.add(order);
        }
      }

      if (!mounted) return;
      setState(() {
        _orders = validOrders;
        _filteredOrders = validOrders;
      });
    } catch (e) {
      print("Error fetching orders: $e");
    }
  }

  void _filterOrders() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredOrders = _orders.where((order) {
        return order.name.toLowerCase().contains(query) ||
            order.pedidoKey.toLowerCase().contains(query) ||
            order.riqi.toLowerCase().contains(query) ||
            order.amount.toString().contains(query) ||
            order.beizhu.toLowerCase().contains(query);
      }).toList();
    });
  }

  Future<void> _confirmAndSyncOrderDetails(Order order) async {
    try {
      List<OrderDetail> orderDetails = await DatabaseService.getOrderDetails(order.pedidoKey);
      if (orderDetails.isNotEmpty) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => OrderDetailScreenZhuangtai2(order: order),
          ),
        ).then((value) => _fetchOrders());
      } else {
        _showNoDetailsDialog();
      }
    } catch (e) {
      print("Error fetching local order details: $e");
    }
  }

  Future<void> _showNoDetailsDialog() async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('提示'),
          content: Text('本地数据库中没有找到该订单的详情'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _fetchOrders();
              },
              child: Text('确定'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _generateReport(Order order) async {
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(
        builder: (context) => OrderConfigScreen(order: order),
      ),
      ModalRoute.withName('/home'),
    ).then((value) => _fetchOrders());
  }

  Future<void> updateOrderStatus(Order order) async {
    try {
      await ApiService.updateOrderStatus(order.pedidoKey, 5);
      _fetchOrders();
    } catch (e) {
      print("Error updating order status: $e");
    }
  }

  void _toggleSearch() {
    setState(() {
      _isSearchVisible = !_isSearchVisible;
    });
  }

  Future<void> _handleOrderReview(Order order) async {
    try {
      // 调用 API 更新 verificacion 字段
      await ApiService.verifyOrder(order.pedidoKey);
      // 执行复核操作
      _fetchOrders(); // Refresh orders list after review
    } catch (e) {
      print("Error during order review: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
        return false;
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text('订单出库'),
          actions: [
            IconButton(
              icon: Icon(_isSearchVisible ? Icons.search_off : Icons.search),
              onPressed: _toggleSearch,
            ),
            IconButton(
              icon: Icon(Icons.sync),
              onPressed: _fetchOrders,
            ),
          ],
        ),
        body: Column(
          children: [
            if (_isSearchVisible)
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    labelText: '搜索',
                    hintText: '输入公司名称、订单编号、日期、金额或备注',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(25.0)),
                    ),
                  ),
                ),
              ),
            Expanded(
              child: Container(
                color: Colors.white,
                child: _filteredOrders.isEmpty
                    ? Center(child: Text('没有数据'))
                    : ListView.builder(
                  itemCount: _filteredOrders.length,
                  itemBuilder: (context, index) {
                    var order = _filteredOrders[index];
                    return OrderItem2(
                      order: order,
                      onTap: () => _confirmAndSyncOrderDetails(order),
                      onGenerateReport: () => _generateReport(order),
                      onReview: () => _handleOrderReview(order),
                      userRevision: _userRevision,
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
