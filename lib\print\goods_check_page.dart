import 'package:flutter/material.dart';
import 'package:mistoer/print/api_print_service.dart';
import 'package:mistoer/services/database_service.dart';
import 'package:mistoer/print/albaran_detail_page.dart';
import 'package:mistoer/print/order_detail_print.dart';
import 'albaran_compare_page.dart';  // 导入新页面

class GoodsCheckPage extends StatefulWidget {
  @override
  _GoodsCheckPageState createState() => _GoodsCheckPageState();
}

class _GoodsCheckPageState extends State<GoodsCheckPage> {
  List<Map<String, dynamic>> _data = [];
  List<Map<String, dynamic>> _filteredData = []; // 过滤后的数据
  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  ScrollController _scrollController = ScrollController(); // 创建 ScrollController
  TextEditingController _searchController = TextEditingController(); // 搜索控制器
  bool _isSearching = false; // 控制搜索框是否显示

  @override
  void initState() {
    super.initState();
    _fetchData();
    _searchController.addListener(_filterData); // 添加搜索监听
  }

  @override
  void dispose() {
    _scrollController.dispose(); // 释放 ScrollController
    _searchController.removeListener(_filterData); 
    _searchController.dispose(); // 释放搜索控制器
    super.dispose();
  }

  void _filterData() {
    final query = _searchController.text.toLowerCase();
    
    if (query.isEmpty) {
      setState(() {
        _filteredData = List.from(_data); // 查询为空时显示所有数据
      });
      return;
    }

    setState(() {
      _filteredData = _data.where((item) {
        final albaranNo = item['AlbaranProveedorNo'].toString().toLowerCase();
        final date = item['Fecha'].toString().toLowerCase();
        final total = item['Total'].toString().toLowerCase();
        
        // 模糊匹配多个字段
        return albaranNo.contains(query) || 
               date.contains(query) || 
               total.contains(query);
      }).toList();
    });
  }

  void _toggleSearch() {
    setState(() {
      _isSearching = !_isSearching;
      if (!_isSearching) {
        _clearSearch();
      }
    });
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _filteredData = List.from(_data);
    });
  }

  Future<void> _fetchData() async {
    try {
      final List<Map<String, dynamic>>? data = await ApiPrintService.fetchUncompletedOrders();
      if (data != null && data.isNotEmpty) {
        setState(() {
          _data = data;
          _filteredData = List.from(data); // 初始化过滤后的数据
        });
      } else {
        setState(() {
          _data = [];
          _filteredData = [];
        });
      }
    } catch (e) {
      setState(() {
        _data = [];
        _filteredData = [];
      });
      _showErrorDialog('数据加载失败', e.toString());
    }
  }

  Future<void> _checkAndDownloadData(String albaranProveedorNo) async {
    print('AlbaranProveedorNo: $albaranProveedorNo');

    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
    });

    try {
      final List<Map<String, dynamic>> localData = await DatabaseService.getAlbaranDetailsByProveedorNo(albaranProveedorNo);

      // 如果本地数据库中已经有数据，直接导航到详情页面
      if (localData.isNotEmpty) {
        print('Data found in local database, skipping download');

        List<OrderDetailPrint> localOrderDetails = localData
            .map((data) => OrderDetailPrint.fromJson(data))
            .toList();

        await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AlbaranDetailPage(
              albaranProveedorNo: albaranProveedorNo,
              details: localOrderDetails,
            ),
          ),
        );

        setState(() {
          _isDownloading = false;
        });
        return;
      }

      // 从在线服务器获取数据
      final List<OrderDetailPrint> onlineData = await ApiPrintService.fetchAlbaranProveedor(albaranProveedorNo);

      if (onlineData.isEmpty) {
        print('No data fetched from server');
        setState(() {
          _isDownloading = false;
        });
        return;
      }

      print('Fetched data: ${onlineData.length} records');

      for (var i = 0; i < onlineData.length; i++) {
        OrderDetailPrint order = onlineData[i];
        await DatabaseService.saveAlbaranDataToLocal({
          'AlbaranProveedorNo': albaranProveedorNo,
          'ArticuloID': order.articuloID,
          'CodigoBarra': order.codigoBarra,
          'NombreES': order.nombreES,
          'Precio': order.precio,
          'Cantidad': order.cantidad,
        });
        setState(() {
          _downloadProgress = (i + 1) / onlineData.length;
        });
      }

      final List<Map<String, dynamic>> finalLocalData = await DatabaseService.getAlbaranDetailsByProveedorNo(albaranProveedorNo);

      if (finalLocalData.isNotEmpty) {
        List<OrderDetailPrint> finalOrderDetails = finalLocalData
            .map((data) => OrderDetailPrint.fromJson(data))
            .toList();

        await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AlbaranDetailPage(
              albaranProveedorNo: albaranProveedorNo,
              details: finalOrderDetails,
            ),
          ),
        );
      } else {
        _showErrorDialog('数据加载失败', '未找到相关记录');
      }
    } catch (e) {
      _showErrorDialog('数据处理失败', e.toString());
    } finally {
      setState(() {
        _isDownloading = false;
      });
    }
  }

  // 新增：跳转到 AlbaranComparePage 页面
  void _goToComparePage(String albaranProveedorNo) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AlbaranComparePage(albaranProveedorNo: albaranProveedorNo),
      ),
    );

    if (result == true) {
      _fetchData(); // 重新获取数据并刷新页面
    }
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('确定'),
            ),
          ],
        );
      },
    );
  }

  // 格式化总额：保留两位小数（如果需要）
  String _formatAmount(dynamic total) {
    if (total == null) return '0'; // 处理 null 情况
    double value = double.tryParse(total.toString()) ?? 0.0;
    return value % 1 == 0 ? value.toInt().toString() : value.toStringAsFixed(2);
  }

  // 格式化数量：确保显示为整数
  String _formatQuantity(dynamic cantidad) {
    if (cantidad == null) return '0'; // 处理 null 情况
    double quantity = double.tryParse(cantidad.toString()) ?? 0.0;
    return quantity.toInt().toString(); // 将数量转换为整数并返回字符串
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: _isSearching 
          ? TextField(
              controller: _searchController,
              autofocus: true,
              style: TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: '搜索入库单...',
                hintStyle: TextStyle(color: Colors.white70),
                border: InputBorder.none,
                suffixIcon: IconButton(
                  icon: Icon(Icons.clear, color: Colors.white),
                  onPressed: _clearSearch,
                ),
              ),
            ) 
          : Text('货品核对'),
        centerTitle: !_isSearching,
        elevation: 0, // 移除AppBar阴影，使界面更现代
        backgroundColor: Colors.blue.shade700, // 更深的蓝色
        actions: [
          IconButton(
            icon: Icon(_isSearching ? Icons.cancel : Icons.search),
            onPressed: _toggleSearch,
            tooltip: _isSearching ? '取消搜索' : '搜索入库单',
          ),
          if (!_isSearching)
            IconButton(
              icon: Icon(Icons.refresh),
              onPressed: () {
                _fetchData();
              },
              tooltip: '刷新数据',
            ),
        ],
      ),
      backgroundColor: Colors.grey.shade50, // 添加轻微的背景色
      body: _isDownloading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: Offset(0, 3),
                        ),
                      ],
                    ),
                    child: CircularProgressIndicator(
                      value: _downloadProgress,
                      strokeWidth: 4,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade700),
                    ),
                  ),
                  SizedBox(height: 20),
                  Text(
                    '下载中... ${(_downloadProgress * 100).toStringAsFixed(0)}%',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            )
          : _filteredData.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        _isSearching ? Icons.search_off : Icons.inbox_outlined,
                        size: 70,
                        color: Colors.grey.shade400,
                      ),
                      SizedBox(height: 16),
                      Text(
                        _isSearching ? '未找到匹配的入库单' : '没有查询到入库单列表',
                        style: TextStyle(fontSize: 18, color: Colors.grey.shade600),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  controller: _scrollController,
                  padding: EdgeInsets.symmetric(vertical: 12),
                  itemCount: _filteredData.length,
                  itemBuilder: (context, index) {
                    final item = _filteredData[index];
                    return Container(
                      margin: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                      decoration: BoxDecoration(
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            offset: Offset(0, 2),
                            blurRadius: 8,
                          ),
                        ],
                      ),
                      child: Card(
                        margin: EdgeInsets.zero,
                        elevation: 0, // 移除默认阴影，使用Container的阴影
                        clipBehavior: Clip.antiAlias, // 确保阴影效果不超出圆角
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: InkWell(
                          onTap: () => _checkAndDownloadData(item['AlbaranProveedorNo'].toString()),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Icon(
                                      Icons.receipt_outlined,
                                      color: Colors.blue.shade700,
                                      size: 18,
                                    ),
                                    SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        '入库单编号: ${item['AlbaranProveedorNo']}',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 18,
                                          color: Colors.blue.shade700,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                Divider(height: 24, thickness: 0.5),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    // Left side - Information
                                    Expanded(
                                      child: Padding(
                                        padding: const EdgeInsets.only(left: 4.0),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Icon(Icons.monetization_on_outlined, color: Colors.green.shade600, size: 16),
                                                SizedBox(width: 8),
                                                Text(
                                                  '总额: ${_formatAmount(item['Total'])}',
                                                  style: TextStyle(
                                                    fontSize: 15,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.green.shade600,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 6),
                                            Row(
                                              children: [
                                                Icon(Icons.inventory_2_outlined, color: Colors.grey.shade700, size: 16),
                                                SizedBox(width: 8),
                                                Text(
                                                  '数量: ${_formatQuantity(item['CantidadTotal'])}',
                                                  style: TextStyle(
                                                    fontSize: 15,
                                                    fontWeight: FontWeight.w500,
                                                    color: Colors.grey.shade700,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 6),
                                            Row(
                                              children: [
                                                Icon(Icons.calendar_today_outlined, color: Colors.grey.shade500, size: 16),
                                                SizedBox(width: 8),
                                                Text(
                                                  '日期: ${item['Fecha']}',
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    color: Colors.grey.shade600,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    // Right side - Button
                                    ElevatedButton.icon(
                                      icon: Icon(Icons.compare_arrows, size: 16),
                                      label: Text('生成结果详情', style: TextStyle(fontSize: 13)),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.blue.shade600,
                                        foregroundColor: Colors.white,
                                        elevation: 1,
                                        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(6),
                                        ),
                                        minimumSize: Size(40, 32), // Smaller minimum size
                                      ),
                                      onPressed: () => _goToComparePage(item['AlbaranProveedorNo'].toString()),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
    );
  }
}
