import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsProvider with ChangeNotifier {
  bool _isDetailedMode = true;
  bool _isOrderCountMode = true;
  bool _isSimpleMode = false; // 新增的属性

  bool get isDetailedMode => _isDetailedMode;
  bool get isOrderCountMode => _isOrderCountMode;
  bool get isSimpleMode => _isSimpleMode; // 新增的 getter

  SettingsProvider() {
    _loadPreferences();
  }

  void _loadPreferences() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    _isDetailedMode = prefs.getBool('isDetailedMode') ?? true;
    _isOrderCountMode = prefs.getBool('isOrderCountMode') ?? true;
    _isSimpleMode = prefs.getBool('isSimpleMode') ?? false; // 加载偏好设置
    notifyListeners();
  }

  void toggleDetailedMode() async {
    _isDetailedMode = !_isDetailedMode;
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool('isDetailedMode', _isDetailedMode);
    notifyListeners();
  }

  void toggleOrderCountMode() async {
    _isOrderCountMode = !_isOrderCountMode;
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool('isOrderCountMode', _isOrderCountMode);
    notifyListeners();
  }

  void toggleSimpleMode() async {
    _isSimpleMode = !_isSimpleMode; // 新增的方法
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool('isSimpleMode', _isSimpleMode);
    notifyListeners();
  }
}
