import 'package:flutter/material.dart';
import 'package:mistoer/models/order.dart';
import 'package:mistoer/models/order_detail.dart';
import 'package:mistoer/services/api_service.dart';
import 'package:mistoer/services/database_service.dart';
import 'package:mistoer/screens/order_detail_screen_zhuangtai2.dart';
import 'package:mistoer/widgets/order_item.dart'; // Ensure this is the correct path
import 'package:mistoer/main.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart'; // Add this import for date formatting

class OrderScreenZhuangtai1 extends StatefulWidget {
  final int zhuangtai;

  OrderScreenZhuangtai1({required this.zhuangtai});

  @override
  _OrderScreenZhuangtai1State createState() => _OrderScreenZhuangtai1State();
}

class _OrderScreenZhuangtai1State extends State<OrderScreenZhuangtai1>
    with RouteAware {
  List<Order> _orders = [];
  List<Order> _filteredOrders = [];
  bool _isLoading = false;
  double _progress = 0.0;
  TextEditingController _searchController = TextEditingController();
  bool _isSearchVisible = false;
  bool _isAscending = true; // Variable to track sorting order

  @override
  void initState() {
    super.initState();
    _fetchOrders();
    _searchController.addListener(_filterOrders);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final ModalRoute? modalRoute = ModalRoute.of(context);
    if (modalRoute is PageRoute) {
      routeObserver.subscribe(this, modalRoute);
    }
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    _searchController.dispose();
    super.dispose();
  }

  @override
  void didPopNext() {
    _fetchOrders();
  }

  Future<void> _fetchOrders() async {
    setState(() {
      _isLoading = true;
      _progress = 0.0;
    });

    try {
      List<Order> orders =
      await ApiService.fetchOrders(zhuangtai: widget.zhuangtai);
      for (var i = 0; i < orders.length; i++) {
        double rate = await DatabaseService.getOrderFulfillmentRate(
            orders[i].pedidoKey);
        orders[i] = orders[i].copyWith(fulfillmentRate: rate);
        setState(() {
          _progress = (i + 1) / orders.length;
        });
      }

      if (!mounted) return;
      setState(() {
        _orders = orders;
        _filteredOrders = orders;
        _sortOrders(); // Sort orders after fetching
        _isLoading = false;
      });

      // 检查下载完成后的订单是否有 baozhuangshu 为 0 的情况
      _checkZeroPackageCountOrders();
    } catch (e) {
      print("Error fetching orders: $e");
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _checkZeroPackageCountOrders() async {
    List<OrderDetail> zeroPackageOrders =
    await DatabaseService.getZeroPackageOrders();

    if (zeroPackageOrders.isNotEmpty) {
      String productCodes = zeroPackageOrders
          .map((detail) => '${detail.codigo} (订单编号: ${detail.pedidoKey})')
          .join(', ');
      _showErrorDialog('以下订单存在包装数为 0 的商品，请检查货号：$productCodes');
    }
  }

  Future<void> _showErrorDialog(String message) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('错误'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('确定'),
            ),
          ],
        );
      },
    );
  }

  void _filterOrders() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredOrders = _orders.where((order) {
        return order.name.toLowerCase().contains(query) ||
            order.pedidoKey.toLowerCase().contains(query) ||
            order.riqi.toLowerCase().contains(query) ||
            order.amount.toString().contains(query) ||
            order.note.toLowerCase().contains(query) ||
            order.beizhu.toLowerCase().contains(query);
      }).toList();
      _sortOrders(); // Sort orders after filtering
    });
  }

  Future<void> _confirmAndSyncOrderDetails(Order order) async {
    bool shouldProceed = await _showConfirmationDialog();

    if (shouldProceed) {
      setState(() {
        _isLoading = true;
        _progress = 0.0;
      });

      try {
        int status = await ApiService.checkOrderStatus(order.pedidoKey);

        if (status > 1) {
          _showOutStockDialog();
          setState(() {
            _isLoading = false;
          });
          return;
        } else if (status == 1) {
          List<OrderDetail> orderDetails =
          await ApiService.fetchOrderDetails(order.pedidoKey);

          bool hasZeroPackageCount = false;

          for (var i = 0; i < orderDetails.length; i++) {
            // 检查本地是否存在该订单细节
            var localDetail =
            await DatabaseService.getOrderDetail(orderDetails[i].id);

            // 如果本地的 finish 不为 -1，则初始化为 0
            int initialFinish = localDetail?.finish == -1 ? -1 : 0;

            OrderDetail newDetail =
            orderDetails[i].copyWith(finish: initialFinish);

            if (newDetail.baozhuangshu == 0) {
              hasZeroPackageCount = true;
            }

            await DatabaseService.insertOrderDetail(newDetail);
            setState(() {
              _progress = (i + 1) / orderDetails.length;
            });
          }

          // 下载完成后进行订单行数比对
          bool isLineCountMatched =
          await _compareOrderLineCount(order.pedidoKey);
          if (!isLineCountMatched) {
            _showOrderLineCountMismatchDialog();
            setState(() {
              _isLoading = false;
            });
            return;
          }

          // 如果存在 baozhuangshu 为 0 的情况，显示警告对话框
          if (hasZeroPackageCount) {
            _showZeroPackageCountDialog();
          }

          // 同步和比对成功后，更新订单状态为2
          await ApiService.updateOrderStatus(order.pedidoKey, 2);

          // 打开OrderDetailScreenZhuangtai2页面
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  OrderDetailScreenZhuangtai2(order: order), // 使用Zhuangtai2页面
            ),
          ).then((value) {
            if (!mounted) return;
            _fetchOrders();
          });
        }
      } catch (e) {
        print("Error syncing order details: $e");
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _showOutStockDialog() async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('提示'),
          content: Text('该订单已被其他人员操作'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _fetchOrders();
              },
              child: Text('确定'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _showZeroPackageCountDialog() async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('警告'),
          content: Text('该订单中存在中包数为 0 的商品，请检查相关信息。'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('确定'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _showOrderLineCountMismatchDialog() async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('订单行数不一致'),
          content: Text('服务器行数与本地行数不一致，无法同步订单详情。'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('确定'),
            ),
          ],
        );
      },
    );
  }

  Future<bool> _showConfirmationDialog() async {
    return (await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('确认'),
        content: Text('你确定要同步此订单的详情吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text('确认'),
          ),
        ],
      ),
    )) ??
        false;
  }

  Future<bool> _compareOrderLineCount(String pedidoKey) async {
    final prefs = await SharedPreferences.getInstance();
    final localLineCountKey = 'order_line_count_$pedidoKey';

    // 检查本地是否已保存订单行数
    if (!prefs.containsKey(localLineCountKey)) {
      try {
        // 从服务器获取订单行数
        int serverLineCount = await ApiService.fetchOrderLineCount(pedidoKey);

        // 本地获取订单行数
        int localLineCount =
        await DatabaseService.getLocalOrderLineCount(pedidoKey);

        // 比对行数
        if (serverLineCount == localLineCount) {
          // 行数一致，保存到本地存储以便下次使用
          prefs.setInt(localLineCountKey, localLineCount);
          return true;
        } else {
          print('行数不一致，服务器: $serverLineCount, 本地: $localLineCount');
          // 处理行数不一致的情况
          return false;
        }
      } catch (e) {
        print('获取行数失败: $e');
        // 处理错误
        return false;
      }
    } else {
      // 行数已在本地保存，无需再次比对
      print('订单行数已本地保存，无需比对');
      return true;
    }
  }

  void _toggleSearch() {
    setState(() {
      _isSearchVisible = !_isSearchVisible;
    });
  }

  // Add this function to toggle sorting order
  void _toggleSortOrder() {
    setState(() {
      _isAscending = !_isAscending;
      _sortOrders();
    });
  }

  // Add this function to sort orders
  void _sortOrders() {
    final dateFormat = DateFormat('yyyy-MM-dd'); // Adjust the format as needed
    _filteredOrders.sort((a, b) {
      DateTime dateA;
      DateTime dateB;
      try {
        dateA = dateFormat.parse(a.riqi);
      } catch (e) {
        dateA = DateTime(1970); // Default date if parsing fails
      }
      try {
        dateB = dateFormat.parse(b.riqi);
      } catch (e) {
        dateB = DateTime(1970); // Default date if parsing fails
      }
      if (_isAscending) {
        return dateA.compareTo(dateB);
      } else {
        return dateB.compareTo(dateA);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('友购订单'),
        actions: [
          IconButton(
            icon: Icon(Icons.search),
            onPressed: _toggleSearch,
          ),
          IconButton(
            icon: Icon(Icons.sync),
            onPressed: _fetchOrders,
          ),
          IconButton(
            icon: Icon(
                _isAscending ? Icons.arrow_downward : Icons.arrow_upward), // Sorting icon
            onPressed: _toggleSortOrder, // Toggle sorting order
          ),
          IconButton(
            icon: Icon(Icons.settings),
            onPressed: () {
              Navigator.pushNamed(context, '/settings');
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          Column(
            children: [
              if (_isSearchVisible)
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      labelText: '搜索',
                      hintText: '输入公司名称、订单编号、日期、金额或备注',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(25.0)),
                      ),
                    ),
                  ),
                ),
              Expanded(
                child: _filteredOrders.isEmpty
                    ? Center(child: Text('没有数据'))
                    : ListView.builder(
                  itemCount: _filteredOrders.length,
                  itemBuilder: (context, index) {
                    var order = _filteredOrders[index];
                    return Card(
                      color: Colors.white,
                      margin: EdgeInsets.all(8.0),
                      elevation: 2.0,
                      child: OrderItem(
                        order: order,
                        onTap: () =>
                            _confirmAndSyncOrderDetails(order),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
          if (_isLoading)
            Center(
              child: Container(
                width: 200,
                padding: EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.8),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 20),
                    LinearProgressIndicator(value: _progress),
                    SizedBox(height: 20),
                    Text(
                      '${(_progress * 100).toStringAsFixed(0)}%',
                      style: TextStyle(color: Colors.white),
                    ),
                    SizedBox(height: 20),
                    Text(
                      '加载中，请稍等...',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}
