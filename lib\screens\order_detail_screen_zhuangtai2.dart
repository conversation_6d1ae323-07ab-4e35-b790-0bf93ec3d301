import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:mistoer/models/order.dart';
import 'package:mistoer/models/order_detail.dart';
import 'package:mistoer/services/api_service.dart';
import 'package:mistoer/services/database_service.dart';
import 'package:mistoer/providers/zhuangtai_provider.dart';
import 'package:mistoer/providers/settings_provider.dart';
import 'package:mistoer/models/dotteddivider.dart';
import 'order_screen_zhuangtai2.dart';
import 'dart:async';
import 'package:mistoer/services/scan_api_service.dart';
import 'package:mistoer/widgets/custom_keyboard.dart';

class OrderDetailScreenZhuangtai2 extends StatefulWidget {
  final Order order;

  OrderDetailScreenZhuangtai2({required this.order});

  @override
  _OrderDetailScreenZhuangtai2State createState() =>
      _OrderDetailScreenZhuangtai2State();
}

class _OrderDetailScreenZhuangtai2State
    extends State<OrderDetailScreenZhuangtai2> {
  bool _isLoading = true;
  double _progress = 0.0;
  TextEditingController _barcodeController = TextEditingController();
  TextEditingController _searchController = TextEditingController();
  bool _isBulkCount = true; // 中包按钮的状态
  String _scannedBarcode = '';
  final FocusNode _barcodeFocusNode = FocusNode();
  bool _isEditing = false;
  final AudioPlayer _audioPlayer = AudioPlayer();
  String _searchQuery = '';
  bool _isSearchVisible = false;
  final ScrollController _scrollController = ScrollController();
  bool _showCompleted = false;
  bool _scrolledToFirst = false;
  Timer? _uploadTimer;
  double? _fulfillmentRate; // 允许空值
  String? _ygid; // 新增：保存 ygid
  bool _isKeyboardVisible = false; // 添加键盘显示状态控制

  // 新增：用于跟踪被隐藏的订单详情ID
  Set<int> _hiddenDetailIds = {};

  @override
  void initState() {
    super.initState();
    _loadOrderDetails();
    _loadYGID(); // 新增：加载 ygid
    _updateFulfillmentRate(); // 新增：在初始化时更新出库率
    _uploadFulfillmentRate();
    // 启动一个定时器，每分钟上传一次出库率
    _uploadTimer = Timer.periodic(Duration(minutes: 1), (timer) {
      _uploadFulfillmentRate();
    });
    
    // 添加延迟聚焦，确保在页面构建完成后执行
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusBarcodeInput();
      setState(() {
        _isKeyboardVisible = false; // 默认隐藏键盘
      });
    });
  }

  Future<void> _loadYGID() async {
    _ygid = await DatabaseService.getYGID();
  }

  @override
  void dispose() {
    _uploadTimer?.cancel();
    _barcodeFocusNode.dispose();
    _barcodeController.dispose();
    _searchController.dispose();
    _audioPlayer.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadOrderDetails() async {
    try {
      List<OrderDetail> localDetails =
      await DatabaseService.getOrderDetails(widget.order.pedidoKey);
      if (localDetails.isNotEmpty) {
        Provider.of<ZhuangtaiProvider>(context, listen: false)
            .loadOrderDetails(localDetails);
        setState(() {
          _isLoading = false;
        });
      } else {
        List<OrderDetail> remoteDetails =
        await ApiService.fetchOrderDetails(widget.order.pedidoKey);
        int totalDetails = remoteDetails.length;
        int count = 0;
        for (var detail in remoteDetails) {
          await DatabaseService.insertOrderDetail(detail);
          count++;
          setState(() {
            _progress = count / totalDetails;
          });
        }
        Provider.of<ZhuangtaiProvider>(context, listen: false)
            .loadOrderDetails(remoteDetails);
        setState(() {
          _isLoading = false;
        });
      }
      // 加载完成后请求焦点
      _focusBarcodeInput();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      // 即使发生错误，也可以选择请求焦点
      _focusBarcodeInput();
    }
  }

  Future<void> _uploadFulfillmentRate() async {
    try {
      // 从本地数据库中获取登录的用户名
      final username = await _getCurrentUsername();
      if (username == 'Default') {
        // 提示用户登录或处理未登录状态
        print('没有找到活动用户，请登录');
        // 这里可以导航到登录页面或者弹出提示框
        // Navigator.pushReplacementNamed(context, '/login');
      }

      // 获取订单的出库率
      final fulfillmentRate =
      await _calculateFulfillmentRate(widget.order.pedidoKey);

      // 上传数据到服务器
      await ApiService.uploadOrderInfo(
          widget.order.pedidoKey, username, fulfillmentRate);
    } catch (e) {
      print('Error uploading fulfillment rate: $e');
    }
  }

  Future<String> _getCurrentUsername() async {
    // 查询本地数据库，获取 in_use = -1 的用户名
    final empleados = await DatabaseService.getUserWithInUseValue(-1);
    if (empleados.isEmpty) {
      return 'Default';
    }
    return empleados.first['Nombre'] as String? ?? 'Default';
  }

  Future<double> _calculateFulfillmentRate(String pedidoKey) async {
    // 从数据库中获取订单详情
    List<OrderDetail> details =
    await DatabaseService.getOrderDetails(pedidoKey);

    // 计算总扫描量和总数量
    int totalScan = details.fold(0, (sum, item) => sum + item.scan);
    int totalQuantity =
    details.fold(0, (sum, item) => sum + item.zongshuliang);

    // 如果总数量为 0，返回 0.0
    if (totalQuantity == 0) return 0.0;

    // 计算出库率
    double fulfillmentRate = totalScan / totalQuantity;

    // 将出库率保留小数点后三位
    return double.parse(fulfillmentRate.toStringAsFixed(3));
  }

  Future<void> _updateFulfillmentRate() async {
    final rate = await _calculateFulfillmentRate(widget.order.pedidoKey);
    setState(() {
      _fulfillmentRate = rate;
    });
  }

  void _handleBarcodeScan(String barcode) async {
    try {
      List<OrderDetail> matchingDetails =
      Provider.of<ZhuangtaiProvider>(context, listen: false)
          .orderDetails
          .where((detail) => detail.bianhao == barcode || detail.codigo == barcode)
          .toList();

      if (matchingDetails.isNotEmpty) {
        var detail = matchingDetails.first;

        // 检查 baozhuangshu 是否为 0
        if (detail.baozhuangshu == 0) {
          _showBaozhuangshuErrorDialog(detail); // 显示错误提示并允许修改
          _playSound('err.mp3'); // 播放错误音效
          return;
        }

        int count = _isBulkCount ? detail.baozhuangshu : 1;
        if (detail.scan >= detail.zongshuliang) {
          _showOutStockDialog();
          _playSound('err.mp3'); // 播放错误音效
        } else {
          await _updateOrderDetails(barcode, count);
          _barcodeController.clear();
          _focusBarcodeInput();
          await _updateFulfillmentRate(); // 新增：扫描后更新出库率

          // 扫描匹配并更新数据库后，重新获取scan值并显示对话框
          OrderDetail? updatedDetail =
          await DatabaseService.getOrderDetail(detail.id);
          if (updatedDetail != null) {
            _showScannedDetailDialog(updatedDetail); // 声音播放在更新方法中完成
          }
        }
      } else {
        _showBarcodeErrorDialog();
        _playSound('err.mp3'); // 播放错误音效
      }
      // 扫描后隐藏已完成的订单
      setState(() {
        _showCompleted = false;
      });
    } catch (e) {
      _showBarcodeErrorDialog();
      _playSound('err.mp3'); // 播放错误音效
    }
  }

  Future<void> _updateOrderDetails(String barcode, int count) async {
    List<OrderDetail> matchingDetails =
    Provider.of<ZhuangtaiProvider>(context, listen: false)
        .orderDetails
        .where((detail) => detail.bianhao == barcode || detail.codigo == barcode)
        .toList();

    if (matchingDetails.isNotEmpty) {
      var detail = matchingDetails.first;
      int newScanCount = detail.scan + count;

      if (newScanCount > detail.zongshuliang) {
        _showOutStockDialog();
        _playSound('err.mp3'); // 播放错误音效
        return;
      } else {
        if (newScanCount >= detail.zongshuliang) {
          detail = detail.copyWith(scan: newScanCount, finish: -1);
        } else {
          detail = detail.copyWith(scan: newScanCount);
        }

        await DatabaseService.updateOrderDetailScan(detail);
        Provider.of<ZhuangtaiProvider>(context, listen: false)
            .updateScan(detail.id, newScanCount);

        // 在更新成功后播放声音
        _playSound('scan.mp3');

        setState(() {
          _scannedBarcode = barcode;
        });
        _startBlinking();
      }
    } else {
      _showBarcodeErrorDialog();
      _playSound('err.mp3'); // 播放错误音效
    }
  }

  void _playSound(String sound) async {
    try {
      await _audioPlayer.play(AssetSource('$sound'));
    } catch (e) {
      print('Error playing sound: $e');
    }
  }

  void _showOutStockDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Error'),
          content: Text(
              '订单已出库, 或检查中包数是否正确或手动修改出库数'),
          actions: [
            TextButton(
              onPressed: () {
                _barcodeController.clear();
                Navigator.of(context).pop();
                _focusBarcodeInput(); // 返回输入框
              },
              child: Text('是'),
            ),
          ],
        );
      },
    );
  }

  void _showBaozhuangshuErrorDialog(OrderDetail detail) {
    final TextEditingController _bzsController = TextEditingController(text: '');
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('中包数为0'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('无法出库: 中包数为0。请输入此货号的中包数以修正本地数据：'),
              SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _bzsController,
                      autofocus: true,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: '中包数 (pcs)',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _barcodeController.clear();
                _focusBarcodeInput();
              },
              child: Text('取消'),
            ),
            TextButton(
              onPressed: () async {
                final String input = _bzsController.text.trim();
                final int? newBzs = int.tryParse(input);
                if (newBzs == null || newBzs <= 0) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('请输入大于0的有效整数')),
                  );
                  return;
                }
                // 更新 Provider 和 本地数据库
                Provider.of<ZhuangtaiProvider>(context, listen: false)
                    .updateBaozhuangshu(detail.id, newBzs);
                await _updateFulfillmentRate();
                Navigator.of(context).pop();
                // 可选：提示成功
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('已更新中包数为 $newBzs')),
                );
                _barcodeController.clear();
                _focusBarcodeInput();
              },
              child: Text('保存'),
            ),
          ],
        );
      },
    );
  }

  void _showBarcodeErrorDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Error'),
          content: Text('商品不属于该订单'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _barcodeController.clear();
                _focusBarcodeInput();
              },
              child: Text('是'),
            ),
          ],
        );
      },
    );
  }

  void _showEmptyInputDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Error'),
          content:
          Text('输入框不能为空，请扫码商品。'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _focusBarcodeInput();
              },
              child: Text('是'),
            ),
          ],
        );
      },
    );
  }

  void _startBlinking() {
    Future.delayed(Duration(milliseconds: 500), () {
      setState(() {
        _scannedBarcode = '';
      });
    });
    Future.delayed(Duration(seconds: 1), () {
      setState(() {
        _scannedBarcode = _barcodeController.text;
      });
    });
  }

  void _focusBarcodeInput() {
    if (mounted) {
      _barcodeFocusNode.requestFocus();
    }
  }

  void _toggleKeyboard() {
    setState(() {
      _isKeyboardVisible = !_isKeyboardVisible;
    });
    _focusBarcodeInput();
  }

  void _toggleSearch() {
    setState(() {
      _isSearchVisible = !_isSearchVisible;
      if (_isSearchVisible) {
        // 切换到搜索模式时，隐藏自定义键盘
        _isKeyboardVisible = false;
      } else {
        // 退出搜索模式时
        _searchQuery = '';
        _searchController.clear();
        _focusBarcodeInput(); // 切换回扫描模式时聚焦条码输入框
      }
    });
  }

  void _applySearchQuery(String query) {
    setState(() {
      _searchQuery = query;
    });
  }

  void _toggleEdit() {
    setState(() {
      _isEditing = !_isEditing;
    });
  }

  Future<void> _saveChanges() async {
    for (var detail in Provider.of<ZhuangtaiProvider>(context, listen: false)
        .orderDetails) {
      await DatabaseService.updateOrderDetailScan(detail);
    }
    setState(() {
      _isEditing = false;
    });
  }

  void _showEditDialog(OrderDetail detail) {
    TextEditingController _scanController = TextEditingController();
    TextEditingController _quantityController = TextEditingController();
    _scanController.text = detail.scan.toString();
    _quantityController.text = detail.zongshuliang.toString();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('修改出库数量'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('现出库数量为: ${detail.scan}'),
              TextField(
                controller: _scanController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(labelText: '手动数量'),
              ),
              TextField(
                controller: _quantityController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(labelText: '客户订单数量'),
                enabled: false,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _focusBarcodeInput(); // 返回输入框
              },
              child: Text('取消'),
            ),
            TextButton(
              onPressed: () {
                int newScan =
                    int.tryParse(_scanController.text) ?? detail.scan;
                int newQuantity =
                    int.tryParse(_quantityController.text) ??
                        detail.zongshuliang;
                if (newScan > newQuantity) {
                  _showOutStockDialog();
                  _playSound('err.mp3'); // 播放错误音效
                } else {
                  Provider.of<ZhuangtaiProvider>(context, listen: false)
                      .updateScan(detail.id, newScan);
                  DatabaseService.updateOrderDetailScan(
                    detail.copyWith(
                        scan: newScan, zongshuliang: newQuantity),
                  ); // 存到数据库
                  setState(() {
                    _isEditing = false; // 退出编辑模式
                  });
                  Navigator.of(context).pop();
                  _focusBarcodeInput(); // 返回输入框
                }
              },
              child: Text('是'),
            ),
          ],
        );
      },
    );
  }

  void _scrollToFirstOrder() {
    _scrollController.animateTo(
      0.0,
      duration: Duration(milliseconds: 300), // 调整动画时间
      curve: Curves.easeInOut, // 使用平滑的动画曲线
    );
  }

  void _scrollToFirstUnfinishedOrder() {
    List<OrderDetail> details =
        Provider.of<ZhuangtaiProvider>(context, listen: false).orderDetails;
    int index =
    details.indexWhere((detail) => detail.scan < detail.zongshuliang);
    if (index != -1) {
      _scrollController.animateTo(
        _calculateScrollOffset(index),
        duration: Duration(milliseconds: 300), // 调整动画时间
        curve: Curves.easeInOut, // 使用平滑的动画曲线
      );
    }
  }

  double _calculateScrollOffset(int index) {
    // 根据实际的ListTile高度计算偏移量，假设每个ListTile的高度为80
    return index * 80.0;
  }

  void _handleDoubleTapOnPage() {
    // 可选：页面整体双击事件处理，如果需要
  }

  void _handleDoubleTap(OrderDetail detail) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('隐藏订单'),
          content: Text('您确定要隐藏此订单吗？'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // 取消操作
              },
              child: Text('取消'),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _hiddenDetailIds.add(detail.id); // 将订单详情ID添加到隐藏集合中
                });
                Navigator.of(context).pop(); // 关闭对话框
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('订单已隐藏')),
                );
              },
              child: Text('确定'),
            ),
          ],
        );
      },
    );
  }

  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('设置'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(_isBulkCount ? '中包开启' : '中包关闭'), // 动态显示文本
                  Switch(
                    value: _isBulkCount,
                    onChanged: (value) {
                      setState(() {
                        _isBulkCount = value;
                      });
                      Navigator.of(context).pop();
                      _focusBarcodeInput(); // 返回输入框
                    },
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(_showCompleted ? '显示已完成订单' : '隐藏已完成订单'), // 新增开关
                  Switch(
                    value: _showCompleted,
                    onChanged: (value) {
                      setState(() {
                        _showCompleted = value;
                      });
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  void _showImage(OrderDetail detail) async {
    try {
      // 获取 YGID 通过 DatabaseService.getYGID()
      String? ygid = _ygid;

      // artId 和 hash 在 orderDetails 中
      String? artId = detail.artId?.toString();
      String? hash = detail.hash?.toString();

      if (ygid != null && ygid.isNotEmpty && artId != null && hash != null) {
        String imageUrl =
            'https://img-mx-1.freex.es/img/$ygid/$artId/600x600/$hash';

        // 打印 imageUrl 到调试控制台
        print('Image URL: $imageUrl');

        // 显示图片弹窗
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.network(imageUrl),
                  SizedBox(height: 10),
                  Text('商品图片', style: TextStyle(fontWeight: FontWeight.bold)),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text('关闭'),
                ),
              ],
            );
          },
        );
      } else {
        // 显示错误信息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('无法获取图片信息')),
        );
      }
    } catch (e) {
      print('Error fetching image info: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('无法获取图片信息')),
      );
    }
  }

  Widget _buildOrderCountDetailItem(
      OrderDetail detail, bool isHighlighted, bool isSimpleMode) {
    // 生成图片 URL
    String imageUrl = _ygid != null && detail.artId != null && detail.hash != null
        ? 'https://img-mx-1.freex.es/img/$_ygid/${detail.artId}/600x600/${detail.hash}'
        : '';

    return ListTile(
      tileColor: isHighlighted
          ? Colors.blue
          : detail.scan == detail.zongshuliang
          ? Colors.white
          : Colors.blue.withOpacity(0.15),
      title: Row(
        children: [
          // 左边显示位置和货号
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.location_on, color: Colors.blue),
                    SizedBox(width: 4),
                    Text(
                      '${detail.weizhi}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8),
                GestureDetector(
                  onTap: () => _showDetailDialog(detail),
                  child: Text(
                    '${detail.codigo}',
                    style: TextStyle(
                      fontSize: 25,
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 缩略图
          GestureDetector(
            onTap: () {
              _showImage(detail);
            },
            child: Container(
              width: 50,
              height: 50,
              child: imageUrl.isNotEmpty
                  ? Image.network(
                imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(Icons.broken_image);
                },
              )
                  : Icon(Icons.image),
            ),
          ),
          // 右边显示订单数和已出库
          Container(
            width: 150,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      '订单数: ',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    Text(
                      '${detail.zongshuliang}',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      '已出库: ',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    Text(
                      '${detail.scan}',
                      style: TextStyle(
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          if (_isEditing)
            IconButton(
              icon: Icon(Icons.edit),
              onPressed: () {
                _showEditDialog(detail);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildPackageCountDetailItem(
      OrderDetail detail, bool isHighlighted, bool isSimpleMode) {
    // 生成图片 URL
    String imageUrl = _ygid != null && detail.artId != null && detail.hash != null
        ? 'https://img-mx-1.freex.es/img/$_ygid/${detail.artId}/600x600/${detail.hash}'
        : '';

    return ListTile(
      tileColor: isHighlighted
          ? Colors.blue
          : detail.scan == detail.zongshuliang
          ? Colors.white
          : Colors.blue.withOpacity(0.15),
      title: Row(
        children: [
          // 左边显示位置和货号
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.location_on, color: Colors.blue),
                    SizedBox(width: 4),
                    Text(
                      '${detail.weizhi}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8),
                GestureDetector(
                  onTap: () => _showDetailDialog(detail),
                  child: Text(
                    '${detail.codigo}',
                    style: TextStyle(
                      fontSize: 25,
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 缩略图
          GestureDetector(
            onTap: () {
              _showImage(detail);
            },
            child: Container(
              width: 50,
              height: 50,
              child: imageUrl.isNotEmpty
                  ? Image.network(
                imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(Icons.broken_image);
                },
              )
                  : Icon(Icons.image),
            ),
          ),
          // 右边显示包数和已出库
          Container(
            width: 150,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      '包数: ',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    Text(
                      '${(detail.zongshuliang / detail.baozhuangshu).toStringAsFixed(0)}',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold, // 字体加粗
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      '已出库: ',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    Text(
                      '${(detail.scan / detail.baozhuangshu).toStringAsFixed(0)}',
                      style: TextStyle(
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          if (_isEditing)
            IconButton(
              icon: Icon(Icons.edit),
              onPressed: () {
                _showEditDialog(detail);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildDetailedModeItem(
      OrderDetail detail, bool isHighlighted, bool isOrderCountMode) {
    // 生成图片 URL
    String imageUrl = _ygid != null && detail.artId != null && detail.hash != null
        ? 'https://img-mx-1.freex.es/img/$_ygid/${detail.artId}/600x600/${detail.hash}'
        : '';

    return ListTile(
      tileColor: isHighlighted
          ? Colors.blue
          : detail.scan == detail.zongshuliang
          ? Colors.white
          : Colors.blue.withOpacity(0.15),
      title: Row(
        children: [
          // 左边显示位置、订单数和已出库
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.location_on, color: Colors.blue),
                    SizedBox(width: 4),
                    Text(
                      '${detail.weizhi}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      isOrderCountMode ? '订单数: ' : '包数: ',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    Text(
                      isOrderCountMode
                          ? '${detail.zongshuliang}'
                          : '${(detail.zongshuliang / detail.baozhuangshu).toStringAsFixed(0)}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      '已出库: ',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    Text(
                      isOrderCountMode
                          ? '${detail.scan}'
                          : '${(detail.scan / detail.baozhuangshu).toStringAsFixed(0)}',
                      style: TextStyle(
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // 中间显示货号、条形码及商品名
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.only(left: 20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('货号: ${detail.codigo}'),
                  Text('条形码: ${detail.bianhao}'),
                  Text(
                      '商品名: ${detail.name_ce.length > 25 ? detail.name_ce.substring(0, 25) + '...' : detail.name_ce}'),
                ],
              ),
            ),
          ),
          // 缩略图
          GestureDetector(
            onTap: () {
              _showImage(detail);
            },
            child: Container(
              width: 50,
              height: 50,
              child: imageUrl.isNotEmpty
                  ? Image.network(
                imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(Icons.broken_image);
                },
              )
                  : Icon(Icons.image),
            ),
          ),
          if (_isEditing)
            IconButton(
              icon: Icon(Icons.edit),
              onPressed: () {
                _showEditDialog(detail);
              },
            ),
        ],
      ),
    );
  }

  void _showDetailDialog(OrderDetail detail) async {
    List<Map<String, dynamic>> bigstockResult = [];
    try {
      // 使用 detail.bianhao 作为查询输入，传递 queryType（例如 'warehouse'）
      bigstockResult =
      await ScanApiService.bigstockqueryDatabase(detail.bianhao, 'warehouse');
    } catch (e) {
      print('获取库存信息失败： $e');
    }

    // 提取库位和箱数信息
    String weizhi = bigstockResult.isNotEmpty
        ? bigstockResult.first['weizhi'] ?? '未知库位'
        : '未知库位';
    String caja = bigstockResult.isNotEmpty
        ? bigstockResult.first['caja']?.toString() ?? '0'
        : '0';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            '商品信息',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('货号: ${detail.codigo}', textAlign: TextAlign.left),
              Text('条形码: ${detail.bianhao}', textAlign: TextAlign.left),
              Text('商品名: ${detail.name_ce}', textAlign: TextAlign.left),
              SizedBox(height: 10),
              // 显示库位和箱数
              Row(
                children: [
                  Icon(Icons.location_on, color: Colors.blue), // 定位图标
                  SizedBox(width: 4), // 添加图标与文本的间距
                  Text(
                    '库位: ',
                    style: TextStyle(
                      fontSize: 16, // 自定义库位标签的字体大小
                      fontWeight: FontWeight.bold, // 自定义字体粗细
                    ),
                  ),
                  Text(
                    '$weizhi',
                    style: TextStyle(
                      fontSize: 20, // 自定义库位数字的字体大小
                      fontWeight: FontWeight.bold, // 自定义字体粗细
                      color: Colors.red, // 自定义颜色
                    ),
                  ),
                ],
              ),
              SizedBox(height: 10),
              Row(
                children: [
                  Icon(Icons.inventory, color: Colors.green), // 箱数图标
                  SizedBox(width: 4),
                  Text(
                    '箱数: ',
                    style: TextStyle(
                      fontSize: 16, // 自定义箱数标签的字体大小
                      fontWeight: FontWeight.bold, // 自定义字体粗细
                    ),
                  ),
                  Text(
                    '$caja',
                    style: TextStyle(
                      fontSize: 20, // 自定义箱数数字的字体大小
                      fontWeight: FontWeight.bold, // 自定义字体粗细
                      color: Colors.green, // 自定义颜色
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _focusBarcodeInput(); // 返回输入框
              },
              child: Text('关闭'),
            ),
          ],
        );
      },
    );
  }

  void _showScannedDetailDialog(OrderDetail detail) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        // 使用 Future.delayed 在 3 秒后自动关闭对话框
        Future.delayed(Duration(seconds: 3), () {
          if (Navigator.of(context).canPop()) {
            Navigator.of(context).pop();
            _focusBarcodeInput(); // 返回输入框
          }
        });

        return AlertDialog(
          title: Center(
            child: Text(
              '剩余需出库中包数:',
              style: TextStyle(
                fontSize: 18, // 自定义字体大小
                fontWeight: FontWeight.bold, // 自定义字体粗细
              ),
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Center(
                child: Text(
                  ' ${((detail.zongshuliang - detail.scan) / detail.baozhuangshu).toStringAsFixed(0)}',
                  style: TextStyle(
                    fontSize: 60, // 大字体显示
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ),
              SizedBox(height: 10), // 添加一些间距
              Center(
                child: RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: '此商品中包数: ',
                        style: TextStyle(
                          fontSize: 18, // 自定义字体大小
                          color: Colors.blue, // 设置文本颜色
                        ),
                      ),
                      TextSpan(
                        text: '${detail.baozhuangshu} pcs', // 显示的数量用红色
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.red, // 设置数量为红色
                          fontWeight: FontWeight.bold, // 数量加粗
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _focusBarcodeInput(); // 返回输入框
              },
              child: Text(
                'Sí',
                style: TextStyle(
                  fontSize: 20, // 大字体显示
                  color: Colors.blue,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // 新增：显示隐藏订单的对话框
  void _showHiddenOrders() {
    List<OrderDetail> hiddenDetails = Provider.of<ZhuangtaiProvider>(context, listen: false)
        .orderDetails
        .where((detail) => _hiddenDetailIds.contains(detail.id))
        .toList();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('隐藏的订单'),
          content: hiddenDetails.isEmpty
              ? Text('没有隐藏的订单。')
              : Container(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: hiddenDetails.length,
              itemBuilder: (context, index) {
                var detail = hiddenDetails[index];
                return ListTile(
                  title: Text(detail.codigo),
                  subtitle: Text(detail.weizhi),
                  trailing: IconButton(
                    icon: Icon(Icons.visibility),
                    tooltip: '恢复订单',
                    onPressed: () {
                      setState(() {
                        _hiddenDetailIds.remove(detail.id); // 从隐藏集合中移除
                      });
                      Navigator.of(context).pop(); // 关闭对话框
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('订单已恢复显示')),
                      );
                    },
                  ),
                  onTap: () {
                    // 可选：显示订单详情
                    _showDetailDialog(detail);
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('关闭'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final settings = Provider.of<SettingsProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${widget.order.name.length > 15 ? widget.order.name.substring(0, 15) + '...' : widget.order.name} ',
              style: TextStyle(fontSize: 16.0),
              overflow: TextOverflow.ellipsis,
            ),
            if (widget.order.pedidoKey != null)
              Text(
                '${widget.order.pedidoKey}',
                style: TextStyle(fontSize: 16.0),
              ),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(_isEditing ? Icons.save : Icons.edit),
            onPressed: _isEditing ? _saveChanges : _toggleEdit,
          ),
          IconButton(
            icon: Icon(_isSearchVisible ? Icons.search_off : Icons.search),
            onPressed: _toggleSearch,
          ),
          IconButton(
            icon: Icon(Icons.settings),
            onPressed: _showSettingsDialog,
          ),
        ],
      ),
      body: GestureDetector(
        onTap: () {
          setState(() {
            _isKeyboardVisible = false;
          });
          _focusBarcodeInput();
        },
        child: Stack(
          children: [
            if (_isLoading)
              Opacity(
                opacity: 0.8,
                child: ModalBarrier(dismissible: false, color: Colors.black),
              ),
            if (_isLoading)
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 20),
                    LinearProgressIndicator(value: _progress),
                    SizedBox(height: 20),
                    Text('加载中，请稍候...'),
                  ],
                ),
              )
            else
              Column(
                children: [
                  if (!_isSearchVisible) // 只在非搜索模式下显示扫描条码输入框
                    Container(
                      color: Colors.white,
                      padding: EdgeInsets.all(8.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: Container(
                              color: Colors.white,
                              child: TextField(
                                controller: _barcodeController,
                                focusNode: _barcodeFocusNode,
                                readOnly: false,
                                showCursor: true,
                                autofocus: true,
                                enableInteractiveSelection: true,
                                keyboardType: TextInputType.none,
                                cursorColor: Colors.blue,
                                cursorWidth: 2.0,
                                decoration: InputDecoration(
                                  labelText: '扫描条形码',
                                  filled: true,
                                  fillColor: Colors.white,
                                  suffixIcon: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: Icon(
                                          _isKeyboardVisible ? Icons.keyboard_hide : Icons.keyboard,
                                          color: Colors.blue,
                                        ),
                                        onPressed: _toggleKeyboard,
                                      ),
                                      // 刷新按钮
                                      PopupMenuButton<String>(
                                        icon: Icon(Icons.refresh),
                                        onSelected: (value) {
                                          if (value == 'toggle_completed') {
                                            setState(() {
                                              _showCompleted = !_showCompleted;
                                            });
                                          } else if (value == 'show_hidden') {
                                            _showHiddenOrders();
                                          }
                                        },
                                        itemBuilder: (BuildContext context) =>
                                        <PopupMenuEntry<String>>[
                                          PopupMenuItem<String>(
                                            value: 'toggle_completed',
                                            child: Text(_showCompleted
                                                ? '隐藏已完成订单'
                                                : '显示已完成订单'),
                                          ),
                                          PopupMenuItem<String>(
                                            value: 'show_hidden',
                                            child: Text('显示隐藏订单'),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                onTap: () {
                                  if (!_isKeyboardVisible) {
                                    FocusScope.of(context).requestFocus(_barcodeFocusNode);
                                  }
                                },
                                onChanged: (value) {
                                  // 如果输入包含回车符，处理扫描输入
                                  if (value.contains('\n')) {
                                    String barcode = value.trim();
                                    _barcodeController.clear();
                                    _handleBarcodeScan(barcode);
                                  }
                                },
                                onSubmitted: (value) {
                                  if (value.isNotEmpty) {
                                    _handleBarcodeScan(value);
                                    _barcodeController.clear();
                                  }
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  if (_isSearchVisible)
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TextField(
                        controller: _searchController,
                        autofocus: true, // 自动聚焦搜索框
                        decoration: InputDecoration(
                          labelText: '搜索货号或条码',
                          filled: true,
                          fillColor: Colors.white,
                          border: OutlineInputBorder(),
                          suffixIcon: IconButton(
                            icon: Icon(Icons.search),
                            onPressed: () {
                              _applySearchQuery(_searchController.text);
                            },
                          ),
                        ),
                        onSubmitted: (value) {
                          _applySearchQuery(value);
                        },
                      ),
                    ),
                  // 新增：出库进度条
                  Transform.translate(
                    offset: Offset(0, -1), // 使用负将组件向上移动，例如-10像素
                    child: Container(
                      width: double.infinity, // 设置进度条宽度为父容器的全部宽度
                      child: SizedBox(
                        height: 8.0, // 设置进度条的高度为8像素
                        child: LinearProgressIndicator(
                          value: _fulfillmentRate,
                          backgroundColor: Colors.grey[300],
                          valueColor:
                          AlwaysStoppedAnimation<Color>(Colors.red),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Consumer<ZhuangtaiProvider>(
                      builder: (context, zhuangtaiProvider, child) {
                        List<OrderDetail> sortedDetails = zhuangtaiProvider
                            .orderDetails
                            .where((detail) => !_hiddenDetailIds.contains(detail.id)) // 排除被隐藏的订单详情
                            .where((detail) =>
                        _searchQuery.isEmpty ||
                            detail.codigo.contains(_searchQuery) ||
                            detail.bianhao.contains(_searchQuery))
                            .toList()
                          ..sort((a, b) {
                            // 1. 处理库位序
                            int weizhiCompare =
                            _compareWeizhi(a.weizhi, b.weizhi);
                            if (weizhiCompare != 0) return weizhiCompare;

                            // 2. 处理货号排序
                            return _compareCodigo(a.codigo, b.codigo);
                          });

                        // 如果_showCompleted为false，则只显示未完成的订单
                        if (!_showCompleted) {
                          sortedDetails = sortedDetails
                              .where((detail) =>
                          detail.scan < detail.zongshuliang) // 仅显示未完成订单
                              .toList();
                        }

                        return sortedDetails.isEmpty
                            ? ListView(
                          // 确保即使没有数据时也可以下拉
                          children: [
                            Center(child: Text('未获取订单详情.')),
                          ],
                        )
                            : ListView.builder(
                          controller: _scrollController,
                          itemCount: sortedDetails.length,
                          itemBuilder: (context, index) {
                            var detail = sortedDetails[index];
                            bool isHighlighted =
                                detail.bianhao == _scannedBarcode;
                            return Padding(
                              padding:
                              const EdgeInsets.symmetric(vertical: 0),
                              child: Column(
                                children: [
                                  GestureDetector(
                                    onDoubleTap: () =>
                                        _handleDoubleTap(detail),
                                    child: settings.isDetailedMode
                                        ? _buildDetailedModeItem(
                                        detail,
                                        isHighlighted,
                                        settings.isOrderCountMode)
                                        : settings.isOrderCountMode
                                        ? _buildOrderCountDetailItem(
                                        detail,
                                        isHighlighted,
                                        settings.isDetailedMode)
                                        : _buildPackageCountDetailItem(
                                        detail,
                                        isHighlighted,
                                        settings.isDetailedMode),
                                  ),
                                  DottedDivider(
                                    color: Colors.black,
                                    height: 1,
                                    dashWidth: 2,
                                  ),
                                ],
                              ),
                            );
                          },
                        );
                      },
                    ),
                  ),
                  if (_isKeyboardVisible)
                    CustomKeyboard(
                      controller: _barcodeController,
                      onSubmit: _handleBarcodeScan,
                      showKeyboard: _isKeyboardVisible,
                      onKeyboardVisibilityChanged: () {
                        setState(() {
                          _isKeyboardVisible = !_isKeyboardVisible;
                        });
                      },
                    ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  // 比较库位函数
  int _compareWeizhi(String weizhiA, String weizhiB) {
    // 现有的比较逻辑...
    RegExp numericRegExp = RegExp(r'^(\d+)-(\d+)(?:-(\d+))?$');
    RegExp alphaNumericRegExp = RegExp(r'^([A-Z]+)(\d+)-(\d+)$');

    var matchA = numericRegExp.firstMatch(weizhiA) ??
        alphaNumericRegExp.firstMatch(weizhiA);
    var matchB = numericRegExp.firstMatch(weizhiB) ??
        alphaNumericRegExp.firstMatch(weizhiB);

    if (matchA != null && matchB != null) {
      if (alphaNumericRegExp.hasMatch(weizhiA) ||
          alphaNumericRegExp.hasMatch(weizhiB)) {
        String letterA = matchA.group(1)!;
        String letterB = matchB.group(1)!;
        int letterCompare = letterA.compareTo(letterB);
        if (letterCompare != 0) return letterCompare;

        int number1A = int.parse(matchA.group(2)!);
        int number1B = int.parse(matchB.group(2)!);
        int number1Compare = number1A.compareTo(number1B);
        if (number1Compare != 0) return number1Compare;

        int number2A = int.parse(matchA.group(3)!);
        int number2B = int.parse(matchB.group(3)!);
        return number2A.compareTo(number2B);
      } else {
        int part1A = int.parse(matchA.group(1)!);
        int part1B = int.parse(matchB.group(1)!);
        int part1Compare = part1A.compareTo(part1B);
        if (part1Compare != 0) return part1Compare;

        int part2A = int.parse(matchA.group(2)!);
        int part2B = int.parse(matchB.group(2)!);
        int part2Compare = part2A.compareTo(part2B);
        if (part2Compare != 0) return part2Compare;

        if (matchA.group(3) != null && matchB.group(3) != null) {
          int part3A = int.parse(matchA.group(3)!);
          int part3B = int.parse(matchB.group(3)!);
          return part3A.compareTo(part3B);
        }
      }
    }

    return weizhiA.compareTo(weizhiB);
  }

  // 比较货号函数
  int _compareCodigo(String codigoA, String codigoB) {
    // 现有的比较逻辑...
    RegExp numericRegExp = RegExp(r'^\d+$');
    RegExp alphaNumericRegExp = RegExp(r'^([A-Z]+)(\d+)$');

    var matchA = numericRegExp.firstMatch(codigoA) ??
        alphaNumericRegExp.firstMatch(codigoA);
    var matchB = numericRegExp.firstMatch(codigoB) ??
        alphaNumericRegExp.firstMatch(codigoB);

    if (matchA != null && matchB != null) {
      if (numericRegExp.hasMatch(codigoA) && numericRegExp.hasMatch(codigoB)) {
        int numberA = int.parse(codigoA);
        int numberB = int.parse(codigoB);
        return numberA.compareTo(numberB);
      }

      if (alphaNumericRegExp.hasMatch(codigoA) &&
          alphaNumericRegExp.hasMatch(codigoB)) {
        String letterA = matchA.group(1)!;
        String letterB = matchB.group(1)!;
        int letterCompare = letterA.compareTo(letterB);
        if (letterCompare != 0) return letterCompare;

        int numberA = int.parse(matchA.group(2)!);
        int numberB = int.parse(matchB.group(2)!);
        return numberA.compareTo(numberB);
      }
    }

    return codigoA.compareTo(codigoB);
  }
}

class ImageScreen extends StatelessWidget {
  final String imageUrl;

  ImageScreen({required this.imageUrl});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('商品图片'),
      ),
      body: Center(
        child: InteractiveViewer(
          panEnabled: true, // 允许平移
          boundaryMargin: EdgeInsets.all(20),
          minScale: 0.5, // 最小缩放比例
          maxScale: 4.0, // 最大缩放比例
          child: Image.network(imageUrl),
        ),
      ),
    );
  }
}
