class OrderDetailPrint {
  final String albaranProveedorNo;
  final String articuloID;
  final String codigoBarra;
  final String nombreES;
  final double precio;
  final double cantidad;
  final String? fechaCaducada;
  final int ordenNo;
  final double checked;

  OrderDetailPrint({
    required this.albaranProveedorNo,
    required this.articuloID,
    required this.codigoBarra,
    required this.nombreES,
    required this.precio,
    required this.cantidad,
    required this.fechaCaducada,
    required this.ordenNo,
    required this.checked,
  });

  // 从 JSON 创建 OrderDetailPrint 实例
  factory OrderDetailPrint.fromJson(Map<String, dynamic> json) {
    return OrderDetailPrint(
      albaranProveedorNo: json['AlbaranProveedorNo']?.toString() ?? '',
      articuloID: json['ArticuloID']?.toString() ?? '',
      codigoBarra: json['CodigoBarra']?.toString() ?? '',
      nombreES: json['NombreES']?.toString() ?? '',
      precio: OrderDetailPrint._parseDouble(json['Precio']),
      cantidad: OrderDetailPrint._parseDouble(json['Cantidad']),
      fechaCaducada: json['FechaCaducada']?.toString(),
      ordenNo: OrderDetailPrint._parseInt(json['OrdenNo']),
      checked: OrderDetailPrint._parseDouble(json['Checked']),
    );
  }

  // 安全地将字符串解析为 double
  static double _parseDouble(dynamic value) {
    if (value == null) {
      return 0.0; // 处理 null 值
    }
    if (value is String) {
      value = value.replaceAll(',', ''); // 移除可能的千位分隔符
      return double.tryParse(value) ?? 0.0; // 解析失败时返回默认值
    } else if (value is int) {
      return value.toDouble(); // 如果是 int，转换为 double
    } else if (value is double) {
      return value; // 如果是 double，直接返回
    } else {
      return 0.0; // 不支持的类型，返回默认值
    }
  }

  // 安全地将字符串解析为 int
  static int _parseInt(dynamic value) {
    if (value == null) {
      return 0; // 返回默认值
    }
    if (value is String) {
      value = value.replaceAll(',', ''); // 移除可能的千位分隔符
      return int.tryParse(value) ?? 0; // 解析失败时返回默认值
    } else if (value is double) {
      return value.toInt(); // 如果是 double，转换为 int
    } else if (value is int) {
      return value; // 如果已经是 int，直接返回
    } else {
      return 0; // 不支持的类型，返回默认值
    }
  }

  // 将 OrderDetailPrint 实例转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'AlbaranProveedorNo': albaranProveedorNo,
      'ArticuloID': articuloID,
      'CodigoBarra': codigoBarra,
      'NombreES': nombreES,
      'Precio': precio.toStringAsFixed(2), // 保留两位小数
      'Cantidad': cantidad.toString(), // 显示为字符串格式
      'FechaCaducada': fechaCaducada,
      'OrdenNo': ordenNo,
      'Checked': checked.toStringAsFixed(2), // 保留两位小数
    };
  }
}
