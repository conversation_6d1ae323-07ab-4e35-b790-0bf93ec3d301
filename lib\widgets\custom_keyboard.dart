import 'package:flutter/material.dart';

// 定义 Shift 键的状态
enum ShiftState {
  off, // 全小写
  sentence, // 句首大写（默认）
  single, // 单次大写
  caps // 全大写（双击）
}

class CustomKeyboard extends StatefulWidget {
  final TextEditingController controller;
  final Function(String) onSubmit;
  final bool showKeyboard;
  final VoidCallback? onKeyboardVisibilityChanged;
  // 添加常驻锁相关属性
  final bool isPermanentKeyboard;
  final Function(bool)? onPermanentModeChanged;
  // 添加按键输入回调
  final Function(String)? onKeyPressed;

  const CustomKeyboard({
    Key? key,
    required this.controller,
    required this.onSubmit,
    this.showKeyboard = true,
    this.onKeyboardVisibilityChanged,
    this.isPermanentKeyboard = false,
    this.onPermanentModeChanged,
    this.onKeyPressed,
  }) : super(key: key);

  @override
  _CustomKeyboardState createState() => _CustomKeyboardState();
}

class _CustomKeyboardState extends State<CustomKeyboard> {
  String _keyboardType = 'number'; // 'number', 'letter', 'symbol'
  ShiftState _shiftState = ShiftState.sentence; // 添加 Shift 状态控制
  DateTime? _lastShiftTap; // 用于检测双击
  // 内部维护常驻模式状态
  late bool _isPermanentMode;

  // 按键布局定义
  final numericRows = [
    ['1', '2', '3', '<-'],
    ['4', '5', '6', 'Enter'],
    ['7', '8', '9', 'Enter'],
    ['-', '0', '.', 'Enter'],
  ];

  List<List<String>> get letterRows {
    final baseRows = [
      ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
      ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l'],
      ['⇧', 'z', 'x', 'c', 'v', 'b', 'n', 'm', '<-'],
    ];

    return baseRows.map((row) => row.map((key) {
      if (key == '⇧' || key == '<-') return key;
      switch (_shiftState) {
        case ShiftState.off:
          return key.toLowerCase();
        case ShiftState.caps:
          return key.toUpperCase();
        case ShiftState.single:
        case ShiftState.sentence:
          // 如果是句首且是第一个字母，或者是单次大写模式，则大写
          bool shouldCapitalize = _shiftState == ShiftState.single || 
              (_shiftState == ShiftState.sentence && widget.controller.text.isEmpty);
          return shouldCapitalize ? key.toUpperCase() : key.toLowerCase();
      }
    }).toList()).toList();
  }

  final symbolRows = [
    ['!', '@', '#', '\$', '%', '^', '&', '*', '(', ')'],
    ['-', '_', '+', '=', '[', ']', '{', '}', '|', '\\'],
    ['/', '?', '.', ',', '<', '>', '~', '<-'],
  ];

  @override
  void initState() {
    super.initState();
    // 初始化常驻模式状态
    _isPermanentMode = widget.isPermanentKeyboard;
    // 监听文本变化以处理句首大写
    widget.controller.addListener(_handleTextChange);
  }

  @override
  void didUpdateWidget(CustomKeyboard oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 如果外部属性变化，同步更新内部状态
    if (oldWidget.isPermanentKeyboard != widget.isPermanentKeyboard) {
      _isPermanentMode = widget.isPermanentKeyboard;
    }
  }

  @override
  void dispose() {
    widget.controller.removeListener(_handleTextChange);
    super.dispose();
  }

  void _handleTextChange() {
    if (_shiftState == ShiftState.single) {
      setState(() {
        _shiftState = ShiftState.sentence;
      });
    }
  }

  void _handleShiftTap() {
    final now = DateTime.now();
    if (_lastShiftTap != null && 
        now.difference(_lastShiftTap!) < Duration(milliseconds: 300)) {
      // 双击 Shift
      setState(() {
        _shiftState = _shiftState == ShiftState.caps ? ShiftState.off : ShiftState.caps;
      });
    } else {
      // 单击 Shift
      setState(() {
        switch (_shiftState) {
          case ShiftState.off:
            _shiftState = ShiftState.single;
            break;
          case ShiftState.single:
          case ShiftState.sentence:
            _shiftState = ShiftState.off;
            break;
          case ShiftState.caps:
            _shiftState = ShiftState.off;
            break;
        }
      });
    }
    _lastShiftTap = now;
  }

  void _handleKeyPress(String value) {
    if (value == '⇧') {
      _handleShiftTap();
      return;
    }

    if (value == 'Enter') {
      widget.onSubmit(widget.controller.text);
      return;
    }

    final currentText = widget.controller.text;
    final currentPosition = widget.controller.selection.baseOffset;

    // 处理小数点的特殊情况
    if (value == '.' && currentText.contains('.')) {
      return; // 如果已经有小数点，则不再添加
    }

    if (currentPosition < 0) {
      // 如果没有选择位置，就添加到末尾
      widget.controller.text = currentText + value;
      widget.controller.selection = TextSelection.collapsed(offset: widget.controller.text.length);
    } else {
      // 在当前光标位置插入
      final newText = currentText.substring(0, currentPosition) + value + currentText.substring(currentPosition);
      widget.controller.text = newText;
      widget.controller.selection = TextSelection.collapsed(offset: currentPosition + 1);
    }

    // 触发按键输入回调
    if (widget.onKeyPressed != null) {
      widget.onKeyPressed!(widget.controller.text);
    }
  }

  void _handleBackspace() {
    final text = widget.controller.text;
    final cursorPosition = widget.controller.selection.baseOffset;

    if (text.isEmpty) return;

    if (cursorPosition < 0) {
      // 如果没有选择位置，删除最后一个字符
      widget.controller.text = text.substring(0, text.length - 1);
      widget.controller.selection = TextSelection.collapsed(offset: widget.controller.text.length);
    } else if (cursorPosition > 0) {
      // 删除光标前的字符
      final newText = text.substring(0, cursorPosition - 1) + text.substring(cursorPosition);
      widget.controller.text = newText;
      widget.controller.selection = TextSelection.collapsed(offset: cursorPosition - 1);
    }
    
    // 触发按键输入回调
    if (widget.onKeyPressed != null) {
      widget.onKeyPressed!(widget.controller.text);
    }
  }

  Widget _buildKeyboardTypeButton(String text, String type) {
    bool isActive = _keyboardType == type;
    return GestureDetector(
      onTap: () {
        setState(() {
          _keyboardType = type;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        decoration: BoxDecoration(
          color: isActive ? Colors.blue.withOpacity(0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(15),
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: isActive ? Colors.blue : Colors.grey,
          ),
        ),
      ),
    );
  }

  Widget _buildKeyButton(String key) {
    bool isSpecialKey = key == '<-' || key == '⇧' || key == 'Enter';
    bool isActionKey = key == '<-' || key == 'Enter';
    bool isShiftKey = key == '⇧';
    bool isEnterKey = key == 'Enter';

    // 确定 Shift 键的颜色和图标
    Color shiftColor = Colors.white;
    Color shiftIconColor = Colors.black87;
    Color buttonColor = Colors.white;

    if (isShiftKey) {
      switch (_shiftState) {
        case ShiftState.off:
          shiftColor = Colors.white;
          shiftIconColor = Colors.black87;
          break;
        case ShiftState.single:
        case ShiftState.sentence:
          shiftColor = Colors.blue.withOpacity(0.2);
          shiftIconColor = Colors.blue;
          break;
        case ShiftState.caps:
          shiftColor = Colors.blue;
          shiftIconColor = Colors.white;
          break;
      }
      buttonColor = shiftColor;
    } else if (isEnterKey) {
      buttonColor = Colors.blue;
    } else if (key == '<-') {
      buttonColor = Colors.red[300]!;
    }

    return Container(
      padding: EdgeInsets.all(1), // 使用padding代替margin，确保整个区域都可点击
      child: Material(
        elevation: 2,
        borderRadius: BorderRadius.circular(2),
        color: buttonColor,
        child: InkWell(
          onTap: () {
            if (key == '<-') {
              _handleBackspace();
            } else {
              _handleKeyPress(key);
            }
          },
          borderRadius: BorderRadius.circular(2),
          child: Container(
            alignment: Alignment.center,
            child: isSpecialKey
                ? (isShiftKey
                    ? Icon(
                        _shiftState == ShiftState.caps
                            ? Icons.keyboard_double_arrow_up
                            : Icons.arrow_upward,
                        color: shiftIconColor,
                        size: 20,
                      )
                    : isEnterKey
                        ? Icon(
                            Icons.keyboard_return,
                            color: Colors.white,
                            size: 24,
                          )
                        : Icon(
                            Icons.backspace,
                            color: Colors.white,
                            size: 20,
                          ))
                : Text(
                    key,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: isActionKey ? Colors.white : Colors.black87,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  Widget _buildNumericKeyboard(List<List<String>> rows) {
    return Row(
      children: [
        // 左侧数字区域
        Expanded(
          flex: 3,
          child: Column(
            children: [
              // 数字 1-9
              ...rows.take(3).map((row) {
                return Expanded(
                  child: Row(
                    children: row.take(3).map((key) {
                      return Expanded(
                        child: _buildKeyButton(key),
                      );
                    }).toList(),
                  ),
                );
              }),
              // 最后一行 - 0 .
              Expanded(
                child: Row(
                  children: ['-', '0', '.'].map((key) {
                    return Expanded(
                      child: _buildKeyButton(key),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),
        // 右侧功能键区域
        Expanded(
          flex: 1,
          child: Column(
            children: [
              // 退格键
              Expanded(
                child: _buildKeyButton('<-'),
              ),
              // 回车键（占据三行）
              Expanded(
                flex: 3,
                child: Container(
                  padding: EdgeInsets.all(1), // 使用padding代替margin，确保整个区域都可点击
                  child: Material(
                    elevation: 2,
                    borderRadius: BorderRadius.circular(2),
                    color: Colors.blue,
                    child: InkWell(
                      onTap: () => _handleKeyPress('Enter'),
                      borderRadius: BorderRadius.circular(2),
                      child: Container(
                        alignment: Alignment.center,
                        child: Icon(
                          Icons.keyboard_return,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLetterKeyboard(List<List<String>> rows) {
    return Column(
      children: rows.map((row) {
        return Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: row.map((key) {
              double flex = key == '<-' ? 1.5 : 1.0;
              return Expanded(
                flex: flex.toInt(),
                child: _buildKeyButton(key),
              );
            }).toList(),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSymbolKeyboard(List<List<String>> rows) {
    return Column(
      children: rows.map((row) {
        return Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: row.map((key) {
              double flex = key == '<-' ? 1.5 : 1.0;
              return Expanded(
                flex: flex.toInt(),
                child: _buildKeyButton(key),
              );
            }).toList(),
          ),
        );
      }).toList(),
    );
  }

  // 切换常驻模式
  void _togglePermanentMode() {
    bool wasLocked = _isPermanentMode;
    
    setState(() {
      _isPermanentMode = !_isPermanentMode;
    });
    
    // 通知父组件状态变化
    if (widget.onPermanentModeChanged != null) {
      widget.onPermanentModeChanged!(_isPermanentMode);
    }
    
    // 如果是从锁定状态切换到解锁状态，则自动隐藏键盘
    if (wasLocked && !_isPermanentMode && widget.onKeyboardVisibilityChanged != null) {
      widget.onKeyboardVisibilityChanged!();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showKeyboard) return SizedBox.shrink();

    return Container(
      height: 280,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 键盘类型切换栏
          Container(
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildKeyboardTypeButton('123', 'number'),
                Container(width: 1, height: 20, color: Colors.grey[300]),
                _buildKeyboardTypeButton('ABC', 'letter'),
                Container(width: 1, height: 20, color: Colors.grey[300]),
                _buildKeyboardTypeButton('#+=', 'symbol'),
                Container(width: 1, height: 20, color: Colors.grey[300]),
                // 添加常驻锁按钮
                GestureDetector(
                  onTap: _togglePermanentMode,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _isPermanentMode ? Icons.lock : Icons.lock_open,
                          size: 18,
                          color: _isPermanentMode ? Colors.orange : Colors.grey,
                        ),
                        SizedBox(width: 4),
                        Text(
                          "锁定",
                          style: TextStyle(
                            fontSize: 14,
                            color: _isPermanentMode ? Colors.orange : Colors.grey,
                            fontWeight: _isPermanentMode ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: _keyboardType == 'number'
                  ? _buildNumericKeyboard(numericRows)
                  : _keyboardType == 'letter'
                      ? _buildLetterKeyboard(letterRows)
                      : _buildSymbolKeyboard(symbolRows),
            ),
          ),
        ],
      ),
    );
  }
} 