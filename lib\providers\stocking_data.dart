import 'package:flutter/material.dart';

class StockingData with ChangeNotifier {
  String _lastScannedWarehouse = '';
  List<Map<String, dynamic>> _scanResults = [];
  bool _inventoryMode = false;

  String get lastScannedWarehouse => _lastScannedWarehouse;
  List<Map<String, dynamic>> get scanResults => _scanResults;
  bool get inventoryMode => _inventoryMode;

  void setLastScannedWarehouse(String warehouse) {
    _lastScannedWarehouse = warehouse;
    notifyListeners();
  }

  void addScanResult(Map<String, dynamic> result) {
    _scanResults.add(result);
    notifyListeners();
  }

  void toggleInventoryMode() {
    _inventoryMode = !_inventoryMode;
    notifyListeners();
  }
}
