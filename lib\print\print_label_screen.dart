import 'package:flutter/material.dart';
import 'package:mistoer/services/database_service_sn.dart';
import 'api_print_service.dart';
import 'new_edit_articulo_screen.dart'; // 导入新页面
import 'package:mistoer/services/database_service.dart';
import 'print_service.dart';
import 'package:barcode_widget/barcode_widget.dart';
import 'print_settings_screen.dart';
import 'edit_articulo_screen.dart'; // 导入编辑页面
import 'package:flutter/services.dart'; // 导入 MethodChannel 支持
import 'package:shared_preferences/shared_preferences.dart'; // 导入 shared_preferences
import 'package:mistoer/widgets/custom_keyboard.dart'; // 导入自定义键盘

class PrintLabelScreen extends StatefulWidget {
  final String articuloID;
  final bool fromAlbaran; // 用于判断是否从 albaran_detail_page.dart 进入

  PrintLabelScreen({this.articuloID = '', this.fromAlbaran = false}); // 默认 fromAlbaran 为 false

  @override
  _PrintLabelScreenState createState() => _PrintLabelScreenState();
}

class _PrintLabelScreenState extends State<PrintLabelScreen> {
  final TextEditingController _controller = TextEditingController();
  late final FocusNode _focusNode;
  Map<String, dynamic>? _articuloData;
  Map<String, dynamic>? _proveedorData; // 添加供应商数据变量
  bool _isLoading = false;
  bool _showCustomKeyboard = false; // 添加自定义键盘显示状态

  int? _poderCambioStock;
  int? _poderMantenerArticulo;

  Map<String, String>? _printTitles;
  String? _currencySymbol;
  int? _currencySymbolPosition;
  int? _labe1; // 用于存储 labe1 值
  int? _labe2; // 用于存储 labe2 值
  int decimalPlaces = 2; // 默认保留 2 位小数

  // 定义 MethodChannel 用于扫码功能
  static const platformScan = MethodChannel('com.example.mistoer/scan');

  // Variables for FloatingActionButton position
  Offset _fabPosition = Offset(240, 170); // 修改默认位置到右侧输入框下方区域
  late SharedPreferences _prefs;

  // 添加一个防止重复查询的标记
  bool _preventDuplicateQuery = false;
  
  // 添加一个打印锁定标记
  bool _isPrintingLocked = false;

  // 添加新的标记，表示是否从编辑页面返回
  bool _isReturningFromEdit = false;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _controller.text = widget.articuloID;
    _loadUserPermissions();
    _loadPrintTitles();
    _loadFabPosition();
    if (widget.articuloID.isNotEmpty && widget.articuloID != 'default_articulo_id') {
      _fetchData(widget.articuloID);
    }

    // 延迟请求焦点，确保 widget 已经完全构建
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        FocusScope.of(context).requestFocus(_focusNode);
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  Future<void> _loadUserPermissions() async {
    int? poderCambioStock = await DatabaseService.getPoderCambioStock();
    int? poderMantenerArticulo = await DatabaseService.getPoderMantenerArticulo();

    setState(() {
      _poderCambioStock = poderCambioStock;
      _poderMantenerArticulo = poderMantenerArticulo;
    });
  }

  Future<void> _loadPrintTitles() async {
    _printTitles = await DatabaseService.getPrintTitles();
    _currencySymbol = _printTitles!['currency_symbol'] ?? '';
    _currencySymbolPosition = int.tryParse(_printTitles!['currency_symbol_position'] ?? '1');
    _labe1 = int.tryParse(_printTitles!['labe1'] ?? '2'); // 默认为2，表示显示零售价
    _labe2 = int.tryParse(_printTitles!['labe2'] ?? '1'); // 默认为1，表示不显示批发价

    // 新增：加载 decimal_places 值
    String? decimalPlacesStr = _printTitles!['decimal_places'] ?? '2';
    decimalPlaces = int.tryParse(decimalPlacesStr) ?? 2;

    setState(() {});
  }

  // 添加一个通用的方法来处理焦点返回到输入框
  void _requestInputFocus() {
    // 使用双重延迟确保焦点更可靠地回到输入框
    Future.delayed(Duration(milliseconds: 150), () {
      if (mounted) {
        FocusScope.of(context).unfocus();
        Future.delayed(Duration(milliseconds: 50), () {
          if (mounted) {
            FocusScope.of(context).requestFocus(_focusNode);
            print('已将焦点返回到输入框');
          }
        });
      }
    });
  }

  Future<void> _fetchData(String input) async {
    if (input.isEmpty) {
      return;
    }
    
    // 新增：首先检查是否处于防重复查询状态
    if (_preventDuplicateQuery) {
      print('防重复查询标记已设置，忽略该查询请求: $input');
      return;
    }
    
    // 如果正在打印中，不处理新的查询
    if (_isPrintingLocked) {
      print('打印操作锁定中，忽略新的查询请求: $input');
      return;
    }
    
    // 设置防重复查询标记
    _preventDuplicateQuery = true;
    print('开始查询商品: $input');

    setState(() {
      _isLoading = true;
    });

    try {
      await _loadPrintTitles();

      // 直接使用input查询，服务端会根据长度判断是否为条形码
      _articuloData = await ApiPrintService.fetchArticuloData(input);

      if (_articuloData != null) {
        // 获取供应商信息
        String proveedorId = "";
        
        // 获取bohao值，决定使用哪个字段获取供应商信息
        int? bohaoValue = await DatabaseServiceSN.getBohaoValue();
        
        if (bohaoValue == 1 && _articuloData!['EmpresaID'] != null && 
            _articuloData!['EmpresaID'].toString().isNotEmpty) {
          // 如果bohao为1，使用EmpresaID
          proveedorId = _articuloData!['EmpresaID'].toString();
          print('使用EmpresaID获取供应商信息: $proveedorId');
        } else if (_articuloData!['ProveedorID'] != null && 
                  _articuloData!['ProveedorID'].toString().isNotEmpty) {
          // 否则使用ProveedorID
          proveedorId = _articuloData!['ProveedorID'].toString();
          print('使用ProveedorID获取供应商信息: $proveedorId');
        }
        
        // 只有当供应商ID非空时才获取供应商信息
        if (proveedorId.isNotEmpty) {
          _proveedorData = await ApiPrintService.fetchProveedorName(proveedorId);
          print('获取供应商信息: $_proveedorData');
        } else {
          _proveedorData = null;
          print('供应商ID为空，跳过获取供应商信息');
        }
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }

      if (_articuloData == null) {
        print('未找到商品: $input');
        _showCreateArticuloDialog(input, input.length > 10 ? 'barcode' : 'codigo');
      } else {
        print('查询到商品: ${_articuloData!['ArticuloID']} - ${_articuloData!['NombreES']}');
        if (mounted) {
          setState(() {
            _articuloData = _articuloData;
            _controller.clear(); // 查询到结果后清除输入框内容
          });
        }

        // 检查是否配置为"扫描自动打印"模式，且不是从编辑页面返回，如果是则自动打印一张标签
        if (_printTitles != null && _printTitles!['maxpage'] == '2' && !_isPrintingLocked && !_isReturningFromEdit) {
          print('检测到扫描自动打印模式，开始打印');
          await _startPrintingProcess(printCount: 1);
        } else if (_isPrintingLocked) {
          print('已有打印任务进行中，不触发新的打印');
        } else if (_isReturningFromEdit) {
          print('从编辑页面返回，跳过自动打印');
          // 重置标记
          _isReturningFromEdit = false;
          // 返回焦点到输入框
          _requestInputFocus();
        } else {
          // 如果不自动打印，确保光标回到输入框
          print('未设置自动打印，返回焦点到输入框');
          _requestInputFocus();
        }
      }
    } catch (e) {
      print('查询商品时出错: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('查询商品时出错，请重试')),
        );
      }
    } finally {
      // 无论成功失败，最后都重置防重复查询标记
      _preventDuplicateQuery = false;
    }
  }

  // 修改摄像头扫码功能为通过 MethodChannel 调用原生扫码功能
  Future<void> _scanBarcode() async {
    // 如果已经在扫码或打印过程中，直接返回
    if (_preventDuplicateQuery || _isPrintingLocked) {
      print('已有扫码或打印任务正在进行，忽略此次扫码请求');
      return;
    }
    
    try {
      // 设置标记，防止重复查询
      _preventDuplicateQuery = true;
      print('开始扫码过程，设置防重复查询标记');
      
      // 调用原生的扫码功能
      final String scanResult = await platformScan.invokeMethod('startScan');
      if (scanResult.isNotEmpty && scanResult != '-1') {
        print('扫码成功: $scanResult');
        
        setState(() {
          _controller.text = scanResult; // 将扫码结果填入输入框
        });
        
        // 直接查询，避免通过输入框的onChange再次触发
        await _fetchData(scanResult);
        
        // 扫码和查询完成后重置标记
        print('扫码和查询过程完成，重置防重复查询标记');
        _preventDuplicateQuery = false;
      } else {
        print('扫码取消或失败');
        // 重置标记
        _preventDuplicateQuery = false;
        // 如果扫码取消或失败，确保焦点回到输入框
        _requestInputFocus();
      }
    } on PlatformException catch (e) {
      print("Failed to start scan: '${e.message}'.");
      // 重置标记
      _preventDuplicateQuery = false;
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('无法获取扫码结果')),
      );
      // 出错也确保焦点回到输入框
      _requestInputFocus();
    }
  }

  void _showCreateArticuloDialog(String input, String inputType) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('创建商品'),
          content: Text('未查询到商品信息，是否创建新商品？'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // 使用通用方法请求焦点
                _requestInputFocus();
              },
              child: Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _navigateToNewEditArticuloScreen(input, inputType);
              },
              child: Text('创建'),
            ),
          ],
        );
      },
    );
  }

  void _navigateToNewEditArticuloScreen(String input, String inputType) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NewEditArticuloScreen(
          initialCodigo: inputType == 'codigo' && input.length <= 10 ? input : null,
          initialBarcode: inputType == 'barcode' && input.length > 10 ? input : null,
        ),
      ),
    ).then((dynamic result) {
      // 首先清除输入框内容，防止onChange触发
      setState(() {
        _controller.clear();
      });
      
      // 检查返回结果是否包含实际创建的商品ID
      if (result is Map<String, dynamic>) {
        print('新商品创建成功，返回数据: $result');
        // 创建成功后，设置标记防止自动打印
        _isReturningFromEdit = true;
        
        // 延迟一下再查询，给服务器处理数据的时间
        Future.delayed(Duration(milliseconds: 800), () {
          // 使用返回的商品ID进行查询，优先使用ArticuloID
          String articuloID = result['ArticuloID'] ?? '';
          if (articuloID.isNotEmpty) {
            print('使用返回的商品ID进行查询: $articuloID');
            _fetchData(articuloID);
          } else {
            // 如果没有返回ID，使用原始输入
            print('使用原始输入进行查询: $input');
            _fetchData(input);
          }
        });
      } else if (result == true) {
        // 旧版本兼容，如果只返回了true，则使用原始输入查询
        print('新商品创建成功，但未返回详细信息，使用原始输入查询');
        _isReturningFromEdit = true;
        
        // 同样延迟查询
        Future.delayed(Duration(milliseconds: 800), () {
          _fetchData(input);
        });
      } else {
        // 如果创建未成功，焦点回到输入框
        _requestInputFocus();
      }
    });
  }

  void _showStockEditDialog() {
    if (_poderCambioStock == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('您没有权限修改库存')),
      );
      return;
    }

    final TextEditingController _stockController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) {
        return FutureBuilder<int?>(
          future: DatabaseServiceSN.getBohaoValue(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(child: CircularProgressIndicator());
            }

            final bohao = snapshot.data ?? 0;
            final stockField = bohao == 1 ? 'stock' : 'Stock';

            return AlertDialog(
              title: Text('修改库存', style: TextStyle(fontSize: 22)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 显示当前库存
                  Container(
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Text('当前库存: ', style: TextStyle(fontSize: 18)),
                        Text(
                          '${(double.tryParse(_articuloData?[stockField]?.toString() ?? '0') ?? 0).toInt()}',
                          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 16),
                  // 输入新库存的文本框
                  TextField(
                    controller: _stockController,
                    keyboardType: TextInputType.number,
                    style: TextStyle(fontSize: 20),
                    decoration: InputDecoration(
                      labelText: '输入新的数量',
                      labelStyle: TextStyle(fontSize: 18),
                      border: OutlineInputBorder(),
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    // 使用通用方法请求焦点
                    _requestInputFocus();
                  },
                  child: Text('取消', style: TextStyle(fontSize: 18)),
                ),
                ElevatedButton(
                  onPressed: () async {
                    try {
                      final newStock = int.parse(_stockController.text);
                      final articuloID = _articuloData!['ArticuloID'].toString();

                      bool success = await ApiPrintService.updateStock(articuloID, newStock);

                      if (success) {
                        setState(() {
                          _articuloData![stockField] = newStock;
                        });
                        Navigator.of(context).pop();
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('库存更新成功')),
                        );
                        // 使用通用方法请求焦点
                        _requestInputFocus();
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('库存更新失败，请重试')),
                        );
                      }
                    } catch (e) {
                      print('Error updating stock: $e');
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('库存更新失败，请检查输入')),
                      );
                      // 使用通用方法请求焦点
                      _requestInputFocus();
                    }
                  },
                  child: Text('保存', style: TextStyle(fontSize: 18)),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _startPrintingProcess({required int printCount}) async {
    // 如果已经在打印中，则不再处理
    if (_isPrintingLocked) {
      print('打印操作已被锁定，忽略重复请求');
      return;
    }
    
    // 设置打印锁定
    _isPrintingLocked = true;
    print('开始打印过程，打印数量: $printCount，锁定打印操作');
    
    // 显示一个加载指示器，表示正在打印
    setState(() {
      _isLoading = true;
    });

    try {
      for (int i = 0; i < printCount; i++) {
        print('打印第 ${i+1} 张标签');
        await _printLabel();
      }
      
      // 仅清除输入框，但保留商品数据显示
      _controller.clear();
      setState(() {
        // 不再清除商品数据: _articuloData = null;
        _isLoading = false;
      });
      
      // 如果是从 albaran_detail_page.dart 进入，打印完成后关闭页面返回上一页
      if (widget.fromAlbaran) {
        print('从albaran页面进入，打印完成后返回');
        Navigator.pop(context);
        // 解除打印锁定
        _isPrintingLocked = false;
        return; // 如果返回上一页，就不需要继续请求焦点
      }
      
      print('打印完成，返回焦点到输入框');
      // 使用通用方法请求焦点
      _requestInputFocus();
      
      // 延迟解除打印锁定，防止连续触发
      Future.delayed(Duration(seconds: 2), () {
        _isPrintingLocked = false;
        print('打印锁定已解除');
      });
    } catch (e) {
      print('打印过程出错: $e');
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('打印失败，请检查输入')),
      );
      
      // 错误情况下也使用通用方法请求焦点
      _requestInputFocus();
      
      // 错误时立即解除锁定
      _isPrintingLocked = false;
      print('打印出错，锁定已解除');
    }
  }

  Future<void> _showPrintDialog() async {
    // 如果打印锁定中，不允许显示打印对话框
    if (_isPrintingLocked) {
      print('打印锁定中，忽略打印按钮请求');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('打印操作进行中，请稍候...')),
      );
      return;
    }
    
    int printCount = 1; // 默认打印数量为 1

    if (_printTitles != null && _printTitles!['maxpage'] == '1') {
      // 如果 maxpage 为 1，直接打印一次
      await _startPrintingProcess(printCount: 1);
    } else {
      final TextEditingController _printController = TextEditingController(text: '1');

      showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            title: Text('打印标签'),
            content: Row(
              children: [
                IconButton(
                  icon: Icon(Icons.remove_circle_outline),
                  onPressed: () {
                    int currentValue = int.tryParse(_printController.text) ?? 1;
                    if (currentValue > 1) {
                      currentValue--;
                      _printController.text = currentValue.toString();
                    }
                  },
                ),
                Expanded(
                  child: TextField(
                    controller: _printController,
                    keyboardType: TextInputType.number, // 设置为数字键盘
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 20),
                    decoration: InputDecoration(
                      border: OutlineInputBorder(),
                    ),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^[1-9][0-9]{0,2}$')),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.add_circle_outline),
                  onPressed: () {
                    int currentValue = int.tryParse(_printController.text) ?? 1;
                    if (currentValue < 999) {
                      currentValue++;
                      _printController.text = currentValue.toString();
                    }
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // 使用通用方法请求焦点
                  _requestInputFocus();
                },
                child: Text('取消', style: TextStyle(fontSize: 18)),
              ),
              ElevatedButton(
                onPressed: () async {
                  try {
                    // 再次检查打印锁定状态（可能用户长时间未操作，对话框打开期间已经触发了其他打印）
                    if (_isPrintingLocked) {
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('打印操作进行中，请稍候...')),
                      );
                      return;
                    }
                    
                    final int printCount = int.parse(_printController.text);
                    Navigator.of(context).pop();
                    await _startPrintingProcess(printCount: printCount);
                    // 不需要额外调用_requestInputFocus()，因为_startPrintingProcess已经包含焦点处理
                  } catch (e) {
                    print('打印过程出错: $e');
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('打印失败，请检查输入')),
                    );
                    Navigator.of(context).pop();
                    // 确保在出错时也将焦点返回到输入框
                    _requestInputFocus();
                  }
                },
                child: Text('打印', style: TextStyle(fontSize: 18)),
              ),
            ],
          );
        },
      );
    }
  }

  Future<void> _printLabel() async {
    if (_articuloData == null || _printTitles == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('商品数据或打印标题未加载，请检查')),
      );
      return;
    }

    try {
      await PrintService.printLabel(
        productNumberLabel: _printTitles!['product_number'] ?? '货号',
        productNameLabel: _printTitles!['product_name'] ?? '商品名称',
        retailPriceLabel: _printTitles!['retail_price'] ?? '零售价',
        memberPriceLabel: _printTitles!['member_price'] ?? '批发价',
        articuloID: _articuloData!['ArticuloID'].toString(),
        nombreES: _articuloData!['NombreES'].toString(),
        codigoBarra: _articuloData!['CodigoBarra'].toString(),
        precioDetalle: _formatPrice(_articuloData!['PrecioDetalle']),
        precioMayor: _formatPrice(_articuloData!['PrecioMayor']),
      );
    } catch (e) {
      print('Error printing label: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('标签打印失败')),
      );
    }
  }

  String _formatPrice(String price) {
    // 尝试将价格转换为 double，如果失败则返回原字符串
    try {
      double parsedPrice = double.parse(price);
      // 使用 decimalPlaces 格式化价格
      String formattedPrice = parsedPrice.toStringAsFixed(decimalPlaces);

      // 根据货币符号的位置来返回带符号的价格
      if (_currencySymbolPosition == 1) {
        return '$_currencySymbol$formattedPrice';
      } else {
        return '$formattedPrice$_currencySymbol';
      }
    } catch (e) {
      // 如果转换失败，返回原字符串（例如：价格不合法时）
      return price;
    }
  }

  // 修改零售价编辑对话框方法
  void _showRetailPriceEditDialog() {
    if (_poderMantenerArticulo != -1) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('您没有权限修改价格')),
      );
      return;
    }

    // 修改初始化，不显示当前价格值
    final TextEditingController _priceController = TextEditingController(
      text: ''  // 将初始值设为空字符串
    );

    showDialog(
      context: context,
      barrierDismissible: false, // 点击外部不关闭
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.0),
          ),
          contentPadding: EdgeInsets.fromLTRB(20, 20, 20, 10),
          titlePadding: EdgeInsets.fromLTRB(20, 20, 20, 0),
          title: Row(
            children: [
              Icon(Icons.edit_note, color: Colors.blue[700], size: 24),
              SizedBox(width: 10),
              Text(
                '修改零售价',
                style: TextStyle(
                  fontSize: 18, 
                  fontWeight: FontWeight.w500,
                  color: Colors.blue[800],
                ),
              ),
            ],
          ),
          content: Container(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 显示当前价格
                Container(
                  padding: EdgeInsets.all(14),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: Colors.blue.withOpacity(0.2)),
                  ),
                  child: Row(
                    children: [
                      Text(
                        '当前价格: ', 
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.blue[800],
                        ),
                      ),
                      Text(
                        _formatPrice(_articuloData?['PrecioDetalle']?.toString() ?? '0'),
                        style: TextStyle(
                          fontSize: 16, 
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 20),
                // 输入新价格的文本框 - 确保使用数字键盘
                TextField(
                  controller: _priceController,
                  keyboardType: TextInputType.numberWithOptions(decimal: true), // 数字键盘带小数点
                  style: TextStyle(fontSize: 18, color: Colors.black87),
                  decoration: InputDecoration(
                    labelText: '输入新的价格',
                    labelStyle: TextStyle(fontSize: 16, color: Colors.grey[700]),
                    hintText: '请输入新的零售价', // 添加提示文本
                    hintStyle: TextStyle(color: Colors.grey[400]),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: BorderSide(color: Colors.blue.withOpacity(0.3), width: 1),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: BorderSide(color: Colors.blue, width: 1.5),
                    ),
                    filled: true,
                    fillColor: Colors.grey[50],
                    contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                    prefixIcon: Icon(Icons.price_change, color: Colors.blue[300], size: 20),
                  ),
                  autofocus: true, // 自动获取焦点
                  // 确保点击时显示数字键盘
                  onTap: () {
                    // 此处不需要额外处理，默认点击会弹出键盘
                    // 但是确保键盘类型正确设置为数字键盘
                  },
                  // 添加提交事件处理
                  onSubmitted: (value) {
                    if (value.isNotEmpty) {
                      // 点击键盘上的确认按钮时自动触发保存
                      try {
                        final newPrice = double.parse(value);
                        _updateRetailPrice(newPrice, context, _requestInputFocus);
                      } catch (e) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('请输入有效的价格')),
                        );
                      }
                    }
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // 使用通用方法请求焦点
                _requestInputFocus();
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey[700],
                padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text('取消', style: TextStyle(fontSize: 16)),
            ),
            ElevatedButton(
              onPressed: () async {
                try {
                  if (_priceController.text.isEmpty) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('请输入新的价格')),
                    );
                    return;
                  }
                  
                  final newPrice = double.parse(_priceController.text);
                  _updateRetailPrice(newPrice, context, _requestInputFocus);
                } catch (e) {
                  print('Error updating price: $e');
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('价格更新失败，请检查输入')),
                  );
                  // 使用通用方法请求焦点
                  _requestInputFocus();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 2,
              ),
              child: Text('保存', style: TextStyle(fontSize: 16)),
            ),
          ],
        );
      },
    );
  }

  // 添加辅助方法更新零售价，减少代码重复
  void _updateRetailPrice(double newPrice, BuildContext context, Function callback) async {
    final articuloID = _articuloData!['ArticuloID'].toString();

    // 获取当前批发价，确保同时更新两个价格
    double precioMayor = double.tryParse(_articuloData!['PrecioMayor']?.toString() ?? '0') ?? 0;

    // 使用 updatePrecio 方法同时更新零售价和批发价
    bool success = await ApiPrintService.updatePrecio(
      articuloID: articuloID,
      precioDetalle: newPrice,
      precioMayor: precioMayor,
    );

    if (success) {
      // 更新成功后重新获取完整的商品数据
      Map<String, dynamic>? updatedData = await ApiPrintService.fetchArticuloData(articuloID);
      
      if (updatedData != null) {
        setState(() {
          _articuloData = updatedData;
        });
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 10),
                Text('零售价更新成功'),
              ],
            ),
            backgroundColor: Colors.green,
          ),
        );
        // 使用通用方法请求焦点
        callback();
      } else {
        // 如果获取更新后的数据失败，至少更新价格字段
        setState(() {
          _articuloData!['PrecioDetalle'] = newPrice.toString();
        });
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('零售价已更新，但获取完整商品信息失败')),
        );
        callback();
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('价格更新失败，请重试')),
      );
    }
  }

  // 修改批发价编辑对话框方法
  void _showWholesalePriceEditDialog() {
    if (_poderMantenerArticulo != -1) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('您没有权限修改价格')),
      );
      return;
    }

    // 修改初始化，不显示当前价格值
    final TextEditingController _priceController = TextEditingController(
      text: ''  // 将初始值设为空字符串
    );

    showDialog(
      context: context,
      barrierDismissible: false, // 点击外部不关闭
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.0),
          ),
          contentPadding: EdgeInsets.fromLTRB(20, 20, 20, 10),
          titlePadding: EdgeInsets.fromLTRB(20, 20, 20, 0),
          title: Row(
            children: [
              Icon(Icons.edit_note, color: Colors.orange[700], size: 24),
              SizedBox(width: 10),
              Text(
                '修改批发价',
                style: TextStyle(
                  fontSize: 18, 
                  fontWeight: FontWeight.w500,
                  color: Colors.orange[800],
                ),
              ),
            ],
          ),
          content: Container(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 显示当前价格
                Container(
                  padding: EdgeInsets.all(14),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: Colors.orange.withOpacity(0.2)),
                  ),
                  child: Row(
                    children: [
                      Text(
                        '当前价格: ', 
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.orange[800],
                        ),
                      ),
                      Text(
                        _formatPrice(_articuloData?['PrecioMayor']?.toString() ?? '0'),
                        style: TextStyle(
                          fontSize: 16, 
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 20),
                // 输入新价格的文本框 - 确保使用数字键盘
                TextField(
                  controller: _priceController,
                  keyboardType: TextInputType.numberWithOptions(decimal: true), // 数字键盘带小数点
                  style: TextStyle(fontSize: 18, color: Colors.black87),
                  decoration: InputDecoration(
                    labelText: '输入新的价格',
                    labelStyle: TextStyle(fontSize: 16, color: Colors.grey[700]),
                    hintText: '请输入新的批发价', // 添加提示文本
                    hintStyle: TextStyle(color: Colors.grey[400]),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: BorderSide(color: Colors.orange.withOpacity(0.3), width: 1),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: BorderSide(color: Colors.orange, width: 1.5),
                    ),
                    filled: true,
                    fillColor: Colors.grey[50],
                    contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                    prefixIcon: Icon(Icons.price_change, color: Colors.orange[300], size: 20),
                  ),
                  autofocus: true, // 自动获取焦点
                  // 确保点击时显示数字键盘
                  onTap: () {
                    // 此处不需要额外处理，默认点击会弹出键盘
                    // 但是确保键盘类型正确设置为数字键盘
                  },
                  // 添加提交事件处理
                  onSubmitted: (value) {
                    if (value.isNotEmpty) {
                      // 点击键盘上的确认按钮时自动触发保存
                      try {
                        final newPrice = double.parse(value);
                        _updateWholesalePrice(newPrice, context, _requestInputFocus);
                      } catch (e) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('请输入有效的价格')),
                        );
                      }
                    }
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // 使用通用方法请求焦点
                _requestInputFocus();
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey[700],
                padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text('取消', style: TextStyle(fontSize: 16)),
            ),
            ElevatedButton(
              onPressed: () async {
                try {
                  if (_priceController.text.isEmpty) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('请输入新的价格')),
                    );
                    return;
                  }
                  
                  final newPrice = double.parse(_priceController.text);
                  _updateWholesalePrice(newPrice, context, _requestInputFocus);
                } catch (e) {
                  print('Error updating wholesale price: $e');
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('价格更新失败，请检查输入')),
                  );
                  // 使用通用方法请求焦点
                  _requestInputFocus();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 2,
              ),
              child: Text('保存', style: TextStyle(fontSize: 16)),
            ),
          ],
        );
      },
    );
  }

  // 添加辅助方法更新批发价，减少代码重复
  void _updateWholesalePrice(double newPrice, BuildContext context, Function callback) async {
    final articuloID = _articuloData!['ArticuloID'].toString();

    // 获取当前零售价，确保同时更新两个价格
    double precioDetalle = double.tryParse(_articuloData!['PrecioDetalle']?.toString() ?? '0') ?? 0;

    // 使用 updatePrecio 方法同时更新零售价和批发价
    bool success = await ApiPrintService.updatePrecio(
      articuloID: articuloID,
      precioDetalle: precioDetalle,
      precioMayor: newPrice,
    );

    if (success) {
      // 更新成功后重新获取完整的商品数据
      Map<String, dynamic>? updatedData = await ApiPrintService.fetchArticuloData(articuloID);
      
      if (updatedData != null) {
        setState(() {
          _articuloData = updatedData;
        });
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 10),
                Text('批发价更新成功'),
              ],
            ),
            backgroundColor: Colors.green,
          ),
        );
        // 使用通用方法请求焦点
        callback();
      } else {
        // 如果获取更新后的数据失败，至少更新价格字段
        setState(() {
          _articuloData!['PrecioMayor'] = newPrice.toString();
        });
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('批发价已更新，但获取完整商品信息失败')),
        );
        callback();
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('价格更新失败，请重试')),
      );
    }
  }

  Widget _buildArticuloInfo() {
    if (_printTitles == null) {
      return Center(child: CircularProgressIndicator());
    }

    if (_articuloData == null) {
      return Container();
    }

    return Container(
      padding: EdgeInsets.only(left: 0.0, right: 0.0, top: 0.0, bottom: 100.0), // 减小底部间距
      decoration: BoxDecoration(
        border: Border.all(color: Colors.white),
        borderRadius: BorderRadius.circular(0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 优化价格区域排版
          Container(
            padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 货号显示，移至价格区域最上方
                _buildInfoRow(
                  title:
                  '${(_printTitles?['product_number']?.isNotEmpty ?? false) ? _printTitles!['product_number'] : '货号'}: ',
                  value: _articuloData?['ArticuloID']?.toString() ?? '无货号',
                  fontSize: 18,
                  boldValue: true,
                ),
                SizedBox(height: 6), // 添加间距
                // 添加进货价显示，权限与修改商品一致，显示在零售价之前
                if (_poderMantenerArticulo == -1 && _articuloData?['PrecioCoste'] != null)
                  Padding(
                    padding: EdgeInsets.only(bottom: 4.0),
                    child: _buildInfoRow(
                      title: '进货价: ',
                      value: _formatPrice(_articuloData!['PrecioCoste']),
                      valueColor: Colors.red[800],
                      fontSize: 20, // 缩小字体
                    ),
                  ),
                // 调整为先显示价格，再显示商品信息
                if (_labe1 == 2)
                  _buildInfoRow(
                    title:
                    '${(_printTitles?['retail_price']?.isNotEmpty ?? false) ? _printTitles!['retail_price'] : '零售价'}: ',
                    value: _articuloData?['PrecioDetalle'] != null
                        ? _formatPrice(_articuloData!['PrecioDetalle'])
                        : '无价格',
                    valueColor: Colors.black,
                    fontSize: 24,
                    boldValue: true,
                    onTap: _poderMantenerArticulo == -1 ? _showRetailPriceEditDialog : null,
                  ),
                if (_labe2 == 2)
                  Padding(
                    padding: EdgeInsets.only(top: 4.0),
                    child: _buildInfoRow(
                      title:
                      '${(_printTitles?['member_price']?.isNotEmpty ?? false) ? _printTitles!['member_price'] : '批发价'}: ',
                      value: _articuloData?['PrecioMayor'] != null
                          ? _formatPrice(_articuloData!['PrecioMayor'])
                          : '无价格',
                      valueColor: Colors.black,
                      fontSize: 24,
                      boldValue: true,
                      onTap: _poderMantenerArticulo == -1 ? _showWholesalePriceEditDialog : null,
                    ),
                  ),
                if (_labe1 == 1 && _labe2 == 1)
                  Column(
                    children: [
                      _buildInfoRow(
                        title:
                        '${(_printTitles?['retail_price']?.isNotEmpty ?? false) ? _printTitles!['retail_price'] : '零售价'}: ',
                        value: _articuloData?['PrecioDetalle'] != null
                            ? _formatPrice(_articuloData!['PrecioDetalle'])
                            : '无价格',
                        valueColor: Colors.black,
                        fontSize: 24,
                        boldValue: true,
                        onTap: _poderMantenerArticulo == -1 ? _showRetailPriceEditDialog : null,
                      ),
                      SizedBox(height: 4), // 减小间距
                      _buildInfoRow(
                        title:
                        '${(_printTitles?['member_price']?.isNotEmpty ?? false) ? _printTitles!['member_price'] : '批发价'}: ',
                        value: _articuloData?['PrecioMayor'] != null
                            ? _formatPrice(_articuloData!['PrecioMayor'])
                            : '无价格',
                        valueColor: Colors.black,
                        fontSize: 24,
                        boldValue: true,
                        onTap: _poderMantenerArticulo == -1 ? _showWholesalePriceEditDialog : null,
                      ),
                    ],
                  ),
              ],
            ),
          ),
          SizedBox(height: 12), // 减小间距
          // 优化商品信息区域排版
          Container(
            padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8), 
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 商品名称移至条形码前面显示
                _buildInfoRow(
                  title:
                  '${(_printTitles?['product_name']?.isNotEmpty ?? false) ? _printTitles!['product_name'] : '商品名称'}: ',
                  value: _articuloData?['NombreES']?.toString() ?? '无商品名称',
                  fontSize: 18,
                  boldValue: true, // 加粗显示
                ),
                // 添加中文名称显示
                if (_articuloData != null && 
                    _articuloData!['NombreCN'] != null && 
                    _articuloData!['NombreCN'].toString().isNotEmpty)
                  Column(
                    children: [
                      _buildInfoRow(
                        title: '中文名称: ',
                        value: _articuloData!['NombreCN'].toString(),
                        fontSize: 16,
                        valueColor: Colors.blue[700],
                      ),
                      SizedBox(height: 10), // 增加间距
                    ],
                  ),
                
                // 添加供应商信息显示
                if (_proveedorData != null && 
                    ((_proveedorData!['NombreCN'] != null && 
                      _proveedorData!['NombreCN'].toString().isNotEmpty && 
                      _proveedorData!['NombreCN'].toString() != '未知供应商') ||
                     (_proveedorData!['Nombre'] != null && 
                      _proveedorData!['Nombre'].toString().isNotEmpty && 
                      _proveedorData!['Nombre'].toString() != '未知供应商')))
                  Column(
                    children: [
                      _buildInfoRow(
                        title: '供应商: ',
                        value: (_proveedorData!['NombreCN'] != null && _proveedorData!['NombreCN'].toString().isNotEmpty) 
                               ? _proveedorData!['NombreCN'].toString() 
                               : _proveedorData!['Nombre'].toString(),
                        fontSize: 16,
                        valueColor: Colors.green[700],
                      ),
                      SizedBox(height: 10), // 增加间距
                    ],
                  ),
                
                SizedBox(height: 10), // 增加间距
                // 条形码显示
                Row(
                  mainAxisAlignment: MainAxisAlignment.center, // 居中显示
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: BarcodeWidget(
                        barcode: Barcode.code128(),
                        data: _articuloData!['CodigoBarra'].toString(),
                        width: 150,
                        height: 50,
                        drawText: true,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }


  Widget _buildInfoRow({
    required String title,
    required String value,
    Color? valueColor,
    bool underlineValue = false,
    double fontSize = 22,
    bool boldValue = false,
    VoidCallback? onTap,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(fontSize: fontSize, fontWeight: FontWeight.bold),
        ),
        Expanded(
          child: GestureDetector(
            onTap: onTap,
            child: Text(
              value,
              style: TextStyle(
                fontSize: fontSize,
                color: valueColor ?? Colors.black,
                fontWeight: boldValue ? FontWeight.bold : FontWeight.normal,
                decoration: onTap != null ? TextDecoration.underline : (underlineValue ? TextDecoration.underline : TextDecoration.none),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        ElevatedButton.icon(
          onPressed: _showPrintDialog,
          icon: Icon(Icons.print, size: 24),
          label: Text('标签打印', style: TextStyle(fontSize: 20)),
          style: ElevatedButton.styleFrom(
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 5,
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
        ),
        ElevatedButton.icon(
          onPressed: _poderMantenerArticulo == -1
              ? () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => EditArticuloScreen(
                  articuloData: _articuloData!, // 将商品信息传递给编辑页面
                ),
              ),
            ).then((result) {
              // 用户返回后请求焦点
              if (result == true) {
                // 设置标记，表示从编辑页面返回
                _isReturningFromEdit = true;
                print('从编辑页面返回，设置标记防止自动打印');
                
                // 如果编辑成功，重新获取商品信息
                _fetchData(_articuloData!['ArticuloID'].toString()).then((_) {
                  // 在_fetchData完成后，使用双重延迟确保焦点回到输入框
                  Future.delayed(Duration(milliseconds: 150), () {
                    if (mounted) {
                      FocusScope.of(context).unfocus();
                      Future.delayed(Duration(milliseconds: 50), () {
                        if (mounted) {
                          FocusScope.of(context).requestFocus(_focusNode);
                        }
                      });
                    }
                  });
                });
              } else {
                // 如果没有编辑或编辑未成功，也需要请求焦点
                Future.delayed(Duration(milliseconds: 150), () {
                  if (mounted) {
                    FocusScope.of(context).unfocus();
                    Future.delayed(Duration(milliseconds: 50), () {
                      if (mounted) {
                        FocusScope.of(context).requestFocus(_focusNode);
                      }
                    });
                  }
                });
              }
            });
          }
              : null,
          icon: Icon(Icons.edit, size: 24),
          label: Text('修改商品', style: TextStyle(fontSize: 20)),
          style: ElevatedButton.styleFrom(
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 5,
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  Future<void> _loadFabPosition() async {
    _prefs = await SharedPreferences.getInstance();
    double? dx = _prefs.getDouble('fab_dx');
    double? dy = _prefs.getDouble('fab_dy');

    if (dx != null && dy != null) {
      setState(() {
        _fabPosition = Offset(dx, dy);
      });
    } else {
      // 如果没有保存过位置，使用货币符号右侧和条码上方的默认位置
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          final screenWidth = MediaQuery.of(context).size.width;
          setState(() {
            _fabPosition = Offset(screenWidth - 150, 250); // 调整Y轴位置到价格区域下方，条码上方
          });
          // 保存默认位置
          _saveFabPosition(_fabPosition);
        }
      });
    }
  }

  Future<void> _saveFabPosition(Offset position) async {
    await _prefs.setDouble('fab_dx', position.dx);
    await _prefs.setDouble('fab_dy', position.dy);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('标签打印'),
        actions: [
          IconButton(
            icon: Icon(Icons.camera_alt),
            onPressed: () {
              _scanBarcode();
            },
          ),
          IconButton(
            icon: Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PrintSettingsScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        child: Stack(
          children: [
            SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  if (_articuloData != null) _buildActionButtons(),
                  SizedBox(height: 20),
                  TextField(
                    controller: _controller,
                    focusNode: _focusNode,
                    keyboardType: TextInputType.none,  // 禁用系统键盘
                    showCursor: true,
                    decoration: InputDecoration(
                      labelText: '产品扫描',
                      labelStyle: TextStyle(fontSize: 22, color: Colors.blue),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10.0),
                      ),
                      suffixIcon: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // 添加清除按钮
                          if (_controller.text.isNotEmpty)
                            IconButton(
                              icon: Icon(Icons.clear),
                              onPressed: () {
                                setState(() {
                                  _controller.clear();
                                  _articuloData = null; // 清除当前显示的商品数据
                                });
                                _requestInputFocus(); // 清除后保持焦点
                              },
                            ),
                          // 保留原有的键盘按钮
                          IconButton(
                            icon: Icon(
                              _showCustomKeyboard ? Icons.keyboard_hide : Icons.keyboard,
                              size: 30,
                            ),
                            onPressed: () {
                              setState(() {
                                _showCustomKeyboard = !_showCustomKeyboard;
                              });
                              // 切换键盘后保持焦点
                              if (!_showCustomKeyboard) {
                                _requestInputFocus();
                              }
                            },
                          ),
                        ],
                      ),
                    ),
                    style: TextStyle(fontSize: 20),
                    onTap: () {
                      // 点击输入框时只获取焦点，不显示键盘
                      FocusScope.of(context).requestFocus(_focusNode);
                    },
                    onSubmitted: (value) {
                      if (value.isNotEmpty) {
                        _fetchData(value);
                      }
                    },
                    // 添加输入监听，在输入长度超过一定值时自动搜索
                    onChanged: (value) {
                      // 严格检查：同时确认没有扫码和打印锁定
                      if (!_preventDuplicateQuery && !_isPrintingLocked) {
                        // 如果是条形码输入（通常会快速输入13-14位数字）
                        if (value.length >= 13 && RegExp(r'^\d+$').hasMatch(value)) {
                          print('通过手动输入触发条码查询: $value');
                          _fetchData(value);
                        }
                      } else {
                        print('忽略输入框onChange事件，防重复查询=${_preventDuplicateQuery}，打印锁定=${_isPrintingLocked}');
                      }
                    },
                  ),
                  SizedBox(height: 30),
                  _isLoading
                      ? CircularProgressIndicator()
                      : _articuloData != null
                          ? Column(
                              children: [
                                _buildArticuloInfo(),
                                SizedBox(height: 30),
                              ],
                            )
                          : Container(),
                  // 添加底部间距以防止内被键盘遮挡
                  SizedBox(height: _showCustomKeyboard ? 280 : 100),
                ],
              ),
            ),
            // 库存按钮层
            if (_articuloData != null)
              Positioned(
                left: _fabPosition.dx,
                top: _fabPosition.dy,
                child: GestureDetector(
                  onPanUpdate: (details) {
                    setState(() {
                      _fabPosition += details.delta;
                      final RenderBox renderBox = context.findRenderObject() as RenderBox;
                      final Size screenSize = renderBox.size;
                      double newX = _fabPosition.dx;
                      double newY = _fabPosition.dy;
                      
                      // 限制水平范围，确保按钮不会超出边界
                      if (newX < 0) newX = 0;
                      if (newX > screenSize.width - 150) newX = screenSize.width - 100;
                      
                      // 限制垂直范围，确保按钮在价格区域下方、条码上方区域
                      // 价格区域下方约为230px，条码上方限制为300px
                      if (newY < 200) newY = 200;
                      if (newY > 300) newY = 300;
                      
                      _fabPosition = Offset(newX, newY);
                    });
                  },
                  onPanEnd: (details) {
                    _saveFabPosition(_fabPosition);
                  },
                  child: FutureBuilder<int?>(
                    future: DatabaseServiceSN.getBohaoValue(),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return CircularProgressIndicator();
                      } else if (snapshot.hasError) {
                        return Text('Error fetching bohao value');
                      } else {
                        final bohao = snapshot.data ?? 0;
                        final stockField = bohao == 1 ? 'stock' : 'Stock';
                        return FloatingActionButton.extended(
                          onPressed: _poderCambioStock == -1 ? _showStockEditDialog : null,
                          label: Row(
                            children: [
                              Text(
                                '库存: ${(double.tryParse(_articuloData?[stockField]?.toString() ?? '0') ?? 0).toInt()}',
                                style: TextStyle(fontSize: 16),
                              ),
                              SizedBox(width: 5),
                              Icon(Icons.drag_indicator, size: 14, color: Colors.white70), // 添加拖动指示图标
                            ],
                          ),
                          tooltip: '修改库存 (可拖动按钮位置)',
                          backgroundColor: _poderCambioStock == -1 ? Colors.orange : Colors.grey,
                        );
                      }
                    },
                  ),
                ),
              ),
            // 自定义键盘层（最上层）
            if (_showCustomKeyboard)
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: Material(
                  elevation: 8,
                  child: CustomKeyboard(
                    controller: _controller,
                    showKeyboard: _showCustomKeyboard,
                    onSubmit: (value) {
                      _fetchData(value);
                      _controller.clear();
                      setState(() {
                        _showCustomKeyboard = false;
                      });
                      FocusScope.of(context).unfocus();
                    },
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

