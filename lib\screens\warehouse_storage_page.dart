import 'package:flutter/material.dart';
import 'package:mistoer/services/scan_api_service.dart'; // 导入 ApiService
import 'package:mistoer/services/database_service.dart';
import 'dart:math' as math;
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:mistoer/widgets/custom_keyboard.dart'; // 导入自定义键盘组件

class WarehouseStoragePage extends StatefulWidget {
  @override
  _WarehouseStoragePageState createState() => _WarehouseStoragePageState();
}

class _WarehouseStoragePageState extends State<WarehouseStoragePage> {
  final TextEditingController inputController = TextEditingController();
  final TextEditingController weizhiController = TextEditingController();
  final TextEditingController cantidadController = TextEditingController();
  final FocusNode _inputFocusNode = FocusNode(); // 添加FocusNode专门用于条码/货号输入框
  final FocusNode _weizhiFocusNode = FocusNode(); // 添加FocusNode用于位置输入框
  final FocusNode _cantidadFocusNode = FocusNode(); // 添加FocusNode用于数量输入框

  String resultMessage = '';
  List<Map<String, dynamic>> queryResults = [];
  List<String> weizhiHistory = []; // 用于保存位置输入的历史记录
  String? username;
  
  // 新增状态变量
  Map<String, dynamic> currentItemLocations = {};
  bool isLoadingLocations = false;
  List<String> availableLocations = []; // 存储可用的库位选择
  bool isCustomKeyboardVisible = false; // 控制自定义键盘显示状态，默认不显示
  TextEditingController? activeController; // 当前活动的输入框控制器
  String? selectedLocation; // 跟踪当前选中的库位
  String? editingLocationType; // 跟踪当前正在编辑的库位类型 (weizhi, weizhi_1, weizhi_2)

  @override
  void initState() {
    super.initState();
    _loadUsername();
    
    // 设置初始活动控制器为条码/货号输入框
    activeController = inputController;
    // 默认显示自定义键盘
    isCustomKeyboardVisible = true;
    
    // 添加位置输入框的监听器，实现精准匹配库位
    weizhiController.addListener(_matchLocationPosition);
    
    // 设置焦点到输入框
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 直接将焦点设置到条码/货号输入框
      _inputFocusNode.requestFocus();
    });
  }

  Future<void> _loadUsername() async {
    final db = await DatabaseService.database;
    final List<Map<String, dynamic>> result = await db.query(
      'empleados',
      where: 'in_use = ?',
      whereArgs: [-1],
    );

    if (result.isNotEmpty) {
      setState(() {
        username = result.first['Nombre'];
      });
    } else {
      setState(() {
        username = "未登录";
      });
    }
  }

  void _addWeizhiToHistory(String weizhi) {
    if (weizhi.isNotEmpty && !weizhiHistory.contains(weizhi)) {
      setState(() {
        weizhiHistory.add(weizhi);
        if (weizhiHistory.length > 3) {
          weizhiHistory.removeAt(0); // 保证历史记录最多3条
        }
      });
    }
  }

  // 添加一个计算总箱数的辅助方法
  void _updateTotalCaja() {
    // 只在有当前商品位置信息时进行计算
    if (currentItemLocations.isEmpty) return;
    
    // 将NULL视为0并计算总和
    int caja = currentItemLocations['caja'] != null ? 
      (int.tryParse(currentItemLocations['caja'].toString()) ?? 0) : 0;
    int caja1 = currentItemLocations['caja1'] != null ? 
      (int.tryParse(currentItemLocations['caja1'].toString()) ?? 0) : 0;
    int caja2 = currentItemLocations['caja2'] != null ? 
      (int.tryParse(currentItemLocations['caja2'].toString()) ?? 0) : 0;
    
    // 更新总箱数
    currentItemLocations['total_caja'] = caja + caja1 + caja2;
    
    // 同时更新查询结果中的总箱数
    for (var i = 0; i < queryResults.length; i++) {
      if (queryResults[i]['ArticuloID'] == currentItemLocations['ArticuloID']) {
        int itemCaja = queryResults[i]['caja'] != null ? 
          (int.tryParse(queryResults[i]['caja'].toString()) ?? 0) : 0;
        int itemCaja1 = queryResults[i]['caja1'] != null ? 
          (int.tryParse(queryResults[i]['caja1'].toString()) ?? 0) : 0;
        int itemCaja2 = queryResults[i]['caja2'] != null ? 
          (int.tryParse(queryResults[i]['caja2'].toString()) ?? 0) : 0;
        
        queryResults[i]['total_caja'] = itemCaja + itemCaja1 + itemCaja2;
      }
    }
  }

  void _bigquery() async {
    final input = inputController.text.trim();
    if (input.isEmpty) {
      setState(() {
        resultMessage = '请输入货号或条码';
      });
      _clearResultMessageAfterDelay();
      return;
    }

    // 判断输入是货号还是条码
    String queryType = input.length > 10 ? 'CodigoBarra' : 'ArticuloID';

    try {
      final results = await ScanApiService.bigstockqueryDatabase(input, queryType);
      setState(() {
        queryResults = results;
        resultMessage = '';

        // 查询完成后将第一个结果更新到库位信息
        if (results.isNotEmpty) {
          currentItemLocations = results.first;
          _updateTotalCaja(); // 更新总箱数
          _updateAvailableLocations(); // 确保更新可用库位列表
        }

        // 查询完成后清空输入框
        inputController.clear();
      });
      
      // 查询成功后，设置焦点回到条码/货号输入框
      _inputFocusNode.requestFocus();
      
    } catch (e) {
      setState(() {
        resultMessage = '查询失败：$e';

        // 即使查询失败，也清空输入框
        inputController.clear();
      });
      
      // 查询失败后，设置焦点回到条码/货号输入框
      _inputFocusNode.requestFocus();
    }
    _clearResultMessageAfterDelay();
  }

  void _register() async {
    final input = inputController.text.trim();
    final weizhi = weizhiController.text.trim();
    final cantidad = cantidadController.text.trim();

    // 添加调试输出
    print('【入库调试】开始入库操作:');
    print('【入库调试】输入值: $input');
    print('【入库调试】库位: $weizhi');
    print('【入库调试】数量: $cantidad');
    print('【入库调试】编辑状态: ${editingLocationType != null ? '正在编辑 $editingLocationType' : '未处于编辑状态'}');

    if (input.isEmpty) {
      setState(() {
        resultMessage = '请输入货号或条码';
      });
      print('【入库调试】错误: 未输入货号或条码');
      _clearResultMessageAfterDelay();
      return;
    }
    if (weizhi.isEmpty || cantidad.isEmpty) {
      setState(() {
        resultMessage = '请输入位置和箱数';
      });
      print('【入库调试】错误: 未输入位置或箱数');
      _clearResultMessageAfterDelay();
      return;
    }

    String queryType = input.length > 10 ? 'CodigoBarra' : 'ArticuloID';
    print('【入库调试】查询类型: $queryType');

    // 确定应该使用哪个库位字段
    String locationType = 'weizhi'; // 默认为主库位
    
    // 优先检查是否处于编辑模式，如果是，则使用正在编辑的库位类型
    if (editingLocationType != null) {
      locationType = editingLocationType!;
      print('【入库调试】当前处于编辑模式，使用编辑中的库位类型: $locationType');
    } else if (selectedLocation != null) {
      // 如果有选中的库位，优先使用选中的库位对应的类型
      if (currentItemLocations.isNotEmpty) {
        if (selectedLocation == currentItemLocations['weizhi_1']) {
          locationType = 'weizhi_1';
          print('【入库调试】使用选中的库位1 (weizhi_1): $selectedLocation');
        } else if (selectedLocation == currentItemLocations['weizhi_2']) {
          locationType = 'weizhi_2';
          print('【入库调试】使用选中的库位2 (weizhi_2): $selectedLocation');
        } else if (selectedLocation == currentItemLocations['weizhi']) {
          locationType = 'weizhi';
          print('【入库调试】使用选中的主备 (weizhi): $selectedLocation');
        } else {
          // 选中的库位与已知库位不匹配，默认使用主库位
          locationType = 'weizhi';
          print('【入库调试】选中的库位与已知库位不匹配，使用主备 (weizhi)');
        }
      }
    } else {
      // 检查是否已有该商品的查询结果
      var existingItem = queryResults.firstWhere(
        (item) => item[queryType] == input,
        orElse: () => <String, dynamic>{},
      );
      
      if (existingItem.isNotEmpty) {
        print('【入库调试】找到现有商品记录:');
        print('【入库调试】ArticuloID: ${existingItem['ArticuloID']}');
        print('【入库调试】商品名称: ${existingItem['name'] ?? '未知'}');
        
        // 直接根据输入的库位值自动匹配对应的位置类型
        if (weizhi == existingItem['weizhi_1']) {
          locationType = 'weizhi_1';
          print('【入库调试】匹配到库位1 (weizhi_1): $weizhi');
        } else if (weizhi == existingItem['weizhi_2']) {
          locationType = 'weizhi_2';
          print('【入库调试】匹配到库位2 (weizhi_2): $weizhi');
        } else if (weizhi == existingItem['weizhi']) {
          locationType = 'weizhi';
          print('【入库调试】匹配到主备 (weizhi): $weizhi');
        } else {
          // 默认使用主库位(weizhi)
          locationType = 'weizhi';
          print('【入库调试】使用主备 (weizhi): $weizhi');
        }
      } else {
        print('【入库调试】未找到现有商品记录，将使用默认主备');
      }
    }

    try {
      print('【入库调试】调用入库API: checkinItem');
      print('【入库调试】参数: 商品=$input, 库位=$weizhi, 数量=$cantidad, 查询类型=$queryType, 库位类型=$locationType');
      
      setState(() {
        resultMessage = '正在处理入库请求...';
      });
      
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text('处理中'),
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 20),
                Text('正在处理入库请求...')
              ],
            ),
          );
        },
      );
      
      final results = await ScanApiService.checkinItem(
        input, 
        weizhi, 
        cantidad, 
        queryType, 
        location_type: locationType
      ).catchError((error) {
        print('【入库调试】API调用异常: $error');
        // 关闭加载对话框
        Navigator.of(context, rootNavigator: true).pop();
        throw error; // 重新抛出异常以便后续处理
      });
      
      // 关闭加载对话框
      Navigator.of(context, rootNavigator: true).pop();
      
      print('【入库调试】入库API调用成功，返回结果数: ${results.length}');
      
      setState(() {
        // 清除编辑状态
        editingLocationType = null;
        
        resultMessage = '入库成功：${locationType == 'weizhi' ? '主备' : locationType == 'weizhi_1' ? '备1' : '备2'}';
        resultMessage += ' (商品: $input, 库位: $weizhi, 数量: $cantidad)';
        queryResults = results;

        // 入库成功后更新当前商品位置信息
        if (results.isNotEmpty) {
          currentItemLocations = results.first;
          print('【入库调试】更新当前商品位置信息:');
          print('【入库调试】主库位: ${currentItemLocations['weizhi'] ?? '无'}, 数量: ${currentItemLocations['caja'] ?? 0}');
          print('【入库调试】库位1: ${currentItemLocations['weizhi_1'] ?? '无'}, 数量: ${currentItemLocations['caja1'] ?? 0}');
          print('【入库调试】库位2: ${currentItemLocations['weizhi_2'] ?? '无'}, 数量: ${currentItemLocations['caja2'] ?? 0}');
          _updateTotalCaja(); // 更新总箱数
          print('【入库调试】总箱数更新为: ${currentItemLocations['total_caja']}');
          _updateAvailableLocations(); // 确保可用库位列表更新
        }

        // 只清空位置和数量输入框，保留条码/货号输入框的内容
        // inputController.clear(); - 不再清空条码/货号输入框
        cantidadController.clear();
        _addWeizhiToHistory(weizhi);
        weizhiController.clear(); // 清空位置输入框
        
        // 清除选中状态
        selectedLocation = null;
      });
      
      // 操作成功后，强制重新构建UI以更新库位按钮上的数量显示
      setState(() {});
      
      // 操作成功后，设置焦点到位置输入框
      FocusScope.of(context).requestFocus(FocusNode());
      print('【入库调试】入库操作完成');
      
      _clearResultMessageAfterDelay();
    } catch (e) {
      print('【入库调试】入库操作失败，错误: $e');
      
      // 创建更友好的错误信息
      String errorMessage = e.toString();
      String userMessage = '入库失败';
      
      // 检查是否包含服务器错误信息
      if (errorMessage.contains("500")) {
        userMessage = '服务器处理入库请求时出错，请检查网络连接或联系管理员。';
        print('【入库调试】检测到500服务器错误，可能的原因：');
        print('【入库调试】1. API端点不存在或URL错误');
        print('【入库调试】2. 服务器内部错误，如数据库操作失败');
        print('【入库调试】3. 参数格式不正确或缺少必要参数');
        print('【入库调试】4. 服务器资源不足或超时');
      } else if (errorMessage.contains("无法获取商品信息")) {
        userMessage = '该商品信息不存在，请先添加商品基本信息';
      } else if (errorMessage.contains("商品插入库存系统失败")) {
        userMessage = '无法将商品添加到库存系统，请联系管理员';
      }
      
      // 检查是否有特定于库位的提示
      if (errorMessage.contains("提示:")) {
        // 保留该提示，它通常包含关于使用主库位的建议
        int tipsIndex = errorMessage.indexOf("提示:");
        if (tipsIndex > 0) {
          String tips = errorMessage.substring(tipsIndex);
          userMessage += '\n' + tips;
        }
      }
      
      setState(() {
        resultMessage = userMessage;
        
        // 只清空位置和数量输入框，保留条码/货号输入框的内容
        // inputController.clear(); - 不再清空条码/货号输入框
        cantidadController.clear();
        
        // 入库失败也清除编辑状态
        editingLocationType = null;
      });
      
      // 显示更详细的错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(userMessage),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 5),
          action: SnackBarAction(
            label: locationType != 'weizhi' ? '使用主库位' : '重试',
            onPressed: () {
              if (locationType != 'weizhi') {
                // 如果不是主库位操作失败，提供使用主库位重试的选项
                setState(() {
                  // 切换到主库位
                  String mainLocation = currentItemLocations['weizhi'] ?? '';
                  if (mainLocation.isNotEmpty) {
                    weizhiController.text = mainLocation;
                    // 选中主库位
                    selectedLocation = mainLocation;
                    // 清除编辑状态
                    editingLocationType = null;
                  }
                });
              }
              // 重新尝试入库操作
              _register();
            },
          ),
        ),
      );
      
      // 操作失败后，也强制设置焦点到条码/货号输入框
      _inputFocusNode.requestFocus();
      
      _clearResultMessageAfterDelay();
    }
  }

  void _checkout() async {
    final input = inputController.text.trim();
    final cantidad = cantidadController.text.trim();
    final weizhi = weizhiController.text.trim();

    // 添加调试输出
    print('【出库调试】开始出库操作:');
    print('【出库调试】输入值: $input');
    print('【出库调试】库位: $weizhi');
    print('【出库调试】数量: $cantidad');
    print('【出库调试】编辑状态: ${editingLocationType != null ? '正在编辑 $editingLocationType' : '未处于编辑状态'}');

    if (input.isEmpty) {
      setState(() {
        resultMessage = '请输入货号或条码';
      });
      print('【出库调试】错误: 未输入货号或条码');
      _clearResultMessageAfterDelay();
      return;
    }
    if (cantidad.isEmpty || weizhi.isEmpty) {
      setState(() {
        resultMessage = '请输入箱数和位置';
      });
      print('【出库调试】错误: 未输入位置或箱数');
      _clearResultMessageAfterDelay();
      return;
    }

    String queryType = input.length > 10 ? 'CodigoBarra' : 'ArticuloID';
    int cantidadInt = int.tryParse(cantidad) ?? 0; // 将输入的数量转换为整数
    print('【出库调试】查询类型: $queryType, 出库数量(整数): $cantidadInt');

    // 确定库位类型
    String locationType = 'weizhi'; // 默认为主库位
    
    // 根据编辑状态或选中的库位确定库位类型
    if (editingLocationType != null) {
      locationType = editingLocationType!;
      print('【出库调试】使用编辑中的库位类型: $locationType');
    } else if (selectedLocation != null) {
      // 根据选中的库位确定库位类型
      if (selectedLocation == currentItemLocations['weizhi_1']) {
        locationType = 'weizhi_1';
        print('【出库调试】使用选中的库位1 (weizhi_1): $selectedLocation');
      } else if (selectedLocation == currentItemLocations['weizhi_2']) {
        locationType = 'weizhi_2';
        print('【出库调试】使用选中的库位2 (weizhi_2): $selectedLocation');
      } else if (selectedLocation == currentItemLocations['weizhi']) {
        locationType = 'weizhi';
        print('【出库调试】使用选中的主备 (weizhi): $selectedLocation');
      } else {
        // 选中的库位与已知库位不匹配，默认使用主库位
        locationType = 'weizhi';
        print('【出库调试】选中的库位与已知库位不匹配，使用主备 (weizhi)');
      }
    } else {
      // 直接根据输入的库位值自动匹配对应的位置类型
      if (currentItemLocations.isNotEmpty) {
        if (weizhi == currentItemLocations['weizhi_1']) {
          locationType = 'weizhi_1';
          print('【出库调试】匹配到库位1 (weizhi_1): $weizhi');
        } else if (weizhi == currentItemLocations['weizhi_2']) {
          locationType = 'weizhi_2';
          print('【出库调试】匹配到库位2 (weizhi_2): $weizhi');
        } else if (weizhi == currentItemLocations['weizhi']) {
          locationType = 'weizhi';
          print('【出库调试】匹配到主备 (weizhi): $weizhi');
        } else {
          print('【出库调试】使用主备 (weizhi): $weizhi');
        }
      }
    }
    
    try {
      print('【出库调试】调用出库API: checkout');
      print('【出库调试】参数: 商品=$input, 数量=$cantidad, 查询类型=$queryType, 库位类型=$locationType');
      
      setState(() {
        resultMessage = '正在处理出库请求...';
      });
      
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text('处理中'),
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 20),
                Text('正在处理出库请求...')
              ],
            ),
          );
        },
      );
      
      // 直接调用API，不依赖queryResults
      final updatedResults = await ScanApiService.checkout(
        input, 
        cantidad, 
        queryType, 
        location_type: locationType
      ).catchError((error) {
        print('【出库调试】API调用异常: $error');
        // 关闭加载对话框
        Navigator.of(context, rootNavigator: true).pop();
        throw error; // 重新抛出异常以便后续处理
      });

      // 关闭加载对话框
      Navigator.of(context, rootNavigator: true).pop();
      
      print('【出库调试】出库API调用成功，返回结果数: ${updatedResults.length}');
      
      setState(() {
        // 清除编辑状态
        editingLocationType = null;
        
        resultMessage = '出库成功';
        resultMessage += ' (商品: $input, 库位: $weizhi, 数量: $cantidad)';
        
        // 更新查询结果
        if (updatedResults.isNotEmpty) {
          queryResults = updatedResults;
          currentItemLocations = updatedResults.first;
          
          print('【出库调试】更新当前商品位置信息:');
          print('【出库调试】主库位: ${currentItemLocations['weizhi'] ?? '无'}, 数量: ${currentItemLocations['caja'] ?? 0}');
          print('【出库调试】库位1: ${currentItemLocations['weizhi_1'] ?? '无'}, 数量: ${currentItemLocations['caja1'] ?? 0}');
          print('【出库调试】库位2: ${currentItemLocations['weizhi_2'] ?? '无'}, 数量: ${currentItemLocations['caja2'] ?? 0}');
          
          _updateTotalCaja(); // 更新总箱数
          print('【出库调试】总箱数更新为: ${currentItemLocations['total_caja']}');
          _updateAvailableLocations(); // 确保可用库位列表更新
        }
        
        // 只清空位置和数量输入框，保留条码/货号输入框的内容
        // inputController.clear(); - 不再清空条码/货号输入框
        cantidadController.clear();
        _addWeizhiToHistory(weizhi);
        weizhiController.clear(); // 清空位置输入框
        
        // 清除选中状态
        selectedLocation = null;
      });
      
      // 操作成功后，强制重新构建UI以更新库位按钮上的数量显示
      setState(() {});

      // 操作成功后，设置焦点到位置输入框
      FocusScope.of(context).requestFocus(FocusNode());
      print('【出库调试】出库操作完成');
      
      _clearResultMessageAfterDelay();
    } catch (e) {
      print('【出库调试】出库操作失败，错误: $e');
      
      // 创建更友好的错误信息
      String errorMessage = e.toString();
      String userMessage = '出库失败';
      
      // 检查是否包含服务器错误信息
      if (errorMessage.contains("500")) {
        userMessage = '服务器处理出库请求时出错，请检查网络连接或联系管理员。';
        print('【出库调试】检测到500服务器错误，可能的原因：');
        print('【出库调试】1. API端点不存在或URL错误');
        print('【出库调试】2. 服务器内部错误，如数据库操作失败');
        print('【出库调试】3. 参数格式不正确或缺少必要参数');
        print('【出库调试】4. 服务器资源不足或超时');
      } else if (errorMessage.contains("库位") && errorMessage.contains("库存不足")) {
        // 提取库存不足的错误信息
        userMessage = errorMessage.contains("出库失败:") ? errorMessage.substring(errorMessage.indexOf("出库失败:") + 5) : errorMessage;
      }
      
      // 检查是否有特定于库位的提示
      if (errorMessage.contains("提示:")) {
        // 保留该提示，它通常包含关于使用主库位的建议
        int tipsIndex = errorMessage.indexOf("提示:");
        if (tipsIndex > 0) {
          String tips = errorMessage.substring(tipsIndex);
          // 替换提示中的库位名称
          tips = tips.replaceAll("主库位", "主备").replaceAll("备货1", "备1").replaceAll("备货2", "备2");
          userMessage += '\n' + tips;
        }
      }
      
      setState(() {
        resultMessage = userMessage;
        
        // 只清空位置和数量输入框，保留条码/货号输入框的内容
        // inputController.clear(); - 不再清空条码/货号输入框
        cantidadController.clear();
        
        // 出库失败也清除编辑状态
        editingLocationType = null;
      });
      
      // 显示更详细的错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(userMessage),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 5),
          action: SnackBarAction(
            label: locationType != 'weizhi' ? '使用主库位' : '重试',
            onPressed: () {
              if (locationType != 'weizhi') {
                // 如果不是主库位操作失败，提供使用主库位重试的选项
                setState(() {
                  // 切换到主库位
                  String mainLocation = currentItemLocations['weizhi'] ?? '';
                  if (mainLocation.isNotEmpty) {
                    weizhiController.text = mainLocation;
                    // 选中主库位
                    selectedLocation = mainLocation;
                    // 清除编辑状态
                    editingLocationType = null;
                  }
                });
              }
              // 重新尝试出库操作
              _checkout();
            },
          ),
        ),
      );
      
      // 操作失败后，也强制设置焦点到条码/货号输入框
      _inputFocusNode.requestFocus();
      
      _clearResultMessageAfterDelay();
    }
  }

  // 延迟清除 resultMessage
  void _clearResultMessageAfterDelay([int seconds = 2]) {
    Future.delayed(Duration(seconds: seconds), () {
      if (mounted) {
        setState(() {
          resultMessage = ''; // N秒后清空 resultMessage
        });
      }
    });
  }

  // 弹出库存不足的对话框
  void _showStockAlertDialog(String currentStock, String requestedAmount) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('库存不足'),
          content: Text('当前库存: $currentStock，出库数量: $requestedAmount'),
          actions: <Widget>[
            TextButton(
              child: Text('确定'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildQueryResults() {
    if (queryResults.isEmpty) {
      return SizedBox();
    }

    // 更新所有查询结果中的总箱数
    for (var i = 0; i < queryResults.length; i++) {
      int itemCaja = queryResults[i]['caja'] != null ? 
        (int.tryParse(queryResults[i]['caja'].toString()) ?? 0) : 0;
      int itemCaja1 = queryResults[i]['caja1'] != null ? 
        (int.tryParse(queryResults[i]['caja1'].toString()) ?? 0) : 0;
      int itemCaja2 = queryResults[i]['caja2'] != null ? 
        (int.tryParse(queryResults[i]['caja2'].toString()) ?? 0) : 0;
      
      queryResults[i]['total_caja'] = itemCaja + itemCaja1 + itemCaja2;
    }

    return Container(
      width: double.infinity,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columnSpacing: 10.0, // 减小列间距以减少总宽度
          horizontalMargin: 5.0, // 减小水平边距
          dataRowHeight: 48.0, // 保持行高
          columns: [
            DataColumn(
              label: Container(
                width: 60, // 设置固定宽度
                child: Text(
                  '位置',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            DataColumn(
              label: Container(
                width: 60, // 设置固定宽度
                child: Text(
                  '货号',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            DataColumn(
              label: Container(
                width: 70, // 设置固定宽度
                child: Text(
                  '条码',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            DataColumn(
              label: Container(
                width: 45, // 设置固定宽度
                child: Text(
                  '主备',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            DataColumn(
              label: Container(
                width: 45, // 设置固定宽度
                child: Text(
                  '备1',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            DataColumn(
              label: Container(
                width: 45, // 设置固定宽度
                child: Text(
                  '备2',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            DataColumn(
              label: Container(
                width: 45, // 设置固定宽度
                child: Text(
                  '总箱数',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: Colors.red),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
          rows: queryResults.map((item) {
            // 确保数据不为空
            String weizhi = item['weizhi'] ?? '';
            String articuloID = item['ArticuloID'] ?? '';
            String codigoBarra = item['CodigoBarra'] ?? '';
            String caja = (item['caja'] ?? 0).toString();
            String caja1 = (item['caja1'] ?? 0).toString();
            String caja2 = (item['caja2'] ?? 0).toString();
            String totalCaja = (item['total_caja'] ?? 0).toString();
            
            return DataRow(cells: [
              DataCell(Container(
                width: 60,
                child: Text(weizhi, 
                  style: TextStyle(fontSize: 13),
                  overflow: TextOverflow.ellipsis,
                ),
              )),
              DataCell(Container(
                width: 60,
                child: Text(articuloID, 
                  style: TextStyle(fontSize: 13),
                  overflow: TextOverflow.ellipsis,
                ),
              )),
              DataCell(Container(
                width: 70,
                child: Text(codigoBarra, 
                  style: TextStyle(fontSize: 13),
                  overflow: TextOverflow.ellipsis,
                ),
              )),
              DataCell(Container(
                width: 45,
                child: Text(caja, 
                  style: TextStyle(fontSize: 13),
                  textAlign: TextAlign.center,
                ),
              )),
              DataCell(Container(
                width: 45,
                child: Text(caja1, 
                  style: TextStyle(fontSize: 13),
                  textAlign: TextAlign.center,
                ),
              )),
              DataCell(Container(
                width: 45,
                child: Text(caja2, 
                  style: TextStyle(fontSize: 13),
                  textAlign: TextAlign.center,
                ),
              )),
              DataCell(Container(
                width: 45,
                child: Text(
                  totalCaja, 
                  style: TextStyle(fontSize: 13, fontWeight: FontWeight.bold, color: Colors.red),
                  textAlign: TextAlign.center,
                ),
              )),
            ]);
          }).toList(),
        ),
      ),
    );
  }

  // 修改 _onInputChanged 方法，移除自动查询逻辑
  void _onInputChanged() {
    // 仅保留必要的状态清理功能，移除自动查询逻辑
    setState(() {
      currentItemLocations = {};
      availableLocations = [];
    });
  }
  
  // 获取商品库位信息的方法
  Future<void> _fetchItemLocations(String input, {bool showInsertDialog = false}) async {
    if (input.isEmpty) return;
    
    setState(() {
      isLoadingLocations = true;
      resultMessage = '正在查询...';
    });
    
    try {
      // 判断输入是货号还是条码
      String queryType = input.length > 10 ? 'CodigoBarra' : 'ArticuloID';
      
      // 查询商品信息
      final results = await ScanApiService.bigstockqueryDatabase(input, queryType);
      
      setState(() {
        if (results.isNotEmpty) {
          currentItemLocations = results.first;
          _updateTotalCaja(); // 更新总箱数
          // 更新可用库位
          _updateAvailableLocations();
          resultMessage = '查询成功';
        } else {
          currentItemLocations = {};
          availableLocations = [];
          resultMessage = '未找到商品记录';
          
          // 如果需要显示插入对话框且没有查询到结果
          if (showInsertDialog) {
            // 延迟显示对话框，确保setState完成
            Future.microtask(() => _showInsertRecordDialog(input));
          }
        }
        isLoadingLocations = false;
      });
    } catch (e) {
      print('获取库位信息失败: $e');
      setState(() {
        currentItemLocations = {};
        availableLocations = [];
        isLoadingLocations = false;
        resultMessage = '查询失败，请重试';
      });
    }
    
    // 2秒后清除结果消息
    _clearResultMessageAfterDelay();
  }
  
  // 显示插入记录对话框
  void _showInsertRecordDialog(String input) {
    // 准备一个编辑控制器，预填当前输入
    final dialogController = TextEditingController(text: input);
    
    // 临时保存当前的键盘状态
    final wasKeyboardVisible = isCustomKeyboardVisible;
    // 隐藏自定义键盘
    setState(() {
      isCustomKeyboardVisible = false;
    });
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('提示'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('无此商品数据，是否需要插入新记录？'),
              SizedBox(height: 10),
              // 使用原生TextField而不是_buildTextField，避免触发自定义键盘
              TextField(
                controller: dialogController,
                decoration: InputDecoration(
                  labelText: '商品编号/ID',
                ),
                // 允许使用系统键盘而不是自定义键盘
                keyboardType: TextInputType.text,
              ),
            ],
          ),
          actions: [
            TextButton(
              child: Text('取消'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('确定'),
              onPressed: () {
                _insertNewRecord(dialogController.text.trim());
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    ).then((_) {
      // 恢复之前的键盘状态
      setState(() {
        isCustomKeyboardVisible = wasKeyboardVisible;
      });
    });
  }
  
  // 插入新记录
  Future<void> _insertNewRecord(String input) async {
    setState(() {
      resultMessage = '正在插入新记录...';
      isLoadingLocations = true;
    });
    
    try {
      // 判断输入是货号还是条码
      bool isBarcode = input.length > 10;
      String queryField = isBarcode ? 'CodigoBarra' : 'ArticuloID';
      
      // 先查询articulo表，获取商品信息
      final matchResults = await ScanApiService.getArticuloInfo(input, queryField);

      if (matchResults.isNotEmpty) {
        final matchResult = matchResults.first;
        // 构建要插入的数据
        String articuloID = (matchResult['ArticuloID'] ?? '').toString();
        String codigoBarra = (matchResult['CodigoBarra'] ?? '').toString();
        
        if (articuloID.isNotEmpty) {
          // 插入到new_stock表
          final success = await ScanApiService.insertToNewStock(articuloID, codigoBarra);
          
          if (success) {
            setState(() {
              resultMessage = '新记录插入成功！';
              // 重新查询库位信息以更新UI
              _fetchItemLocations(isBarcode ? codigoBarra : articuloID);
            });
            
            // 显示快速提示，告知用户已成功插入
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('商品信息已成功添加到库存系统'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 3),
              ),
            );
          } else {
            setState(() {
              resultMessage = '插入失败，请重试！';
              isLoadingLocations = false;
            });
            
            // 显示失败提示
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('商品信息添加失败，请检查网络连接或联系管理员'),
                backgroundColor: Colors.red,
                duration: Duration(seconds: 3),
              ),
            );
          }
        } else {
          setState(() {
            resultMessage = '未能获取有效的商品ID！';
            isLoadingLocations = false;
          });
          
          // 显示数据问题提示
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('商品数据不完整，请检查数据库中的商品信息'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 3),
            ),
          );
        }
      } else {
        setState(() {
          resultMessage = '未找到匹配的商品信息！';
          isLoadingLocations = false;
        });
        
        // 显示商品不存在提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('无法在商品主表中找到此商品，请先添加商品基本信息'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      print('插入过程中出错: $e');
      setState(() {
        resultMessage = '插入失败：$e';
        isLoadingLocations = false;
      });
      
      // 显示异常错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('发生异常错误: ${e.toString().substring(0, math.min(e.toString().length, 100))}...'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    }
    
    _clearResultMessageAfterDelay();
  }

  // 更新可用库位列表
  void _updateAvailableLocations() {
    availableLocations = [];
    
    if (currentItemLocations.isEmpty) return;
    
    String weizhi = currentItemLocations['weizhi']?.toString() ?? '';
    String weizhi1 = currentItemLocations['weizhi_1']?.toString() ?? '';
    String weizhi2 = currentItemLocations['weizhi_2']?.toString() ?? '';
    
    if (weizhi.isNotEmpty) availableLocations.add(weizhi);
    if (weizhi1.isNotEmpty) availableLocations.add(weizhi1);
    if (weizhi2.isNotEmpty) availableLocations.add(weizhi2);
  }
  
  // 当选择库位时更新数量
  void _onLocationSelected(String location) {
    if (location.isEmpty) return;
    
    print('【库位选择】选择库位: $location');
    
    // 将位置填充到输入框
    weizhiController.text = location;
    
    // 添加到历史记录
    _addWeizhiToHistory(location);
    
    // 更新选中位置状态
    setState(() {
      selectedLocation = location;
    });
    
    // 调试输出当前商品的库位信息
    if (currentItemLocations.isNotEmpty) {
      String matchType = "";
      if (location == currentItemLocations['weizhi']) {
        matchType = "主备货";
      } else if (location == currentItemLocations['weizhi_1']) {
        matchType = "备货1";
      } else if (location == currentItemLocations['weizhi_2']) {
        matchType = "备货2";
      } else {
        matchType = "未知库位";
      }
      print('【库位选择】匹配到库位类型: $matchType');
    }
  }
  
  // 添加一个新方法用于双击库位按钮直接编辑
  void _onLocationDoubleTap(String locationType, String location) {
    print('【库位编辑】双击库位按钮，直接进入编辑模式: $locationType');
    _showLocationEditDialog(locationType, location);
  }

  // 添加开始编辑库位的方法
  void _showLocationEditDialog(String locationType, String currentValue) {
    if (currentItemLocations.isEmpty) {
      setState(() {
        resultMessage = '请先查询商品信息';
      });
      _clearResultMessageAfterDelay();
      return;
    }
    
    print('【库位编辑】打开库位编辑弹窗: $locationType, 当前值: $currentValue');
    String locationTypeDescription = locationType == 'weizhi' ? '主备' : 
                                     locationType == 'weizhi_1' ? '备1' : '备2';
    
    // 根据库位类型设置对应的颜色
    Color dialogColor;
    if (locationType == 'weizhi') {
      dialogColor = Colors.blue; // 主备货使用蓝色
    } else if (locationType == 'weizhi_1') {
      dialogColor = Colors.orange; // 备货1使用橙色
    } else {
      dialogColor = Colors.purple; // 备货2使用紫色
    }
    
    print('【库位编辑】当前编辑的库位类型: $locationTypeDescription');
    
    // 创建一个临时的文本编辑控制器用于弹窗
    final editController = TextEditingController();
    
    // 显示编辑对话框
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            '编辑$locationTypeDescription',
            style: TextStyle(
              color: dialogColor, 
              fontWeight: FontWeight.bold,
              fontSize: 18, // 设置标题字体大小为12
            ),
          ),
          titlePadding: EdgeInsets.fromLTRB(20, 16, 20, 0),
          contentPadding: EdgeInsets.fromLTRB(20, 12, 20, 0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(color: dialogColor, width: 2),
          ),
          content: Container(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: dialogColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: dialogColor.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(locationType == 'weizhi' 
                          ? Icons.home_filled 
                          : locationType == 'weizhi_1' 
                              ? Icons.inventory 
                              : Icons.inventory_2,
                          color: dialogColor),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          currentValue.isEmpty ? "未设置" : currentValue, 
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: dialogColor,
                            fontSize: 16,
                          )
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 16),
                TextField(
                  controller: editController,
                  autofocus: true,
                  decoration: InputDecoration(
                    labelText: '新位置',
                    hintText: '输入新的位置值',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: dialogColor, width: 2),
                    ),
                    labelStyle: TextStyle(color: dialogColor),
                    prefixIcon: Icon(Icons.edit_location, color: dialogColor),
                  ),
                  keyboardType: TextInputType.text,
                ),
                SizedBox(height: 16),
              ],
            ),
          ),
          actions: [
            TextButton(
              child: Text(
                '取消',
                style: TextStyle(color: Colors.grey[700]),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: dialogColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              child: Text('确定', style: TextStyle(color: Colors.white)),
              onPressed: () {
                Navigator.of(context).pop();
                
                final newLocation = editController.text.trim();
                if (newLocation.isEmpty) {
                  setState(() {
                    resultMessage = '位置不能为空';
                  });
                  _clearResultMessageAfterDelay();
                  return;
                }
                
                // 检查新值是否与其他库位冲突
                bool isDuplicate = false;
                String duplicateWith = '';
                
                if (locationType == 'weizhi') {
                  if (newLocation == currentItemLocations['weizhi_1']) {
                    isDuplicate = true;
                    duplicateWith = '备1';
                  } else if (newLocation == currentItemLocations['weizhi_2']) {
                    isDuplicate = true;
                    duplicateWith = '备2';
                  }
                } else if (locationType == 'weizhi_1') {
                  if (newLocation == currentItemLocations['weizhi']) {
                    isDuplicate = true;
                    duplicateWith = '主备';
                  } else if (newLocation == currentItemLocations['weizhi_2']) {
                    isDuplicate = true;
                    duplicateWith = '备2';
                  }
                } else if (locationType == 'weizhi_2') {
                  if (newLocation == currentItemLocations['weizhi']) {
                    isDuplicate = true;
                    duplicateWith = '主备';
                  } else if (newLocation == currentItemLocations['weizhi_1']) {
                    isDuplicate = true;
                    duplicateWith = '备1';
                  }
                }
                
                if (isDuplicate) {
                  setState(() {
                    resultMessage = '位置"$newLocation"已被$duplicateWith使用，不能相同';
                  });
                  _clearResultMessageAfterDelay(2);
                  return;
                }
                
                // 更新库位
                _updateLocation(locationType, newLocation);
              },
            ),
          ],
        );
      },
    );
  }

  // 添加库位信息显示的UI组件
  Widget _buildLocationInfo() {
    if (isLoadingLocations) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Center(child: CircularProgressIndicator(strokeWidth: 2)),
      );
    }
    
    if (currentItemLocations.isEmpty) {
      return SizedBox();
    }
    
    // 提取库位信息
    String weizhi = currentItemLocations['weizhi'] ?? '';
    String weizhi1 = currentItemLocations['weizhi_1'] ?? '';
    String weizhi2 = currentItemLocations['weizhi_2'] ?? '';
    int caja = int.tryParse(currentItemLocations['caja']?.toString() ?? '0') ?? 0;
    int caja1 = int.tryParse(currentItemLocations['caja1']?.toString() ?? '0') ?? 0;
    int caja2 = int.tryParse(currentItemLocations['caja2']?.toString() ?? '0') ?? 0;
    int totalCaja = int.tryParse(currentItemLocations['total_caja']?.toString() ?? '0') ?? 0;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [        
        // 库位选择按钮行
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Row(
            children: [
              // 主备货按钮
              Expanded(
                child: _buildLocationButton(
                  '主备货',
                  weizhi,
                  caja.toString(),
                  weizhi.isNotEmpty, 
                  Colors.blue
                ),
              ),
              SizedBox(width: 8),
              
              // 备货1按钮
              Expanded(
                child: _buildLocationButton(
                  '备货1',
                  weizhi1,
                  caja1.toString(),
                  weizhi1.isNotEmpty, 
                  Colors.orange
                ),
              ),
              SizedBox(width: 8),
              
              // 备货2按钮
              Expanded(
                child: _buildLocationButton(
                  '备货2',
                  weizhi2,
                  caja2.toString(),
                  weizhi2.isNotEmpty, 
                  Colors.purple
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  // 构建库位选择按钮
  Widget _buildLocationButton(String title, String location, String quantity, bool isAvailable, Color color) {
    // 为不同库位设置不同图标
    IconData buttonIcon;
    String locationType;
    if (title == '主备货') {
      buttonIcon = Icons.home_filled;
      locationType = 'weizhi';
    } else if (title == '备货1') {
      buttonIcon = Icons.inventory;
      locationType = 'weizhi_1';
    } else {
      buttonIcon = Icons.inventory_2;
      locationType = 'weizhi_2';
    }
    
    // 修改显示标题
    String displayTitle = title;
    if (title == '主备货') {
      displayTitle = '主备';
    } else if (title == '备货1') {
      displayTitle = '备1';
    } else if (title == '备货2') {
      displayTitle = '备2';
    }
    
    // 检查当前库位是否被选中
    bool isSelected = selectedLocation == location;

    // 将按钮始终设为可用，即使库位为空
    bool buttonEnabled = true;
    
    return Card(
      elevation: isAvailable ? (isSelected ? 8 : 4) : 1, // 选中时增加阴影
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4), // 圆角改为4
        side: BorderSide(
          color: isSelected ? Colors.red : color,
          width: isSelected ? 2.5 : 1.5
        ),
      ),
      child: GestureDetector(
        onDoubleTap: buttonEnabled ? () {
          // 双击直接进入编辑模式
          _onLocationDoubleTap(locationType, location);
        } : null,
        child: InkWell(
          onTap: buttonEnabled ? () {
            // 点击选择库位
            if (!isAvailable) {
              // 如果库位为空，直接进入编辑模式
              _showLocationEditDialog(locationType, "");
            } else {
              // 否则选择该位置
              _onLocationSelected(location);
            }
          } : null,
          onLongPress: buttonEnabled ? () {
            // 长按显示编辑对话框
            _showLocationEditDialog(locationType, location);
          } : null,
          borderRadius: BorderRadius.circular(4),
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 8, horizontal: 6), // 减少内边距
            decoration: BoxDecoration(
              gradient: isAvailable 
                ? LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: isSelected
                      ? [Colors.red.withOpacity(0.1), Colors.red.withOpacity(0.2)] // 选中时背景渐变为红色
                      : [color.withOpacity(0.1), color.withOpacity(0.2)]
                  ) 
                : null,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      buttonIcon,
                      color: isAvailable 
                        ? (isSelected ? Colors.red : color) // 选中时图标变为红色
                        : Colors.grey,
                      size: 16, // 减小图标尺寸
                    ),
                    SizedBox(width: 4), // 减小间距
                    Text(
                      displayTitle, // 使用修改后的标题 
                      style: TextStyle(
                        fontWeight: FontWeight.bold, 
                        fontSize: 14, // 减小字体大小
                        color: isAvailable 
                          ? (isSelected ? Colors.red : color) // 选中时文本变为红色
                          : Colors.grey
                      ),
                    ),
                  ],
                ),
                if (isAvailable) Divider(
                  color: isSelected 
                    ? Colors.red.withOpacity(0.3) 
                    : color.withOpacity(0.3), // 选中时分隔线变为红色
                  thickness: 1,
                  height: 8, // 减小分隔线高度
                ),
                if (isAvailable) Text(
                  location,
                  style: TextStyle(
                    fontSize: 11, // 减小字体大小
                    fontWeight: FontWeight.w500,
                    color: isAvailable 
                      ? (isSelected ? Colors.red : Colors.black87) // 选中时文本变为红色
                      : Colors.grey,
                  ),
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
                // 添加数量显示
                if (isAvailable) SizedBox(height: 6),
                if (isAvailable) Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2), // 减小内边距
                  decoration: BoxDecoration(
                    color: isSelected 
                      ? Colors.red.withOpacity(0.2) 
                      : color.withOpacity(0.2), // 选中时背景变为红色
                    borderRadius: BorderRadius.circular(6), // 减小圆角
                  ),
                  child: Text(
                    'Cjs.  $quantity',
                    style: TextStyle(
                      fontSize: 12, // 缩小字体
                      fontWeight: FontWeight.bold,
                      color: isSelected 
                        ? Colors.red 
                        : color, // 选中时文本变为红色
                    ),
                  ),
                ),
                // 当库位为空时显示提示信息
                if (!isAvailable) Container(
                  padding: EdgeInsets.symmetric(vertical: 8),
                  child: Text(
                    "点击设置库位",
                    style: TextStyle(
                      fontSize: 11,
                      fontStyle: FontStyle.italic,
                      color: Colors.grey,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 修改位置输入框的UI部分
  Widget _buildWeizhiInputField() {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: TextField(
            controller: weizhiController,
            focusNode: _weizhiFocusNode,
            decoration: InputDecoration(
              labelText: '位置/Ubicación',
              suffixIcon: IconButton(
                icon: Icon(Icons.menu),
                onPressed: () {
                  // 显示库位选择对话框
                  if (availableLocations.isNotEmpty) {
                    _showLocationSelectionDialog();
                  }
                },
                tooltip: '选择库位',
              ),
            ),
            onTap: () {
              setState(() {
                activeController = weizhiController;
                isCustomKeyboardVisible = true;
              });
            },
            readOnly: false,
            style: TextStyle(fontSize: 18),
            keyboardType: isCustomKeyboardVisible ? TextInputType.none : TextInputType.text,
          ),
        ),
      ],
    );
  }

  // 重写库位选择对话框方法，支持自定义键盘
  void _showLocationSelectionDialog() {
    if (availableLocations.isEmpty) return;
    
    // 记住原来的控制器状态
    final previousController = activeController;
    final previousKeyboardState = isCustomKeyboardVisible;
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: Text('选择库位/Seleccionar ubicación'),
              content: Container(
                width: double.maxFinite,
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: availableLocations.length,
                  itemBuilder: (context, index) {
                    String location = availableLocations[index];
                    
                    // 确定位置类型和对应的数量
                    String quantityText = '0';
                    Color locationColor = Colors.black;
                    String locationName = '';
                    
                    if (location == currentItemLocations['weizhi']) {
                      quantityText = (currentItemLocations['caja'] ?? 0).toString();
                      locationColor = Colors.blue;
                      locationName = '主备';
                    } else if (location == currentItemLocations['weizhi_1']) {
                      quantityText = (currentItemLocations['caja1'] ?? 0).toString();
                      locationColor = Colors.orange;
                      locationName = '备1';
                    } else if (location == currentItemLocations['weizhi_2']) {
                      quantityText = (currentItemLocations['caja2'] ?? 0).toString();
                      locationColor = Colors.purple;
                      locationName = '备2';
                    }
                    
                    return ListTile(
                      title: Text(
                        location,
                        style: TextStyle(color: locationColor, fontWeight: FontWeight.bold),
                      ),
                      subtitle: Text(
                        locationName,
                        style: TextStyle(color: locationColor),
                      ),
                      trailing: Text(
                        '数量: $quantityText',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      onTap: () {
                        Navigator.of(context).pop();
                        _onLocationSelected(location);
                      },
                    );
                  },
                ),
              ),
              actions: [
                TextButton(
                  child: Text('取消/Cancelar'),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            );
          },
        );
      },
    ).then((_) {
      // 恢复原来的控制器状态
      setState(() {
        activeController = previousController;
        isCustomKeyboardVisible = previousKeyboardState;
      });
    });
  }

  @override
  void dispose() {
    // 移除此监听器相关内容
    // inputController.removeListener(_onInputChanged);
    weizhiController.removeListener(_matchLocationPosition); // 移除位置匹配监听器
    inputController.dispose();
    weizhiController.dispose();
    cantidadController.dispose();
    _inputFocusNode.dispose(); // 释放FocusNode
    _weizhiFocusNode.dispose(); // 释放FocusNode
    _cantidadFocusNode.dispose(); // 释放FocusNode
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 从当前商品位置信息中获取总箱数
    int totalCaja = 0;
    if (currentItemLocations.isNotEmpty) {
      // 确保在显示之前已经正确计算
      _updateTotalCaja();
      totalCaja = int.tryParse(currentItemLocations['total_caja']?.toString() ?? '0') ?? 0;
    }
    
    // 修复对编辑相关状态变量的引用
    final bool isEditingLocation = false; // 移除使用而不是定义editingLocationType
    final String? editingLocationType = null; // 提供一个空值以防引用
    
    // 在构建UI时添加键盘隐藏处理
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 确保在编辑库位时不会显示系统键盘
      if (_weizhiFocusNode.hasFocus && 
          MediaQuery.of(context).viewInsets.bottom > 0) {
        // 如果系统键盘已弹出，强制关闭它
        FocusScope.of(context).requestFocus(FocusNode());
        Future.delayed(Duration(milliseconds: 50), () {
          if (mounted) {
            // 重新请求焦点，但不允许系统键盘弹出
            FocusScope.of(context).requestFocus(_weizhiFocusNode);
            setState(() {
              isCustomKeyboardVisible = true;
              activeController = weizhiController;
            });
          }
        });
      }
    });
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('备品仓'),
        actions: [
          // 添加键盘控制按钮
          IconButton(
            icon: Icon(isCustomKeyboardVisible ? Icons.keyboard_hide : Icons.keyboard),
            onPressed: _toggleCustomKeyboard,
            tooltip: isCustomKeyboardVisible ? '隐藏自定义键盘/Ocultar teclado' : '显示自定义键盘/Mostrar teclado',
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.fromLTRB(16.0, 5.0, 16.0, 16.0),
              child: Column(
                children: [
                  // 使用通用方法构建输入框
                  _buildTextField(
                    controller: inputController,
                    focusNode: _inputFocusNode,
                    labelText: '条码/货号/Código',
                    suffixIcon: IconButton(
                      icon: Icon(Icons.clear),
                      onPressed: () {
                        _clearAllContent();
                      },
                      tooltip: '清除全部/Borrar todo',
                    ),
                    onSubmitted: _handleInputSubmitted,
                  ),
                  
                  // 在输入框下方添加库位信息显示
                  _buildLocationInfo(),
                  
                  // 位置输入和总箱数显示
                  Row(
                    children: [
                      Expanded(
                        flex: 3,
                        child: _buildWeizhiInputField(),
                      ),
                      SizedBox(width: 16),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red, width: 1)
                        ),
                        child: Column(
                          children: [
                            Text('总箱数/Total', style: TextStyle(fontSize: 14, color: Colors.red)),
                            Text(
                              '$totalCaja',
                              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.red),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  
                  // 恢复数量输入框的显示
                  SizedBox(height: 0.0),
                  _buildTextField(
                    controller: cantidadController,
                    focusNode: _cantidadFocusNode,
                    labelText: '箱/Caja',
                  ),
                  
                  // 操作按钮
                  SizedBox(height: 16.0),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      ElevatedButton(
                        onPressed: _register,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          textStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 3,
                        ),
                        child: Text('入库/Entrada'),
                      ),
                      ElevatedButton(
                        onPressed: _checkout,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          textStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 3,
                        ),
                        child: Text('出库/Salida'),
                      ),
                    ],
                  ),
                  SizedBox(height: 16.0),
                  Text(resultMessage),
                  _buildQueryResults(),
                ],
              ),
            ),
          ),
          
          // 底部的自定义键盘
          if (isCustomKeyboardVisible && activeController != null)
            CustomKeyboard(
              controller: activeController!,
              onSubmit: (text) {
                if (activeController == inputController) {
                  _handleInputSubmitted(text);
                } else if (activeController == weizhiController) {
                  // 移除对_submitLocationEdit的引用，直接提交当前文本
                  if (text.isNotEmpty && selectedLocation != null) {
                    // 如果已选择某个库位，提交文本内容
                    weizhiController.text = text;
                    // 触发回车提交事件
                    FocusScope.of(context).requestFocus(FocusNode());
                  }
                }
              },
            ),
        ],
      ),
    );
  }

  // 处理输入框回车事件
  Future<void> _handleInputSubmitted(String value) async {
    if (value.isEmpty) return;
    
    setState(() {
      resultMessage = '正在查询...';
    });
    
    // 查询库位信息
    await _fetchItemLocations(value, showInsertDialog: true);
    
    // 查询结束后清空结果消息
    setState(() {
      if (currentItemLocations.isNotEmpty) {
        resultMessage = '查询成功';
      }
    });
    
    _clearResultMessageAfterDelay();
  }

  // 重新计算总箱数
  Future<void> _recalculateTotalCaja() async {
    setState(() {
      resultMessage = '正在重新计算总箱数...';
      isLoadingLocations = true;
    });

    try {
      final success = await ScanApiService.recalculateTotalCaja();
      
      setState(() {
        isLoadingLocations = false;
        if (success) {
          resultMessage = '重新计算总箱数成功';
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('总箱数已更新！如需查看最新数据，请重新查询')),
          );
        } else {
          resultMessage = '重新计算总箱数失败';
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('总箱数更新失败，请稍后再试')),
          );
        }
      });
      
      // 如果当前有查询结果，可以刷新显示
      if (queryResults.isNotEmpty && currentItemLocations.isNotEmpty) {
        await _fetchItemLocations(
          currentItemLocations['ArticuloID'] ?? '',
        );
      }
    } catch (e) {
      setState(() {
        isLoadingLocations = false;
        resultMessage = '重新计算总箱数出错: $e';
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('发生错误: ${e.toString().substring(0, math.min(50, e.toString().length))}')),
      );
    }
  }

  // 添加一个新方法来清除所有内容
  void _clearAllContent() {
    // 清除所有输入框内容
    inputController.clear();
    weizhiController.clear();
    cantidadController.clear();
    
    // 清除页面上的其他内容
    setState(() {
      queryResults = []; // 清空查询结果
      currentItemLocations = {}; // 清空当前商品位置信息
      availableLocations = []; // 清空可用库位列表
      resultMessage = '已清除所有内容'; // 设置结果消息
      selectedLocation = null; // 清除选中状态
      editingLocationType = null; // 清除编辑状态
    });
    
    // 设置焦点回到条码/货号输入框
    _inputFocusNode.requestFocus();
    
    // 2秒后清空结果消息
    _clearResultMessageAfterDelay();
  }

  // 修改键盘切换方法，使其适用于所有输入框，包括弹窗中的输入框
  void _toggleCustomKeyboard() {
    setState(() {
      isCustomKeyboardVisible = !isCustomKeyboardVisible;
    });
    
    // 如果关闭键盘，则取消焦点
    if (!isCustomKeyboardVisible) {
      FocusScope.of(context).unfocus();
      return;
    }
    
    // 如果打开键盘，保持当前输入框的焦点
    if (activeController != null) {
      FocusNode? currentFocusNode;
      
      // 确定当前控制器对应的FocusNode
      if (activeController == inputController) {
        currentFocusNode = _inputFocusNode;
      } else if (activeController == weizhiController) {
        currentFocusNode = _weizhiFocusNode;
      } else if (activeController == cantidadController) {
        currentFocusNode = _cantidadFocusNode;
      }
      
      // 使用短延迟确保在系统键盘关闭后重新设置焦点
      Future.delayed(Duration(milliseconds: 50), () {
        if (currentFocusNode != null) {
          currentFocusNode.requestFocus();
          
          // 如果控制器中有内容，选中全部文本
          if (activeController!.text.isNotEmpty) {
            activeController!.selection = TextSelection(
              baseOffset: 0,
              extentOffset: activeController!.text.length
            );
          }
        }
      });
    }
  }

  // 增强版的设置活动输入框方法，使其适用于所有输入框
  void _setActiveController(TextEditingController controller, {FocusNode? customFocusNode}) {
    setState(() {
      activeController = controller;
      // 确保自定义键盘显示
      isCustomKeyboardVisible = true;
    });
    
    // 获取对应的FocusNode
    FocusNode? focusNode = customFocusNode;
    if (focusNode == null) {
      if (controller == inputController) {
        focusNode = _inputFocusNode;
      } else if (controller == weizhiController) {
        focusNode = _weizhiFocusNode;
      } else if (controller == cantidadController) {
        focusNode = _cantidadFocusNode;
      }
    }
    
    // 关闭系统键盘但保持焦点
    FocusScope.of(context).unfocus();
    
    // 使用短延迟确保在系统键盘关闭后重新设置焦点
    Future.delayed(Duration(milliseconds: 50), () {
      if (focusNode != null) {
        focusNode.requestFocus();
        
        // 如果控制器有文本，则全选
        if (controller.text.isNotEmpty) {
          controller.selection = TextSelection(
            baseOffset: 0,
            extentOffset: controller.text.length
          );
        }
      }
    });
  }

  // 为弹窗输入框添加焦点支持的方法
  void _showDialogWithKeyboardSupport(
    Widget dialogContent, 
    {
      String? title,
      TextEditingController? initialFocusController,
      Function(bool)? onConfirm,
    }
  ) {
    // 暂存当前活动控制器状态
    final previousController = activeController;
    final previousKeyboardState = isCustomKeyboardVisible;
    
    // 如果提供了初始焦点控制器，自动设置为活动控制器
    if (initialFocusController != null) {
      setState(() {
        activeController = initialFocusController;
        isCustomKeyboardVisible = true;
      });
    }
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: title != null ? Text(title) : null,
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    dialogContent,
                    // 在弹窗中显示自定义键盘（如果需要）
                    if (isCustomKeyboardVisible && activeController != null)
                      Container(
                        constraints: BoxConstraints(maxHeight: 250),
                        child: CustomKeyboard(
                          controller: activeController!,
                          onSubmit: (text) {
                            if (onConfirm != null) {
                              onConfirm(true);
                            } else {
                              Navigator.of(context).pop(true);
                            }
                          },
                        ),
                      ),
                  ],
                ),
              ),
              actions: [
                // 添加键盘开关按钮
                IconButton(
                  icon: Icon(isCustomKeyboardVisible ? Icons.keyboard_hide : Icons.keyboard),
                  onPressed: () {
                    setDialogState(() {
                      isCustomKeyboardVisible = !isCustomKeyboardVisible;
                    });
                    // 同时更新父状态
                    setState(() {
                      isCustomKeyboardVisible = !isCustomKeyboardVisible;
                    });
                  },
                  tooltip: isCustomKeyboardVisible ? '隐藏键盘' : '显示键盘',
                ),
                TextButton(
                  child: Text('取消'),
                  onPressed: () {
                    // 恢复先前的键盘状态
                    setState(() {
                      activeController = previousController;
                      isCustomKeyboardVisible = previousKeyboardState;
                    });
                    
                    if (onConfirm != null) {
                      onConfirm(false);
                    } else {
                      Navigator.of(context).pop(false);
                    }
                  },
                ),
                TextButton(
                  child: Text('确定'),
                  onPressed: () {
                    if (onConfirm != null) {
                      onConfirm(true);
                    } else {
                      Navigator.of(context).pop(true); 
                    }
                  },
                ),
              ],
            );
          },
        );
      },
    ).then((result) {
      // 弹窗关闭后，恢复先前的键盘状态
      setState(() {
        activeController = previousController;
        isCustomKeyboardVisible = previousKeyboardState;
      });
    });
  }

  // 修改输入框的构建方法，确保所有输入框都能使用自定义键盘
  Widget _buildTextField({
    required TextEditingController controller,
    required String labelText,
    FocusNode? focusNode,
    Widget? suffixIcon,
    VoidCallback? onTap,
    ValueChanged<String>? onSubmitted,
    bool readOnly = false,
    bool obscureText = false,
    TextInputType? keyboardType,
    ValueChanged<String>? onChanged,
    TextStyle? style,
    InputDecoration? decoration,
  }) {
    // 创建默认的焦点节点（如果未提供）
    focusNode ??= FocusNode();
    
    // 创建默认的样式
    style ??= TextStyle(fontSize: 18);
    
    // 如果处于编辑模式且这是位置输入框，应用特殊样式
    if (controller == weizhiController && editingLocationType != null) {
      style = TextStyle(
        fontSize: 18,
        color: Colors.green,
        fontWeight: FontWeight.bold,
      );
    }
    
    // 构建输入框装饰
    InputDecoration baseDecoration = InputDecoration(
      labelText: labelText,
      suffixIcon: suffixIcon,
    );
    
    // 如果是在编辑位置并且这是位置输入框
    if (controller == weizhiController && editingLocationType != null) {
      baseDecoration = baseDecoration.copyWith(
        labelStyle: TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.green, width: 2.0),
          borderRadius: BorderRadius.circular(8.0),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.green, width: 2.0),
          borderRadius: BorderRadius.circular(8.0),
        ),
        hintText: '输入新的库位值',
      );
    } else if (controller == weizhiController) {
      // 普通状态下的位置输入框
      baseDecoration = baseDecoration.copyWith(
        hintText: '点击库位按钮进入编辑模式',
      );
    }
    
    // 合并装饰器
    if (decoration != null) {
      baseDecoration = baseDecoration.copyWith(
        prefixIcon: decoration.prefixIcon,
        prefixIconConstraints: decoration.prefixIconConstraints,
        prefix: decoration.prefix,
        prefixText: decoration.prefixText,
        prefixStyle: decoration.prefixStyle,
        hintText: decoration.hintText ?? baseDecoration.hintText,
        hintStyle: decoration.hintStyle,
        helperText: decoration.helperText,
        counterText: decoration.counterText,
        filled: decoration.filled,
        fillColor: decoration.fillColor,
        isDense: decoration.isDense,
        contentPadding: decoration.contentPadding,
        border: decoration.border,
        enabledBorder: decoration.enabledBorder ?? baseDecoration.enabledBorder,
        focusedBorder: decoration.focusedBorder ?? baseDecoration.focusedBorder,
        errorBorder: decoration.errorBorder,
        focusedErrorBorder: decoration.focusedErrorBorder,
        errorText: decoration.errorText,
        errorStyle: decoration.errorStyle,
      );
    }
    
    return TextField(
      controller: controller,
      focusNode: focusNode,
      keyboardType: TextInputType.none, // 始终隐藏系统键盘
      readOnly: readOnly,
      obscureText: obscureText,
      decoration: baseDecoration,
      style: style,
      onSubmitted: onSubmitted,
      onChanged: (value) {
        // 如果是位置输入框且未处于编辑模式，尝试匹配库位
        if (controller == weizhiController && editingLocationType == null) {
          _matchLocationPosition();
        }
        
        // 调用外部传入的onChanged回调
        if (onChanged != null) {
          onChanged(value);
        }
      },
      onTap: () {
        // 设置此控制器为活动控制器
        _setActiveController(controller, customFocusNode: focusNode);
        // 执行额外的onTap回调（如果有）
        if (onTap != null) onTap();
      },
    );
  }

  // 添加一个新方法用于匹配输入的位置与库位
  void _matchLocationPosition() {
    // 只有当商品信息已加载时才进行匹配
    if (currentItemLocations.isEmpty) return;
    
    // 获取当前输入的位置
    String inputLocation = weizhiController.text.trim();
    if (inputLocation.isEmpty) {
      // 如果输入为空，清除选中状态
      setState(() {
        selectedLocation = null;
      });
      return;
    }
    
    // 如果当前处于编辑模式，不进行匹配
    if (editingLocationType != null) {
      print('【位置匹配】当前处于编辑模式，跳过匹配');
      return;
    }
    
    // 获取三个库位的值
    String weizhi = currentItemLocations['weizhi']?.toString() ?? '';
    String weizhi1 = currentItemLocations['weizhi_1']?.toString() ?? '';
    String weizhi2 = currentItemLocations['weizhi_2']?.toString() ?? '';
    
    print('【位置匹配】当前输入: $inputLocation');
    print('【位置匹配】现有库位: 主备货=$weizhi, 备货1=$weizhi1, 备货2=$weizhi2');
    
    // 首先尝试精准匹配
    if (inputLocation == weizhi) {
      // 匹配到主备货位置
      setState(() {
        selectedLocation = weizhi;
      });
      print('【位置匹配】精确匹配到主备货位置: $weizhi');
      return;
    } else if (inputLocation == weizhi1) {
      // 匹配到备货1位置
      setState(() {
        selectedLocation = weizhi1;
      });
      print('【位置匹配】精确匹配到备货1位置: $weizhi1');
      return;
    } else if (inputLocation == weizhi2) {
      // 匹配到备货2位置
      setState(() {
        selectedLocation = weizhi2;
      });
      print('【位置匹配】精确匹配到备货2位置: $weizhi2');
      return;
    }
    
    // 如果精准匹配失败，尝试部分匹配（前缀匹配）
    if (weizhi.isNotEmpty && weizhi.startsWith(inputLocation)) {
      setState(() {
        selectedLocation = weizhi;
      });
      print('【位置匹配】部分匹配到主备货位置: $weizhi (输入: $inputLocation)');
      return;
    } else if (weizhi1.isNotEmpty && weizhi1.startsWith(inputLocation)) {
      setState(() {
        selectedLocation = weizhi1;
      });
      print('【位置匹配】部分匹配到备货1位置: $weizhi1 (输入: $inputLocation)');
      return;
    } else if (weizhi2.isNotEmpty && weizhi2.startsWith(inputLocation)) {
      setState(() {
        selectedLocation = weizhi2;
      });
      print('【位置匹配】部分匹配到备货2位置: $weizhi2 (输入: $inputLocation)');
      return;
    }
    
    // 如果前缀匹配也失败，尝试包含匹配
    if (weizhi.isNotEmpty && weizhi.contains(inputLocation)) {
      setState(() {
        selectedLocation = weizhi;
      });
      print('【位置匹配】包含匹配到主备货位置: $weizhi (输入: $inputLocation)');
      return;
    } else if (weizhi1.isNotEmpty && weizhi1.contains(inputLocation)) {
      setState(() {
        selectedLocation = weizhi1;
      });
      print('【位置匹配】包含匹配到备货1位置: $weizhi1 (输入: $inputLocation)');
      return;
    } else if (weizhi2.isNotEmpty && weizhi2.contains(inputLocation)) {
      setState(() {
        selectedLocation = weizhi2;
      });
      print('【位置匹配】包含匹配到备货2位置: $weizhi2 (输入: $inputLocation)');
      return;
    }
    
    // 没有匹配到任何库位
    setState(() {
      selectedLocation = null;
    });
    print('【位置匹配】未匹配到任何库位: $inputLocation');
  }

  // 添加被删除的更新库位位置方法
  Future<void> _updateLocation(String locationField, String newLocation) async {
    if (currentItemLocations.isEmpty) return;

    final String articuloID = currentItemLocations['ArticuloID'];
    if (articuloID == null || articuloID.isEmpty) return;
    
    setState(() {
      resultMessage = '正在更新库位...';
      isLoadingLocations = true;
    });
    
    try {
      print('【库位更新】开始更新库位: 商品ID=$articuloID, 字段=$locationField, 新值=$newLocation');
      
      // 使用ScanApiService提供的方法更新库位
      final updatedData = await ScanApiService.updateLocationField(
        articuloID, 
        locationField, 
        newLocation
      );
      
      print('【库位更新】更新成功，获取到更新后的数据');
      
      setState(() {
        currentItemLocations = updatedData;
        
        // 更新查询结果中的库位信息
        for (var i = 0; i < queryResults.length; i++) {
          if (queryResults[i]['ArticuloID'] == articuloID) {
            queryResults[i][locationField] = newLocation;
            print('【库位更新】更新查询结果中的项目 $i');
          }
        }
        
        _updateAvailableLocations(); // 更新可用库位列表
        resultMessage = '库位更新成功';
      });
      
      return; // 返回成功
    } catch (e) {
      print('【库位更新】更新库位出错: $e');
      setState(() {
        resultMessage = '更新库位出错: $e';
      });
      
      // 显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('库位更新失败: ${e.toString()}'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
      
      throw e; // 重新抛出异常，让调用者知道失败了
    } finally {
      setState(() {
        isLoadingLocations = false;
      });
      _clearResultMessageAfterDelay();
    }
  }
}