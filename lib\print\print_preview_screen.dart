import 'package:flutter/material.dart';

class PrintPreviewScreen extends StatelessWidget {
  final String productName;
  final String productCode;
  final String barcode;
  final String price; // 新增的参数

  PrintPreviewScreen({
    required this.productName,
    required this.productCode,
    required this.barcode,
    required this.price, // 确保构造函数接收 price 参数
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Print Preview'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('货号: $productCode'),
            Text('商品名称: $productName'),
            Text('价格: $price'),
            SizedBox(height: 20),
            Center(
              child: ElevatedButton(
                onPressed: () {
                  // 打印功能
                },
                child: Text('确认并打印'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
