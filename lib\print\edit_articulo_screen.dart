import 'package:flutter/material.dart';
import 'package:mistoer/print/api_print_service.dart';
import 'package:mistoer/services/database_service_sn.dart';

class EditArticuloScreen extends StatefulWidget {
  final Map<String, dynamic> articuloData;

  EditArticuloScreen({required this.articuloData});

  @override
  _EditArticuloScreenState createState() => _EditArticuloScreenState();
}

class _EditArticuloScreenState extends State<EditArticuloScreen> {
  late TextEditingController ArticuloIDController;
  late TextEditingController codigoBarraController;
  late TextEditingController nombreArticuloController;
  late TextEditingController precioVentaController;
  late TextEditingController precioEntradaController;
  late TextEditingController precioMayorController;
  late TextEditingController precioEspecialController;
  late TextEditingController precioSocioController;
  late TextEditingController precioFacturaController;
  late TextEditingController cantPaqueteController;
  late TextEditingController cantCajaController;
  late TextEditingController categoriaController;
  late TextEditingController stockMinController;
  late TextEditingController stockMaxController;
  late TextEditingController unidadNombreController;
  late TextEditingController proveedorEmpresaController;
  late TextEditingController sitioAlmacenController;
  late TextEditingController volumenPesoController;

  List<Map<String, dynamic>> categories = [];
  bool isUnidadUsarRegla = false;
  bool isDescuentoCambioProhibido = false;
  bool isBloqueado = false;
  int? bohaoValue;

  @override
  void initState() {
    super.initState();

    // 初始化表单字段，确保价格保留小数点后两位，包装数和库存字段保留整数
    ArticuloIDController = TextEditingController(text: widget.articuloData['ArticuloID'].toString());
    codigoBarraController = TextEditingController(text: widget.articuloData['CodigoBarra']);
    nombreArticuloController = TextEditingController(text: widget.articuloData['NombreES']);
    precioVentaController = TextEditingController(
        text: widget.articuloData['PrecioDetalle'] != null
            ? _formatDouble(widget.articuloData['PrecioDetalle'], 2)
            : '');
    precioEntradaController = TextEditingController(
        text: widget.articuloData['PrecioCoste'] != null
            ? _formatDouble(widget.articuloData['PrecioCoste'], 2)
            : '');
    precioMayorController = TextEditingController(
        text: widget.articuloData['PrecioMayor'] != null
            ? _formatDouble(widget.articuloData['PrecioMayor'], 2)
            : '');
    precioEspecialController = TextEditingController(
        text: widget.articuloData['PrecioEspecial'] != null
            ? _formatDouble(widget.articuloData['PrecioEspecial'], 2)
            : '');
    precioSocioController = TextEditingController(
        text: widget.articuloData['PrecioSocio'] != null
            ? _formatDouble(widget.articuloData['PrecioSocio'], 2)
            : '');
    precioFacturaController = TextEditingController(
        text: widget.articuloData['PrecioFactura'] != null
            ? _formatDouble(widget.articuloData['PrecioFactura'], 2)
            : '');
    cantPaqueteController = TextEditingController(
        text: widget.articuloData['CantidadPorUnidad'] != null
            ? _formatDouble(widget.articuloData['CantidadPorUnidad'], 0)
            : '');
    cantCajaController = TextEditingController(
        text: widget.articuloData['CantidadPorUnidad2'] != null
            ? _formatDouble(widget.articuloData['CantidadPorUnidad2'], 0)
            : '');
    categoriaController = TextEditingController(text: widget.articuloData['ClaseID'].toString());
    stockMinController = TextEditingController(
        text: widget.articuloData['MiniStock'] != null
            ? _formatDouble(widget.articuloData['MiniStock'], 0)
            : '');
    stockMaxController = TextEditingController(
        text: widget.articuloData['MaxiStock'] != null
            ? _formatDouble(widget.articuloData['MaxiStock'], 0)
            : '');
    unidadNombreController = TextEditingController(text: widget.articuloData['UnidadNombre']);
    proveedorEmpresaController = TextEditingController(text: widget.articuloData['ProveedorID']);
    sitioAlmacenController = TextEditingController(text: widget.articuloData['SitioEnAlmacen']);
    volumenPesoController = TextEditingController(
        text: widget.articuloData['VolumenPeso'] != null
            ? _formatDouble(widget.articuloData['VolumenPeso'], 2)
            : '');

    getBohaoFromDatabase();
    fetchCategories();
  }

  String _formatDouble(dynamic value, int fractionDigits) {
    if (value is double) {
      return value.toStringAsFixed(fractionDigits);
    } else if (value is String) {
      return double.tryParse(value)?.toStringAsFixed(fractionDigits) ?? value;
    } else if (value is int) {
      return value.toString();
    }
    return '';
  }

  Future<void> getBohaoFromDatabase() async {
    bohaoValue = await DatabaseServiceSN.getBohaoValue();
    setState(() {
      if (bohaoValue == 1) {
        isUnidadUsarRegla = true;
        // Convert EmpresaID to string to avoid type error
        proveedorEmpresaController.text = widget.articuloData['EmpresaID']?.toString() ?? '';
      } else {
        isUnidadUsarRegla = false;
      }
    });
  }

  Future<void> fetchCategories() async {
    final fetchedCategories = await ApiPrintService.fetchArticuloClase();
    if (fetchedCategories != null) {
      final uniqueCategories = fetchedCategories.toSet().toList();
      setState(() {
        categories = uniqueCategories;
        categoriaController.text =
        uniqueCategories.isNotEmpty ? uniqueCategories.first['ClaseID'].toString() : '';
      });
    }
  }

  Future<void> saveArticulo() async {
    if (ArticuloIDController.text == '0') {
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('临时商品不可修改')));
      Navigator.pop(context); // 关闭页面并返回
      return;
    }

    // 创建基础商品数据对象
    final articuloData = {
      'ArticuloID': ArticuloIDController.text.isNotEmpty ? ArticuloIDController.text : null,
      'CodigoBarra': codigoBarraController.text.isNotEmpty ? codigoBarraController.text : '',
      'NombreES': nombreArticuloController.text.isNotEmpty ? nombreArticuloController.text : null,
      'PrecioDetalle': precioVentaController.text.isNotEmpty
          ? double.parse(double.parse(precioVentaController.text).toStringAsFixed(2))
          : null,
      'PrecioCoste': precioEntradaController.text.isNotEmpty
          ? double.parse(double.parse(precioEntradaController.text).toStringAsFixed(2))
          : null,
      'PrecioMayor': precioMayorController.text.isNotEmpty
          ? double.parse(double.parse(precioMayorController.text).toStringAsFixed(2))
          : null,
      'PrecioEspecial': precioEspecialController.text.isNotEmpty
          ? double.parse(double.parse(precioEspecialController.text).toStringAsFixed(2))
          : null,
      'PrecioSocio': precioSocioController.text.isNotEmpty
          ? double.parse(double.parse(precioSocioController.text).toStringAsFixed(2))
          : null,
      'PrecioFactura': precioFacturaController.text.isNotEmpty
          ? double.parse(double.parse(precioFacturaController.text).toStringAsFixed(2))
          : null,
      'CantidadPorUnidad': cantPaqueteController.text.isNotEmpty
          ? int.parse(cantPaqueteController.text)
          : null,
      'CantidadPorUnidad2': cantCajaController.text.isNotEmpty
          ? int.parse(cantCajaController.text)
          : null,
      'ClaseID': categoriaController.text.isNotEmpty ? int.parse(categoriaController.text) : null,
      'MiniStock': stockMinController.text.isNotEmpty
          ? int.parse(stockMinController.text)
          : null,
      'MaxiStock': stockMaxController.text.isNotEmpty
          ? int.parse(stockMaxController.text)
          : null,
      'UnidadNombre': unidadNombreController.text.isNotEmpty ? unidadNombreController.text : '',
      'SitioEnAlmacen': sitioAlmacenController.text.isNotEmpty ? sitioAlmacenController.text : '',
      'VolumenPeso': volumenPesoController.text.isNotEmpty
          ? double.parse(double.parse(volumenPesoController.text).toStringAsFixed(2))
          : null,
      'UnidadUsarRegla': isUnidadUsarRegla ? '1' : '0',
      'DescuentoCambioProhibido': isDescuentoCambioProhibido ? '-1' : '0',
      'Bloqueado': isBloqueado ? '-1' : '0',
    };
    
    // 根据bohao值设置正确的供应商字段
    final providerValue = proveedorEmpresaController.text.isNotEmpty ? 
        proveedorEmpresaController.text : '';
        
    if (bohaoValue == 1) {
      // 当bohao为1时使用EmpresaID
      // 尝试解析为整数，如果是有效整数则保持整数类型
      try {
        int empresaID = int.parse(providerValue);
        articuloData['EmpresaID'] = empresaID;
      } catch (e) {
        // 如果不是有效整数，则使用字符串
        articuloData['EmpresaID'] = providerValue;
      }
      print('使用EmpresaID字段: ${articuloData['EmpresaID']}');
    } else {
      // 否则使用ProveedorID
      articuloData['ProveedorID'] = providerValue;
      print('使用ProveedorID字段: $providerValue');
    }

    try {
      print('发送更新商品数据: $articuloData');
      final bool response = await ApiPrintService.updateArticulo(articuloData);

      if (response) {
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text('商品信息已更新')));
        Navigator.pop(context, true); // 返回 true 表示更新成功
      } else {
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text('更新失败，请重试')));
      }
    } catch (error) {
      print('出现错误: $error');
    }
  }

  @override
  void dispose() {
    ArticuloIDController.dispose();
    codigoBarraController.dispose();
    nombreArticuloController.dispose();
    precioVentaController.dispose();
    precioEntradaController.dispose();
    precioMayorController.dispose();
    precioEspecialController.dispose();
    precioSocioController.dispose();
    precioFacturaController.dispose();
    cantPaqueteController.dispose();
    cantCajaController.dispose();
    categoriaController.dispose();
    stockMinController.dispose();
    stockMaxController.dispose();
    unidadNombreController.dispose();
    proveedorEmpresaController.dispose();
    sitioAlmacenController.dispose();
    volumenPesoController.dispose();
    super.dispose();
  }

  Widget buildTextField(String label, TextEditingController controller,
      {bool enabled = true, TextInputType keyboardType = TextInputType.text}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextField(
        controller: controller,
        enabled: enabled,
        keyboardType: keyboardType,
        style: TextStyle(
          fontWeight: FontWeight.bold,
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            fontSize: 18,
          ),
          border: OutlineInputBorder(),
        ),
      ),
    );
  }

  Widget buildCategoryDropdown(String label, TextEditingController controller) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: DropdownButtonFormField<String>(
        value: categories.any((category) => category['ClaseID'].toString() == controller.text)
            ? controller.text
            : null,
        onChanged: (String? newValue) {
          setState(() {
            controller.text = newValue ?? '';
          });
        },
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            fontSize: 18,
          ),
          border: OutlineInputBorder(),
        ),
        items: categories.map<DropdownMenuItem<String>>((category) {
          return DropdownMenuItem<String>(
            value: category['ClaseID'].toString(),
            child: Text(category['NombreES']),
          );
        }).toList(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('修改商品信息'),
      ),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: buildTextField('商品编码:', ArticuloIDController, enabled: false),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                          child: buildTextField('商品条码:', codigoBarraController)),
                    ],
                  ),
                  buildTextField('西文名称:', nombreArticuloController),
                  Row(
                    children: [
                      Expanded(
                          child: buildTextField('零售价:', precioVentaController,
                              keyboardType: TextInputType.numberWithOptions(decimal: true))),
                      SizedBox(width: 16),
                      Expanded(
                          child: buildTextField('进货价:', precioEntradaController,
                              keyboardType: TextInputType.numberWithOptions(decimal: true))),
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                          child: buildTextField('批发价:', precioMayorController,
                              keyboardType: TextInputType.numberWithOptions(decimal: true))),
                      SizedBox(width: 16),
                      Expanded(
                          child: buildTextField('特别价:', precioEspecialController,
                              keyboardType: TextInputType.numberWithOptions(decimal: true))),
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                          child: buildTextField('会员价:', precioSocioController,
                              keyboardType: TextInputType.numberWithOptions(decimal: true))),
                      SizedBox(width: 16),
                      Expanded(
                          child: buildTextField('发票价:', precioFacturaController,
                              keyboardType: TextInputType.numberWithOptions(decimal: true))),
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                          child: buildTextField('整包数:', cantPaqueteController,
                              keyboardType: TextInputType.number)),
                      SizedBox(width: 16),
                      Expanded(
                          child: buildTextField('整箱数:', cantCajaController,
                              keyboardType: TextInputType.number)),
                    ],
                  ),
                  categories.isEmpty
                      ? CircularProgressIndicator()
                      : buildCategoryDropdown('类别:', categoriaController),
                  Row(
                    children: [
                      Expanded(
                          child: buildTextField('库存下限:', stockMinController,
                              keyboardType: TextInputType.number)),
                      SizedBox(width: 16),
                      Expanded(
                          child: buildTextField('库存上限:', stockMaxController,
                              keyboardType: TextInputType.number)),
                    ],
                  ),
                  buildTextField('单位名称:', unidadNombreController),
                  buildTextField('供应商:', proveedorEmpresaController),
                  buildTextField('仓库位置:', sitioAlmacenController),
                  buildTextField('重量容积:', volumenPesoController,
                      keyboardType: TextInputType.numberWithOptions(decimal: true)),
                  Row(
                    children: [
                      Expanded(
                        child: CheckboxListTile(
                          title: Text('启用规则:'),
                          value: isUnidadUsarRegla,
                          onChanged: (bool? value) {
                            setState(() {
                              isUnidadUsarRegla = value ?? false;
                            });
                          },
                        ),
                      ),
                      Expanded(
                        child: CheckboxListTile(
                          title: Text('禁用:'),
                          value: isBloqueado,
                          onChanged: (bool? value) {
                            setState(() {
                              isBloqueado = value ?? false;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  CheckboxListTile(
                    title: Text('禁止修改折扣:'),
                    value: isDescuentoCambioProhibido,
                    onChanged: (bool? value) {
                      setState(() {
                        isDescuentoCambioProhibido = value ?? false;
                      });
                    },
                  ),
                  SizedBox(height: 80),
                ],
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              color: Colors.white,
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: saveArticulo,
                  child: Text('保存'),
                  style: ElevatedButton.styleFrom(
                    textStyle: TextStyle(fontSize: 20),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
