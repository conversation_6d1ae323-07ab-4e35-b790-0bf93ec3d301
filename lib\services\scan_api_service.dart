import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:mistoer/services/database_service.dart';

class ScanApiService {
  static const int port = 3000;
  static const Duration timeoutDuration = Duration(seconds: 10);

  // 获取服务器的基础 URL
  static Future<String> getBaseUrl() async {
    final ipOrDomain = await DatabaseService.getIp(); // 从数据库服务中获取IP地址或域名

    // 检查IP地址或域名是否有效
    if (ipOrDomain == null || ipOrDomain.isEmpty) {
      throw Exception('无法获取有效的IP地址或域名');
    }

    String url;

    // 判断域名是否已包含协议
    if (ipOrDomain.startsWith('http://') || ipOrDomain.startsWith('https://')) {
      // 如果包含协议，直接使用该域名
      url = ipOrDomain; // 直接使用提供的域名
    } else if (RegExp(r'^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(ipOrDomain)) {
      // 如果是有效的域名，使用 http 协议并不加端口
      url = 'http://$ipOrDomain'; // 仅使用域名，不加端口
    } else {
      // 对于 IP 地址，添加协议和端口
      url = 'http://$ipOrDomain:$port';
    }

    return url; // 返回最终构建的 URL
  }

  static Future<List<Map<String, dynamic>>> queryDatabase(String query, String queryType) async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/query');
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'queryType': queryType,
          'value': query,
        }),
      ).timeout(timeoutDuration);
      if (response.statusCode == 200) {
        final data = List<Map<String, dynamic>>.from(jsonDecode(response.body));
        print('Fetched data: $data');  // 打印从服务器获取的数据
        return data;
      } else {
        print('Error querying database: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('Error querying database: $e');
    }
    return [];
  }


  // Method to handle stocktaking
  static Future<String> handleStocktaking(String warehouse, String barcode) async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/stocktaking');
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'warehouse': warehouse,
          'barcode': barcode,
        }),
      ).timeout(timeoutDuration);
      if (response.statusCode == 200) {
        return 'Stocktaking updated successfully';
      } else {
        return 'Failed to update stocktaking: ${response.statusCode} - ${response.body}';
      }
    } catch (e) {
      print('Error updating stocktaking: $e');
      return 'Error updating stocktaking';
    }
  }

  // Unified method for uploading stocktaking data
  static Future<void> uploadStocktakingData(String warehouse, String barcode, String username, int inUse, int quantity, {String? articuloId, String? codigoBarra}) async {
    final baseUrl = await getBaseUrl();
    try {
      final Map<String, dynamic> payload = {
        'warehouse': warehouse,
        'barcode': barcode,
        'user': username, // Corrected field name
        'inUse': inUse,
        'quantity': quantity, // 添加数量参数
      };

      if (codigoBarra != null && codigoBarra.isNotEmpty) {
        payload['CodigoBarra'] = codigoBarra;
      }
      if (articuloId != null && articuloId.isNotEmpty) {
        payload['ArticuloID'] = articuloId;
      }

      final response = await http.post(
        Uri.parse('$baseUrl/stocktaking'),
        body: json.encode(payload),
        headers: {'Content-Type': 'application/json'},
      ).timeout(timeoutDuration);

      if (response.statusCode != 200) {
        print('Error response status: ${response.statusCode} - ${response.body}');
        throw Exception('Failed to upload stocktaking data');
      }
    } catch (e) {
      print('Error uploading stocktaking data: $e');
      throw Exception('Failed to upload stocktaking data');
    }
  }

  // Method to get stocktaking data
  static Future<List<Map<String, dynamic>>> getStocktakingData() async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/stocktaking');
    try {
      final response = await http.get(url).timeout(timeoutDuration);
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      } else {
        print('Error fetching stocktaking data: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('Error fetching stocktaking data: $e');
    }
    return [];
  }

  // Method to get stocktaking data for a specific user
  static Future<List<Map<String, dynamic>>> getStocktakingDataForUser(String username) async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/stocktaking?user=$username'); // Adjust endpoint as needed
    try {
      final response = await http.get(url).timeout(timeoutDuration);
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      } else {
        print('Error fetching stocktaking data for user: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('Error fetching stocktaking data for user: $e');
    }
    return [];
  }

  // Method to update location data
  static Future<void> updateLocationData() async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/updateLocationData');
    try {
      print('Sending request to update location data to $url');
      final response = await http.post(url).timeout(timeoutDuration);
      if (response.statusCode == 200) {
        print('Location data updated successfully');
      } else {
        print('Failed to update location data: ${response.statusCode} - ${response.body}');
        throw Exception('Failed to update location data');
      }
    } catch (e) {
      print('Error updating location data: $e');
      throw Exception('Failed to update location data');
    }
  }

  // Method to delete a record by ID
  static Future<void> deleteRecord(int id) async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/delete_record');
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'id': id}),
      ).timeout(timeoutDuration);
      if (response.statusCode == 200) {
        print('Record deleted successfully');
      } else {
        print('Failed to delete record: ${response.statusCode} - ${response.body}');
        throw Exception('Failed to delete record');
      }
    } catch (e) {
      print('Error deleting record: $e');
      throw Exception('Failed to delete record');
    }
  }

  // Method to delete user data
  static Future<void> deleteUserData(String username) async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/clear_all_data');
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'username': username}),
      ).timeout(timeoutDuration);
      if (response.statusCode == 200) {
        print('User data deleted successfully');
      } else {
        print('Failed to delete user data: ${response.statusCode} - ${response.body}');
        throw Exception('Failed to delete user data');
      }
    } catch (e) {
      print('Error deleting user data: $e');
      throw Exception('Failed to delete user data');
    }
  }
  //查询
  // 修改 queryDatabase 方法，接受 queryType
  static Future<List<Map<String, dynamic>>> bigstockqueryDatabase(String input, String queryType) async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/bigstockquery');
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'input': input,
          'queryType': queryType,  // 传递 queryType
        }),
      ).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      } else {
        print('查询失败: ${response.statusCode} - ${response.body}');
        throw Exception('查询失败: ${response.body}');
      }
    } catch (e) {
      print('Error during bigstockquery: $e');
      throw Exception('查询失败');
    }
  }
  //出库
  // 修改 checkoutItem 方法，接受 3 个参数
  static Future<void> checkoutItem(String input, String cantidad, String queryType) async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/checkout');
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'input': input,
          'cantidad': cantidad,
          'queryType': queryType,  // 传递 queryType
        }),
      ).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        print('出库成功');
      } else {
        print('出库失败: ${response.statusCode} - ${response.body}');
        throw Exception('出库失败: ${response.body}');
      }
    } catch (e) {
      print('Error during checkout: $e');
      throw Exception('出库失败');
    }
  }
  //同步商品名称
  static Future<void> syncData() async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/sync');
    try {
      final response = await http.post(url).timeout(timeoutDuration);
      if (response.statusCode == 200) {
        print('数据同步完成');
      } else {
        print('数据同步失败: ${response.statusCode} - ${response.body}');
        throw Exception('数据同步失败');
      }
    } catch (e) {
      print('Error during data sync: $e');
      throw Exception('数据同步失败');
    }
  }
  // 添加 checkinItem 方法
  static Future<List<Map<String, dynamic>>> checkinItem(
    String input,
    String weizhi,
    String cantidad,
    String queryType, {
    String? location_type,
    String? weizhi_1,
    String? weizhi_2,
  }) async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/checkin');
    try {
      final Map<String, dynamic> requestBody = {
        'input': input,
        'weizhi': weizhi,
        'cantidad': cantidad,
        'queryType': queryType,  // 传递 queryType
      };

      // 添加可选参数
      if (location_type != null) {
        requestBody['location_type'] = location_type;
      }
      if (weizhi_1 != null) {
        requestBody['weizhi_1'] = weizhi_1;
      }
      if (weizhi_2 != null) {
        requestBody['weizhi_2'] = weizhi_2;
      }

      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(requestBody),
      ).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        print('入库成功');
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      } else {
        print('入库失败: ${response.statusCode} - ${response.body}');
        throw Exception('入库失败: ${response.body}');
      }
    } catch (e) {
      print('Error during checkin: $e');
      throw Exception('入库失败');
    }
  }

  // Method to sync special prices from stocktaking to articulo table
  static Future<Map<String, dynamic>> syncSpecialPrices() async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/sync_special_prices');
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
      ).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to sync special prices: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Error syncing special prices: $e');
    }
  }

  // 从服务器获取商品数据
  static Future<Map<String, dynamic>> fetchArticulos({
    required Function(double) onProgress,
  }) async {
    try {
      print('Starting fetchArticulos...');
      final baseUrl = await getBaseUrl();
      if (baseUrl == null || baseUrl.isEmpty) {
        return {
          'success': false,
          'error': '无法获取服务器地址，请检查网络设置',
          'data': null,
        };
      }
      print('Base URL: $baseUrl');
      
      // 1. 获取总记录数
      final countUrl = Uri.parse('$baseUrl/articulos/count');
      print('Requesting count from: $countUrl');
      
      final countResponse = await http.get(
        countUrl,
        headers: {'Content-Type': 'application/json'},
      ).timeout(timeoutDuration);

      print('Count response status: ${countResponse.statusCode}');
      print('Count response body: ${countResponse.body}');

      if (countResponse.statusCode != 200) {
        return {
          'success': false,
          'error': '获取商品总数失败: ${countResponse.statusCode}, body: ${countResponse.body}',
          'data': null,
        };
      }

      final countData = json.decode(countResponse.body);
      if (!countData.containsKey('count')) {
        return {
          'success': false,
          'error': '服务器返回数据格式错误: 缺少count字段',
          'data': null,
        };
      }

      final totalCount = countData['count'] as int;
      print('Total count: $totalCount');
      
      if (totalCount == 0) {
        return {
          'success': true,
          'error': null,
          'data': [],
        };
      }

      final batchSize = 100;
      final totalBatches = (totalCount / batchSize).ceil();
      List<Map<String, dynamic>> allArticulos = [];

      // 2. 分批获取数据
      for (int i = 0; i < totalBatches; i++) {
        final offset = i * batchSize;
        final batchUrl = Uri.parse('$baseUrl/articulos/batch?offset=$offset&limit=$batchSize');
        print('Requesting batch $i from: $batchUrl');
        
        final response = await http.get(
          batchUrl,
          headers: {'Content-Type': 'application/json'},
        ).timeout(timeoutDuration);

        print('Batch $i response status: ${response.statusCode}');
        if (response.statusCode != 200) {
          print('Batch $i response body: ${response.body}');
          return {
            'success': false,
            'error': '获取商品数据失败 (批次 ${i + 1}/$totalBatches): ${response.statusCode}, body: ${response.body}',
            'data': null,
          };
        }

        final List<dynamic> batchData = json.decode(response.body);
        print('Received ${batchData.length} records in batch $i');
        
        // 验证数据格式
        if (batchData.isEmpty) {
          print('Warning: Batch $i is empty');
          continue;
        }

        try {
          final formattedBatch = batchData.map((item) {
            if (item is! Map<String, dynamic>) {
              throw FormatException('Invalid item format');
            }
            return {
              'ArticuloID': item['ArticuloID']?.toString() ?? '',
              'CodigoBarra': item['CodigoBarra']?.toString() ?? '',
              'PrecioDetalle': double.tryParse(item['PrecioDetalle']?.toString() ?? '0') ?? 0.0,
              'PrecioSocio': double.tryParse(item['PrecioSocio']?.toString() ?? '0') ?? 0.0,
              'PrecioMayor': double.tryParse(item['PrecioMayor']?.toString() ?? '0') ?? 0.0,
              'NombreES': item['NombreES']?.toString() ?? '',
              'Descuento': double.tryParse(item['Descuento']?.toString() ?? '0') ?? 0.0,
            };
          }).toList();
          
          allArticulos.addAll(formattedBatch);
        } catch (e) {
          print('Error processing batch $i: $e');
          return {
            'success': false,
            'error': '处理商品数据失败 (批次 ${i + 1}/$totalBatches): $e',
            'data': null,
          };
        }

        // 更新进度
        onProgress((i + 1) / totalBatches);
      }

      print('Successfully fetched all data. Total records: ${allArticulos.length}');
      return {
        'success': true,
        'error': null,
        'data': allArticulos,
      };
    } catch (e, stackTrace) {
      print('Error in fetchArticulos: $e');
      print('Stack trace: $stackTrace');
      if (e is TimeoutException) {
        return {
          'success': false,
          'error': '连接服务器超时，请检查网络连接',
          'data': null,
        };
      } else if (e is SocketException) {
        return {
          'success': false,
          'error': '无法连接到服务器，请检查网络连接',
          'data': null,
        };
      }
      return {
        'success': false,
        'error': '同步失败: $e',
        'data': null,
      };
    }
  }

  // 获取所有商品数据
  static Future<List<Map<String, dynamic>>> getProducts() async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/products');
    
    try {
      final response = await http.get(url).timeout(timeoutDuration);
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data);
      } else {
        print('Error fetching products: ${response.statusCode} - ${response.body}');
        throw Exception('Failed to fetch products');
      }
    } catch (e) {
      print('Error fetching products: $e');
      throw Exception('Failed to fetch products: $e');
    }
  }

  // 获取商品总数
  static Future<int> getArticulosCount() async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/articulos/count');
    try {
      final response = await http.get(
        url,
        headers: {'Content-Type': 'application/json'},
      ).timeout(timeoutDuration);
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['count'] ?? 0;
      } else {
        print('Error getting articulos count: ${response.statusCode} - ${response.body}');
        return 0;
      }
    } catch (e) {
      print('Error getting articulos count: $e');
      return 0;
    }
  }

  // 分批获取商品数据
  static Future<List<Map<String, dynamic>>> getArticulosBatch({
    required int offset,
    required int limit,
  }) async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/articulos/batch?offset=$offset&limit=$limit');
    try {
      final response = await http.get(
        url,
        headers: {'Content-Type': 'application/json'},
      ).timeout(timeoutDuration);
      
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data);
      } else {
        print('Error getting articulos batch: ${response.statusCode} - ${response.body}');
        return [];
      }
    } catch (e) {
      print('Error getting articulos batch: $e');
      return [];
    }
  }

  // 获取更新的商品数据
  static Future<List<Map<String, dynamic>>> getUpdatedArticulos(String lastSyncTime) async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/articulos/updated?since=$lastSyncTime');
    try {
      final response = await http.get(
        url,
        headers: {'Content-Type': 'application/json'},
      ).timeout(timeoutDuration);
      
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data);
      } else {
        print('Error getting updated articulos: ${response.statusCode} - ${response.body}');
        return [];
      }
    } catch (e) {
      print('Error getting updated articulos: $e');
      return [];
    }
  }

  // 检查数据完整性
  static Future<Map<String, dynamic>> checkDataIntegrity() async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/data/integrity');
    try {
      final response = await http.get(
        url,
        headers: {'Content-Type': 'application/json'},
      ).timeout(timeoutDuration);
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        print('Error checking data integrity: ${response.statusCode} - ${response.body}');
        return {'success': false, 'message': 'Server error'};
      }
    } catch (e) {
      print('Error checking data integrity: $e');
      return {'success': false, 'message': e.toString()};
    }
  }

  // 通过条码获取商品ID
  static Future<Map<String, dynamic>> getArticleIdByBarcode(String barcode) async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/articulos/barcode/$barcode');
    try {
      final response = await http.get(
        url,
        headers: {'Content-Type': 'application/json'},
      ).timeout(timeoutDuration);
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        print('Error getting article ID by barcode: ${response.statusCode} - ${response.body}');
        return {};
      }
    } catch (e) {
      print('Error getting article ID by barcode: $e');
      return {};
    }
  }

  // 通过商品ID获取条码
  static Future<Map<String, dynamic>> getBarcodeByArticleId(String articleId) async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/articulos/article/$articleId');
    try {
      final response = await http.get(
        url,
        headers: {'Content-Type': 'application/json'},
      ).timeout(timeoutDuration);
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        print('Error getting barcode by article ID: ${response.statusCode} - ${response.body}');
        return {};
      }
    } catch (e) {
      print('Error getting barcode by article ID: $e');
      return {};
    }
  }

  // 更新盘点数量
  static Future<void> updateStocktakingQuantity(
    String warehouse,
    String barcode,
    int quantity,
    String username,
    int inUseStatus, {
    String? articleId,
    String? updateMode,
    bool? forceInsert,
    bool? allowUnknown,
    int? recordId,
  }) async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/stocktaking/update');
    try {
      final Map<String, dynamic> payload = {
        'warehouse': warehouse,
        'barcode': barcode,
        'quantity': quantity,
        'username': username,
        // 移除 inUse 字段，因为数据库表中不存在此字段
      };

      if (articleId != null) {
        payload['articleId'] = articleId;
      }
      if (updateMode != null) {
        payload['updateMode'] = updateMode;
      }
      if (forceInsert != null) {
        payload['forceInsert'] = forceInsert;
      }
      if (allowUnknown != null) {
        payload['allowUnknown'] = allowUnknown;
      }
      if (recordId != null) {
        payload['recordId'] = recordId;
      }
      
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(payload),
      ).timeout(timeoutDuration);
      
      if (response.statusCode != 200) {
        print('Error updating stocktaking quantity: ${response.statusCode} - ${response.body}');
        throw Exception('Failed to update stocktaking quantity');
      }
    } catch (e) {
      print('Error updating stocktaking quantity: $e');
      throw Exception('Failed to update stocktaking quantity: $e');
    }
  }

  // 上传单个盘点记录
  static Future<void> uploadSingleStocktaking(Map<String, dynamic> data) async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/stocktaking/single');
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(data),
      ).timeout(timeoutDuration);
      
      if (response.statusCode != 200) {
        print('Error uploading single stocktaking: ${response.statusCode} - ${response.body}');
        throw Exception('Failed to upload single stocktaking');
      }
    } catch (e) {
      print('Error uploading single stocktaking: $e');
      throw Exception('Failed to upload single stocktaking: $e');
    }
  }

  // 出库操作
  static Future<List<Map<String, dynamic>>> checkout(
    String input,
    String cantidad,
    String queryType, {
    String? location_type,
  }) async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/checkout');
    try {
      final Map<String, dynamic> requestBody = {
        'input': input,
        'cantidad': cantidad,
        'queryType': queryType,
      };

      // 添加可选参数
      if (location_type != null) {
        requestBody['location_type'] = location_type;
      }

      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(requestBody),
      ).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data);
      } else {
        print('Error during checkout: ${response.statusCode} - ${response.body}');
        throw Exception('Checkout failed: ${response.body}');
      }
    } catch (e) {
      print('Error during checkout: $e');
      throw Exception('Checkout failed: $e');
    }
  }

  // 获取商品信息
  static Future<List<Map<String, dynamic>>> getArticuloInfo(
    String input,
    String queryField
  ) async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/articulos/info');
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'input': input,
          'queryField': queryField,
        }),
      ).timeout(timeoutDuration);
      
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data);
      } else {
        print('Error getting articulo info: ${response.statusCode} - ${response.body}');
        return [];
      }
    } catch (e) {
      print('Error getting articulo info: $e');
      return [];
    }
  }

  // 插入到新库存
  static Future<bool> insertToNewStock(String articuloID, String codigoBarra) async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/stock/insert');
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'articuloID': articuloID,
          'codigoBarra': codigoBarra,
        }),
      ).timeout(timeoutDuration);
      
      return response.statusCode == 200;
    } catch (e) {
      print('Error inserting to new stock: $e');
      return false;
    }
  }

  // 重新计算总箱数
  static Future<bool> recalculateTotalCaja() async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/stock/recalculate');
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
      ).timeout(timeoutDuration);
      
      return response.statusCode == 200;
    } catch (e) {
      print('Error recalculating total caja: $e');
      return false;
    }
  }

  // 更新位置字段
  static Future<Map<String, dynamic>> updateLocationField(
    String input,
    String field,
    String value
  ) async {
    final baseUrl = await getBaseUrl();
    final url = Uri.parse('$baseUrl/location/update');
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'input': input,
          'field': field,
          'value': value,
        }),
      ).timeout(timeoutDuration);
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        print('Error updating location field: ${response.statusCode} - ${response.body}');
        return {};
      }
    } catch (e) {
      print('Error updating location field: $e');
      return {};
    }
  }
}
