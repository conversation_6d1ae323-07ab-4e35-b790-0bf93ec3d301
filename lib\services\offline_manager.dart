import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:http/http.dart' as http;
import 'package:mistoer/services/scan_api_service.dart';
import 'package:mistoer/services/database_service.dart';

class OfflineManager {
  OfflineManager._();
  static final OfflineManager instance = OfflineManager._();

  // 是否手动离线（优先级最高）
  bool manualOffline = false;

  // 在线状态（集中管理），页面可监听
  final ValueNotifier<bool> isOnline = ValueNotifier<bool>(false);

  StreamSubscription<List<ConnectivityResult>>? _connSub;
  Timer? _pingTimer;

  Future<void> start() async {
    await _subscribeConnectivity();
    _startPingTimer();
    await _pingAndUpdate();
  }

  Future<void> stop() async {
    await _connSub?.cancel();
    _connSub = null;
    _pingTimer?.cancel();
    _pingTimer = null;
  }

  // 开启/关闭手动离线。返回非空字符串表示提示信息（如需下载商品）
  Future<String?> setManualOffline(bool enable) async {
    manualOffline = enable;
    if (enable) {
      // 进入手动离线立即置离线
      if (isOnline.value) isOnline.value = false;
      // 检查本地商品是否已存在
      try {
        final cnt = await DatabaseService.getArticulosCount();
        if (cnt == 0) {
          return '离线模式：本地未发现商品数据，请先下载/导入商品后再离线使用';
        }
      } catch (_) {}
      return null;
    } else {
      // 退出手动离线，立即重新检测
      await _pingAndUpdate();
      return null;
    }
  }

  // 内部：订阅系统网络变化
  Future<void> _subscribeConnectivity() async {
    await _connSub?.cancel();
    _connSub = Connectivity().onConnectivityChanged.listen((List<ConnectivityResult> results) async {
      if (manualOffline) return; // 手动离线不改状态
      final hasNetwork = results.any((r) => r != ConnectivityResult.none);
      if (!hasNetwork) {
        if (isOnline.value) isOnline.value = false;
        return;
      }
      await _pingAndUpdate();
    });
  }

  // 内部：定时 ping
  void _startPingTimer() {
    _pingTimer?.cancel();
    _pingTimer = Timer.periodic(const Duration(seconds: 6), (_) async {
      if (manualOffline) return;
      await _pingAndUpdate();
    });
  }

  // 内部：请求 /ping 并更新在线状态
  Future<void> _pingAndUpdate() async {
    try {
      final baseUrl = await ScanApiService.getBaseUrl();
      final url = Uri.parse('$baseUrl/ping');
      final res = await http.get(url).timeout(const Duration(seconds: 3));
      final online = res.statusCode == 200;
      if (isOnline.value != online) isOnline.value = online;
    } catch (_) {
      if (isOnline.value) isOnline.value = false;
    }
  }
} 