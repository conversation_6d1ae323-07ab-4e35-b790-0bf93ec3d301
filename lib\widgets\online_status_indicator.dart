import 'package:flutter/material.dart';
import 'package:mistoer/services/offline_manager.dart';

class OnlineStatusIndicator extends StatelessWidget {
  final bool showText;
  final double iconSize;
  final TextStyle? textStyle;

  const OnlineStatusIndicator({
    Key? key,
    this.showText = false,
    this.iconSize = 20.0,
    this.textStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: OfflineManager.instance.isOnline,
      builder: (context, isOnline, child) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                isOnline ? Icons.cloud_done : Icons.cloud_off,
                color: isOnline ? Colors.green : Colors.red,
                size: iconSize,
              ),
              if (showText) ...[
                SizedBox(width: 4),
                Text(
                  isOnline ? '在线' : '离线',
                  style: textStyle ?? TextStyle(
                    color: isOnline ? Colors.green : Colors.red,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }
}