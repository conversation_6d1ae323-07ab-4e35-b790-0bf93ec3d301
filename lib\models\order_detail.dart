class OrderDetail {
  final int id;
  final String pedidoKey;
  final String codigo;
  final String bianhao;
  final int zongshuliang;
  final int baozhuangshu;
  int scan;
  final int finish;
  final String weizhi;
  final String name_ce;
  final String artId;
  final String hash;

  OrderDetail({
    required this.id,
    required this.pedidoKey,
    required this.codigo,
    required this.bianhao,
    required this.zongshuliang,
    required this.baozhuangshu,
    this.scan = 0,
    this.finish = 0,
    this.weizhi = '',
    this.name_ce = '',
    this.artId = '',
    this.hash = '',
  });

  factory OrderDetail.fromJson(Map<String, dynamic> json) {
    return OrderDetail(
      id: json['id'] as int,
      pedidoKey: json['pedidoKey'] as String? ?? '',
      codigo: json['codigo'] as String? ?? '',
      bianhao: json['bianhao'] as String? ?? '',
      zongshuliang: _parseStringToInt(json['zongshuliang']),
      baozhuangshu: _parseStringToInt(json['baozhuangshu']),
      scan: _parseStringToInt(json['scan']),
      finish: _parseStringToInt(json['finish']),
      weizhi: json['weizhi'] as String? ?? '',
      name_ce: json['name_ce'] as String? ?? '',
      artId: json['artId'] as String? ?? '',
      hash: json['hash'] as String? ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'pedidoKey': pedidoKey,
      'codigo': codigo,
      'bianhao': bianhao,
      'zongshuliang': zongshuliang,
      'baozhuangshu': baozhuangshu,
      'scan': scan,
      'finish': finish,
      'weizhi': weizhi,
      'name_ce': name_ce,
      'artId': artId,
      'hash': hash,
    };
  }

  OrderDetail copyWith({
    int? id,
    String? pedidoKey,
    String? codigo,
    String? bianhao,
    int? zongshuliang,
    int? baozhuangshu,
    int? scan,
    int? finish,
    String? weizhi,
    String? name_ce,
    String? artId,
    String? hash,
  }) {
    return OrderDetail(
      id: id ?? this.id,
      pedidoKey: pedidoKey ?? this.pedidoKey,
      codigo: codigo ?? this.codigo,
      bianhao: bianhao ?? this.bianhao,
      zongshuliang: zongshuliang ?? this.zongshuliang,
      baozhuangshu: baozhuangshu ?? this.baozhuangshu,
      scan: scan ?? this.scan,
      finish: finish ?? this.finish,
      weizhi: weizhi ?? this.weizhi,
      name_ce: name_ce ?? this.name_ce,
      artId: artId ?? this.artId,
      hash: hash ?? this.hash,
    );
  }

  static int _parseStringToInt(dynamic value) {
    if (value is int) {
      return value;
    } else if (value is String) {
      return int.tryParse(value.split('.')[0]) ?? 0;
    } else {
      return 0;
    }
  }
}
